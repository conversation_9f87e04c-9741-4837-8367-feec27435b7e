<script setup lang="ts">
interface DetailDialogProps {
  [key: string]: any
  dialogData: DialogData[]
  column: number
}

interface DialogData {
  label: string
  value: string
  type: 'img' | 'text' | 'slot' | 'imgArr'
  slotName?: string
}

const props = defineProps<DetailDialogProps>()
</script>

<template>
  <el-descriptions :column="column ?? 1">
    <el-descriptions-item v-for="(value, index) in dialogData" :key="index" :label="value.label">
      <template v-if="value.type === 'img'">
        <el-image :src="value.value" />
      </template>
      <template v-else-if="value.type === 'imgArr'">
        <el-image v-for="(item, imgIndex) in value.value" :key="imgIndex" :src="item" />
      </template>
      <template v-else-if="value.type === 'text'">
        {{ value.value }}
      </template>
      <template v-else-if="value.type ==='slot'">
        <slot :name="value.slotName" :value="value.value" />
      </template>
    </el-descriptions-item>
  </el-descriptions>
</template>

<style scoped lang="scss">

</style>
