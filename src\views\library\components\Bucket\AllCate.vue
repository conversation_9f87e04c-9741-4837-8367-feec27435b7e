<template>
  <edit-dialog ref="editDialogRef" v-model:dialog-show="dialogShow" v-bind="dialogConfig" @sure="sure">
    <div class="all-cate-content">
      <el-tree ref="treeRef" v-loading="loading" :data="treeData" :props="treeProps" node-key="id" :default-expanded-keys="[0]"
        :show-checkbox="dialogConfig.showCheckbox" :check-strictly="true" @check-change="handleCheckChange">
        <template #default="{ node, data }">
          <div class="node-content flex-between w100">
            <span class="node-title" v-html="data.title"></span>
            <div v-if="props.type === 'cate'" class="node-action flex">
              <template v-if="data.id !== 0">
                <span class="node-action-item flex-c-center" title="编辑" @click.stop="handleEdit(data)">
                  <i class="iconfont icon-bianji" />
                </span>
                <span class="node-action-item flex-c-center" title="删除" @click.stop="handleDelete(data)"> 
                  <i class="iconfont icon-shanchu1" />
                </span>
                <span class="node-action-item flex-c-center" title="进入文件夹" @click.stop="handleEnter(data)">
                  <i class="iconfont icon-wenjianjia" />
                </span>
              </template>
            </div>
          </div>
        </template>
      </el-tree>
    </div>
    <update-cate ref="updateCateRef" @success="handleSuccess" />
  </edit-dialog>
</template>

<script setup lang="ts">
import { ref, watch, defineAsyncComponent } from 'vue'
import { useDialog } from '@/hooks/useDialog'
import { getAllCateApi } from '@/api/bucket'
import { ElMessage } from 'element-plus'

const { EditDialog, editDialogRef, dialogShow, openDialog, closeDialog } = useDialog()

const UpdateCate = defineAsyncComponent(() => import('./UpdateCate.vue'))
const updateCateRef = ref()

const emit = defineEmits(['enterFolder', 'deleteCate', 'editCate', 'transfer', 'selectCate', 'refresh'])

const props = defineProps({
  type: {
    type: String,  // cate: 全部分类 transfer: 转移  select: 选择
    default: 'cate'
  }
})

const treeRef = ref()
const treeData = ref([
  { id: 0, title: '全部分类', children: [] }
])
const treeProps = ref({
  children: 'children',
  label: 'title'
})

const dialogConfig = ref({
  title: '全部分类',
  width: '600px',
  customForm: true,
  showBottomBtn: false,
  sureText: '确定',
  showCheckbox: false
})

// 获取全部分类
const loading = ref(false)
const getAllCate = () => {
  loading.value = true
  getAllCateApi().then((res: any) => {
    treeData.value[0].children = res
  }).finally(() => {
    loading.value = false
  })
}

const handleEdit = (data: any) => {
  console.log(data, 'data')
  updateCateRef.value.openDialog({ id: data.id, title: data.title })
}

const handleSuccess = () => {
  getAllCate()
  emit('refresh')
}

const handleDelete = (data: any) => {
  console.log(data, 'data')
  emit('deleteCate', { isFile: 1, ...data})
}

const handleEnter = (data: any) => {
  // console.log(data, 'data')
  // 获取根节点的最外层节点
  const rootNode = treeRef.value.getNode(data)
  // console.log(rootNode, 'rootNode')
  let allParent = findAllParent(rootNode)
  allParent.push(rootNode)
  // console.log(allParent, 'allParent')
  const folderArr = allParent.map(({ data }: any) => ({ name: data.title, id: data.id }))
  // console.log(folderArr, 'folderArr')
  emit('enterFolder', folderArr)
}

// 找到所有parent并且包含当前的节点 除了level = 0
const findAllParent: any = (node: any) => {
  const parent = node.parent
  if (parent && parent.level !== 0) {
    return [...findAllParent(parent), parent]
  }
  return []
}

// 监听treeRef的复选框选择
const handleCheckChange = (data: any, checked: boolean) => {
  console.log(data, checked, 'handleCheckChange')
  // 转移 单选
  if (checked && props.type === 'transfer') {
    treeRef.value.setCheckedKeys([data.id])
  }
}

const sure = () => {
  console.log('sure')
  if (props.type === 'transfer') {
    if (!treeData.value.length) return ElMessage.warning('请先创建分类')
    else if (!treeRef.value.getCheckedNodes().length) return ElMessage.warning('请先选择要转移的分类')
    const checkedNodes = treeRef.value.getCheckedNodes()
    console.log(checkedNodes[0], 'checkedNodes')
    emit('transfer', checkedNodes[0])
  } else if (props.type === 'select') {
    if (!treeData.value.length) return ElMessage.warning('请先创建分类')
    else if (!treeRef.value.getCheckedNodes().length) return ElMessage.warning('请先选择分类')
    const checkedNodes = treeRef.value.getCheckedNodes()
    console.log(checkedNodes, 'checkedNodes')
    emit('selectCate', checkedNodes)
  }
}

watch(dialogShow, (newVal: boolean) => {
  if (newVal) {
    if (props.type === 'cate') dialogConfig.value.title = '全部分类'
    else if (props.type === 'transfer') {
      dialogConfig.value.title = '转移到'
      dialogConfig.value.showBottomBtn = true
      dialogConfig.value.sureText = '转移'
      dialogConfig.value.showCheckbox = true
    } else if (props.type === 'select') {
      dialogConfig.value.title = '选择分类'
      dialogConfig.value.showBottomBtn = true
      dialogConfig.value.sureText = '确定'
      dialogConfig.value.showCheckbox = true
    }
    getAllCate()
  }
})

defineExpose({ openDialog, closeDialog })
</script>

<style scoped lang="scss">
.all-cate-content {
  height: 50vh;
  overflow: auto;
  border: 1px solid #E3E5EC;
  :deep(.el-tree) {
    .el-tree-node {
      .el-tree-node__content {
        height: auto;
        border-bottom: 1px solid #E3E5EC;
      }
    }
  }
  .node-content {
    padding: 10px 0;
    .node-title {
      font-size: 14px;
    }
    .node-action {
      cursor: pointer;
      .node-action-item {
        width: 30px;
        height: 30px;
        .iconfont {
          font-size: 16px;
          color: #999FBE;
          &.icon-wenjianjia {
            color: var(--jzt-color-main);
          }
        }
      }
    }
  }
}
</style>
