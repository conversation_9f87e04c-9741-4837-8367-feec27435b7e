<template>
  <div id="preview">
    <img v-if="preview.type === 1" :src="preview.url" alt="">
    <video v-if="preview.type === 2" :src="preview.url" controls autoplay></video>
    <el-icon :size="60" :color="'#fff'" @click="closePreview">
      <Close />
    </el-icon>
  </div>
</template>

<script setup lang="ts">
import { Close } from "@element-plus/icons-vue";

const preview = defineProps({
  type: {
    type: Number,
    default: 1 // 1是图片，2是视频
  },
  url: {
    type: String,
    default: ""
  }
});

const emit = defineEmits(['closePreview']);

const closePreview = () => {
  emit("closePreview")
}
</script>

<style lang="scss">
#preview {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.2);
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;

  img,
  video {
    max-width: 80%;
    max-height: 90%;
  }

  .el-icon {
    position: absolute;
    top: 44px;
    right: 30px;
    cursor: pointer;
  }
}
</style>
