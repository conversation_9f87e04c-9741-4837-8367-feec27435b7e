<script setup lang="ts">
import {nextTick, ref} from "vue";

const props = defineProps({
  item: {
    type: Object,
    default: () => {
      return {
        title: '',
        content: ''
      }
    }
  }
})

const contentRef = ref()
const boxRef = ref()
const showExpendBtn = ref(false)
nextTick(() => {
  setTimeout(() => {
    const ele = contentRef.value
    const box = boxRef.value
    if (!ele || !box) return false
    //   ele的高度
    const eleHeight = ele.offsetHeight
    //   box的高度
    const boxHeight = box.offsetHeight
    showExpendBtn.value = eleHeight > boxHeight
  }, 500)
})

const isExpend = ref(false)
const maxHeight = ref(500)
const toggleExpend = () => {
  isExpend.value =!isExpend.value
  const ele = contentRef.value
  const box = boxRef.value
  if (!ele ||!box) return false
  //   ele的高度
  const eleHeight = ele.offsetHeight
  if (isExpend.value) {
    box.style.maxHeight = eleHeight + 70 + 'px'
  } else {
    box.style.maxHeight = maxHeight.value + 'px'
  }
}
</script>

<template>
  <div class="content-box" ref="boxRef">
    <div class="log-time">{{ item.title }}</div>
    <div class="log-content" v-if="item.content" ref="contentRef">
      <div :class="{'is-expend': showExpendBtn}" v-html="item.content"></div>
      <div :class="['log-mask', {'no-bg': isExpend}]" v-if="showExpendBtn">
        <el-button class="expend" @click="toggleExpend">
          {{ isExpend ? '收起' : '展开' }}
          <i :class="['iconfont', isExpend ? 'icon-tabshouqi' : 'icon-tabxiala']"/>
        </el-button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.content-box {
  border-bottom: 1px solid #E3E5EC;
  background: #fff;
  overflow: hidden;
  position: relative;
  transition: .5s;
  margin-bottom: 0;
  max-height: 500px;
  padding: 20px 30px;
  box-sizing: border-box;
  &:last-child {
    padding-bottom: 70px;
  }
  .log-time {
    font-size: 20px;
    font-family: PingFang SC;
    font-weight: 800;
    color: #1C2B4B;
    margin-bottom: 20px;
  }
  .log-content {
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 500;
    color: #1C2B4B;
    line-height: 28px;
    width: 100%;
    .is-expend{
      padding-bottom: 70px;
    }
    :deep(img) {
      max-width: 100%;
    }
    :deep(ul) {
      padding-left: 30px;
    }
    .log-mask {
      width: 100%;
      height: 130px;
      position: absolute;
      bottom: 0;
      left: 0;
      padding-left: 40px;
      padding-bottom: 20px;
      background: linear-gradient(0deg, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%);
      display: flex;
      justify-content: start;
      align-items: end;
      &.no-bg {
        background: none;
      }
      .expend {
        border-color: #A0B6FF;
        color: #0048FF;
        width: 62px;
        height: 30px;
        font-size: 12px;
        .iconfont {
          font-size: 8px;
          margin-left: 3px;
        }
      }
    }
  }
}
</style>