<script setup lang="ts">
import {useGzhInfo} from "@/hooks/useGzhInfo";
import {ref, watch} from "vue";
import DataTable from "@/components/Global/DataTable.vue";
import {getImageTextListApi} from "@/api/report/imgText";
import GzhName from "./components/GzhName.vue";

const emits = defineEmits(['changeNav']);

/*公众号数据*/
const {loading, gzhInfo, getGzhInfo} = useGzhInfo();
getGzhInfo();

/*获取列表*/
const tableConfig = ref({
  api: getImageTextListApi,
  params: {
    datedate: '',
    page: 1,
    limit: 10,
  },
  columns: [
    {
      label: '内容标题',
      prop: 'title',
      width: 250
    },
    {
      label: '时间',
      prop: 'ref_date',
      type: 'slot',
    },
    {
      label: '阅读次数',
      prop: 'ydcount',
    },
    {
      label: '分享次数',
      prop: 'fxcount',
    },
    {
      label: '阅读后关注人数',
      prop: 'ydren',
    },
    {
      label: '送达阅读率',
      prop: 'sdbl',
      type: 'slot',
      width: 150,
    },
    {
      label: '操作',
      prop: 'action',
      type: 'slot',
      width: 180,
      align: 'center',
    }
  ]
})
const tableRef = ref<any>(null);
const getList = () => {
  tableRef.value.refresh();
}
const detail = (row: any) => {
  emits('changeNav', 2, row);
}
const review = (row: any) => {
  emits('changeNav', 3, row);
}

/*筛选表单*/
const form = ref({
  datedate: [],
  page: 1,
  limit: 10,
})
watch(() => form.value.datedate, (val) => {
  if(val.length === 0) {
    tableConfig.value.params.datedate = val.join(' ~ ');
  }
})
</script>

<template>
  <div class="wrap h100">
    <div class="content-box">
      <gzh-name :show-list="false" />
      <div class="filter-box mt-20">
        <p class="p-title">数据分析</p>
        <el-form :model="form" class="px-20 mt-20">
          <el-form-item label="时间选择">
            <div style="width: 300px">
              <el-date-picker
                  v-model="form.datedate"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-M-D"
                  @change="getList"
              />
            </div>
            <div class="flex flex-1 tips">
              （默认为7天内的数据）
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div class="list mt-20">
        <data-table :api="tableConfig.api" :columns="tableConfig.columns" ref="tableRef" :show-multiple="false">
          <template #sdbl="{row}">
            <span>{{ row.sdbl }}%</span>
          </template>
          <template #action="{row}">
            <div class="table-action flex">
              <div class="item" @click="detail(row)">
                <i class="iconfont icon-xiangqing"></i> <span>详情</span>
              </div>
              <div v-if="+row.pl === 0" class="item" @click="review(row)">
                <i class="iconfont icon-tuwen"></i> <span>评论</span>
              </div>
            </div>
          </template>
        </data-table>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
@import "src/views/media/report/gzh/style/common";
</style>
