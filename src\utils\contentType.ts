import { contentResumeListApi } from '@/api/content';

// 默认列表配置
const defaultListTableConfig:any = {
  showIndex: false,
  allResult: true,
  columns: <any>[
    { label: '分类', prop: 'categories', type: 'slot', width: 200 },
    { label: '标题', prop: 'title', edit: true },
    { label: '添加时间', prop: 'created', width: 200, sortable: 'custom' },
    { label: '状态', prop: 'state', width: 100, align: 'center', type: 'state' },
    { label: '操作', prop: 'action', type: 'slot', align: 'center' }
  ],
  multipleSelection: [],
  params: <any>{
    catid: 0
  },
  maxNumber: 1,
  dragSort: true,
}

// 分类字典
export const cateTypeDic:any = {
  'content': { 
    name: '文章', 
    api: { list: 'Contentnew', type: 'Categories' }
  },
  'goods': { 
    name: '图文',
    api: { list: 'Goodsnew', type: 'GoodsType' }
  },
  'carousel': {
    name: '轮播',
    api: { list: 'Bannernew', type: 'BannerType' }
  },
  'notice': {
    name: '公告',
    api: { list: 'Notice', type: 'NoticeTypenew' }
  },
  'product': {
    name: '商品',
    api: { list: 'Shopnew', type: 'ShopsType' }
  },
  'activity': {
    name: '活动',
    api: { list: 'ActivityListlbnew', type: 'Activityfl' },
    listConfig: {
      cate: 'activityfl'
    }
  },
  'text': {
    name: '信息',
    api: { list: 'ContentList', type: 'ContentType' }
  },
  'recruitment': {
    name: '招聘',
    api: { list: 'Recruitnew', type: 'RecruitType' },
    listConfig: {
      cate: 'recruittype',
      createForm: [
        { label: '招聘人数', key: 'recruit_num', width: '100', default: '若干' },
        { label: '工作经验', key: 'experience', width: '100' },
        { label: '最低学历', key: 'education', width: '100' },
        { label: '薪资待遇', key: 'treatment', width: '100', default: '面议' }
      ],
      tableConfig: {
        
      },
      resumeTableConfig: {
        api: contentResumeListApi,
        showIndex: false,
        columns: <any>[
          { label: '简历信息', prop: 'content', type: 'slot' },
          { label: '提交时间', prop: 'create_time', width: 200, sortable: 'custom' },
          { label: '操作', prop: 'action', type: 'slot', align: 'center', width: 200 },
        ],
        multipleSelection: [],
      }
    }
  },
  'advertisement': {
    name: '广告',
    api: { list: 'Advertise', type: 'AdvertiseType' }
  },
  'image': {
    name: '图片',
    api: { list: 'Image', type: 'ImageType' }
  }
}

export const cateNavList:any = (type: string) => {
  let name = cateTypeDic[type] && cateTypeDic[type].name || '内容';
  let list:any = [
    { title: `${name}列表`, key: 'list' },
    { title: `${name}分类`, key: 'list-type' },
    { title: '添加', key: 'add', hidden: true },
    { title: '素材库批量添加', key: 'library-add', hidden: true, rule: ['goods', 'product', 'image'] },
    { title: '同步公众号', key: 'we-chat-sync', hidden: true, rule: ['content'] },
    { title: '简历列表', key:'resume-list', hidden: true, rule: ['recruitment'] }
  ]
  return list.filter((item:any) => {
    if (item.rule) return item.rule.includes(type)
    return true
  })
}

export const cateApiUrl:any = (type: string) => {
  return cateTypeDic[type] && cateTypeDic[type].api || 'Contentnew';
}

export const listConfigCreateForm:any = (type: string) => {
  let list:any = []
  if (cateTypeDic[type] && cateTypeDic[type].listConfig) {
    list = cateTypeDic[type].listConfig.createForm || []
  }
  return list
}

export const listConfigCate:any = (type: string) => {
  if (cateTypeDic[type] && cateTypeDic[type].listConfig) {
    return cateTypeDic[type].listConfig.cate || 'categories'
  }
  return 'categories'
}

export const resumeListConfig:any = (type: string) => {
  let obj: any = {}
  if (cateTypeDic[type] && cateTypeDic[type].listConfig) {
    obj = cateTypeDic[type].listConfig.resumeTableConfig || {}
    if (obj.api) obj.apiType = cateApiUrl(type).list
  }
  return obj
}