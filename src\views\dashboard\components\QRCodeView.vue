<template>
  <!--各端二维码-->
  <div class="content-box review-container">
    <div class="flex-between" style="height: 100%;">
      <div class="pc-box part05-box flex-column" style="flex: 1">
        <img src="@/assets/images/index/jzt.png" alt="" />
        <a class="flex index-btn" :href="'http://' + pcDomain" target="_blank" v-if="pcDomain">
          <span>域名访问</span>
          <img src="@/assets/images/index/right.png" alt="" />
        </a>
        <p v-else style="font-size: 14px; color: #a7abbb; margin-top: 20px">
          未绑定域名
        </p>
      </div>
      <div class="mut-box flex-wrap" style="max-width: 500px;">
        <template v-for="(item, index) in mutList" :key="index">
          <div :class="['item', 'part05-box', 'flex-column', {'no-qr': !item.qrCode}]" style="position: relative;">
            <img :src="require('@/assets/images/index/' + (item.qrCode ? item.icon_large : item.icon_large_w) + '.png')" alt="">
            <p class="i-name">{{ item.name }}</p>
            <template v-if="item.qrCode">
              <a class="flex index-btn">
                <img src="@/assets/images/index/qrcode.png" alt="">
                <span>已开通</span>
              </a>
              <div class="item-cover flex-column">
                <img :src="item.qrCode" alt="" class="qr-code">
                <p class="flex">
                  <img :src="require('@/assets/images/index/' + item.icon_small + '.png')" alt="">
                  <span>{{ item.name }}</span>
                </p>
              </div>
            </template>
            <template v-else>
              <router-link to="/applet/wechat" class="flex index-btn active">
                <span>点击开通</span>
                <img src="@/assets/images/index/right--.png" alt="">
              </router-link>
            </template>
          </div>
        </template>
      </div>
      <div class="more-box part05-box flex-column" v-if="false">
        <p class="more-title">更多内容<br />敬请期待</p>
        <img src="@/assets/images/index/nodata.png" alt="">
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted } from 'vue'
import useDashboard from '@/views/dashboard/hooks/useDashboard'

const { mutList, pcDomain, getSiteQRCode } = useDashboard()

onMounted(() => {
  getSiteQRCode()
})

</script>

<style lang="scss" scoped>

/* 各端二维码 */
.review-container {
  .part05-box {
    background: #ffffff;
    border: 1px solid #eff0f4;
    border-radius: 2px;
    cursor: pointer;
    transition: all ease-in-out 0.3s;
    &:hover {
      border-color: transparent;
      box-shadow: 0px 4px 7px 3px rgba(231, 231, 231, 0.4);
    }
  }
  .pc-box,
  .more-box {
    width: 235px;
    height: 100%;
  }
  .index-btn {
    width: 80px;
    height: 28px;
    background: #ffffff;
    border: 1px solid #e3e5ec;
    border-radius: 14px;
    font-size: 12px;
    /*font-family: PingFang SC;*/
    font-weight: 500;
    color: #7f8294;
    justify-content: center;
    span {
      margin-left: 4px;
    }
  }
  .pc-box {
    .pc-title {
      font-size: 14px;
      /*font-family: PingFang SC;*/
      font-weight: 500;
      color: #1c2b4b;
      margin-top: 15px;
    }
    .pc-domain {
      font-size: 14px;
      /*font-family: PingFang SC;*/
      font-weight: 400;
      color: #7f8294;
      margin-top: 3px;
      width: 80%;
      text-align: center;
    }
    .index-btn {
      width: 120px;
      height: 34px;
      background: var(--jzt-color-main);
      border-radius: 17px;
      margin-top: 44px;
      color: #ffffff;
      font-size: 14px;
      font-weight: bold;
      span {
        margin-left: 0;
        margin-right: 5px;
      }
    }
  }
  .mut-box {
    height: 100%;
    max-width: 600px !important;
    .item {
      width: calc(100% / 3 - 20px);
      height: calc((100% - 18px) / 2);
      margin-left: 16px;
      margin-bottom: 16px;
      position: relative;
      overflow: hidden;
      &.no-qr:before {
        content: url("@/assets/images/index/icon07.png");
        position: absolute;
        top: 0;
        right: 0;
      }
      .i-name {
        font-size: 14px;
        /*font-family: PingFang SC;*/
        font-weight: 500;
        color: #1c2b4b;
        margin-top: 14px;
      }
      .item-cover {
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        background-color: #ffffff;
        transition: all ease-in-out 0.3s;
        transform: translateY(100%);
        .qr-code {
          width: 105px;
          height: 105px;
          margin-bottom: 9px;
          object-fit: contain;
        }
        p {
          font-size: 14px;
          color: #1c2b4b;
          span {
            margin-left: 5px;
          }
        }
      }
      &:hover {
        .item-cover {
          transform: translateY(0%);
        }
      }
    }
    .index-btn {
      margin-top: 10px;
      &.active {
        width: 90px;
        border: 1px solid #a0b6ff;
        font-weight: bold;
        color: #2b5bfc;
        span {
          margin-left: 0;
          margin-right: 5px;
        }
      }
    }
  }
  .more-box {
    margin-left: 16px;
    .more-title {
      font-size: 14px;
      /*font-family: PingFang SC;*/
      font-weight: 500;
      color: #a7abbb;
      margin-bottom: 20px;
    }
  }
}
</style>