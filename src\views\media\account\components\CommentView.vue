<template>
  <edit-dialog ref="editDialogRef" v-model:dialog-show="dialogShow" v-bind="formConfig">
    <div v-loading="loading" class="comment-list">
      <div class="fans-list comment-list" v-if="commentList && commentList.length > 0">
        <template v-for="(item, index) in commentList">
          <div class="item-box">
            <div class="fans-item flex">
              <img
                src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20221025/bb671ea9abe6fe9c6d51a58563287708.jpeg"
                alt="" class="head">
              <div class="name">{{ item.content }}</div>
            </div>
            <div class="reply-list">
              <div class="fans-item flex"
                v-for="(items, indexs) in item.reply">
                <img
                  src="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/20221025/bb671ea9abe6fe9c6d51a58563287708.jpeg"
                  alt="" class="head">
                <div class="name">{{ items.content }}</div>
              </div>
            </div>
          </div>
        </template>
        <div class="more" v-if="hasMore && commentList.length > 0" @click="getCommentList">点击查看更多</div>
      </div>
      <div class="fans-list" v-else>
        <div class="more">暂无评论</div>
      </div>
    </div>
  </edit-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useDialog } from '@/hooks/useDialog'
import { getCommentListApi } from '@/api/media/account'

const { EditDialog, editDialogRef, dialogData, dialogShow, openDialog } = useDialog()

const props = defineProps({
  nowInfoId: {
    type: Number,
    default: 0
  }
})

const formConfig = ref({
  title: '评论列表',
  width: '600px',
  customForm: true,
  showBottomBtn: false
})

// 获取评论列表
const loading = ref(false)
const commentList:any = ref(null)
const listQ = ref({ page: 1, limit: 10, item_id: '' })
const hasMore = ref(false)
const getCommentList = async () => {
  loading.value = true
  try {
    const res:any = await getCommentListApi({ id: props.nowInfoId, ...listQ.value })
    console.log(res)
    const { list, cursor, has_more} = res
    if (commentList.value) {
      commentList.value = [...commentList.value, ...list]
    } else {
      commentList.value = list
    }
    listQ.value.page = cursor
    hasMore.value = has_more
  } catch (error) {
    console.log(error, '获取评论列表失败')
  } finally {
    loading.value = false
  }
}

watch(() => dialogShow.value, (val) => {
  if (val) {
    if (dialogData.value && dialogData.value.item_id) {
      listQ.value.item_id = dialogData.value.item_id
      listQ.value.page = 1
      commentList.value = null
      // getCommentList()
    }
  }
})

defineExpose({ openDialog })
</script>

<style scoped lang="scss">
@import '../styles/common.scss';
</style>