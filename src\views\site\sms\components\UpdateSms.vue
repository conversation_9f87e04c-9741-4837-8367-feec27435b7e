<script setup lang="ts">
import {computed, markRaw, ref} from "vue";
import {ElForm, ElMessage, FormInstance} from "element-plus";
import {addSmsApi, editSmsApi} from "@/api/siteManage";

interface Props {
  [key: string]: any,
  form: {
    [key: string]: any,
    ordering: number,
    parent_id: number,
    accessKeyId: string,
    accessKeySecret: string
  }
}
const props = withDefaults(defineProps<Props>(), {
  form: () => ({
    ordering: 0,
    parent_id: 1,
    accessKeyId: '',
    accessKeySecret: ''
  })
})

const emits = defineEmits(['update:form', 'closeDialog', 'refresh'])

const formRef = ref<InstanceType<typeof ElForm>>()
const rules = computed(() => {
  return {
    parent_id: [
      { required: true, message: '请选择模板类型', trigger: 'change' }
    ],
    accessKeyId: [
      { required: true, message: '请输入accessKeyId', trigger: 'change' }
    ],
    accessKeySecret: [
      { required: true, message: '请输入accessKeySecret', trigger: 'change' }
    ],
    signature: [
      { required: true, message: '请输入签名', trigger: 'change' }
    ],
    code: [
      { required: true, message: '请输入模板参数', trigger: 'change' }
    ]
  }
})
const loading = ref(false)

const formData = computed({
  get() {
    return props.form
  },
  set(val) {
    emits('update:form', val)
  }
})

const typeList = markRaw([{
  id: 1,
  title: '阿里云'
}])

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      loading.value = true
      try{
        if (formData.value.id) {
          await editSmsApi(formData.value)
        }else{
          await addSmsApi(formData.value)
        }
        ElMessage.success('操作成功')
        closeDialog()
        emits('refresh')
      }catch (e){
        console.log(e);
      }finally {
        loading.value = false
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}

const closeDialog = () => {
  emits('closeDialog')
}
</script>

<template>
<el-form :model="formData" label-width="125" ref="formRef" :rules="rules">
  <el-form-item label="排序" prop="sms-type-cate">
    <div class="flex-between w100">
      <div class="item-box">
        <el-input-number v-model="formData.ordering" placeholder="请输入排序" />
      </div>
      <div class="flex-between w100 item-box">
        <el-select v-model="formData.parent_id" placeholder="请选择模板类型">
          <el-option v-for="item in typeList" :key="item.id" :label="item.title" :value="item.id" />
        </el-select>
      </div>
    </div>
  </el-form-item>
  <el-form-item label="accessKeyId" prop="accessKeyId">
    <el-input v-model="formData.accessKeyId" placeholder="请输入accessKeyId" autocomplete="off" />
  </el-form-item>
  <el-form-item label="accessKeySecret" prop="accessKeySecret">
    <el-input v-model="formData.accessKeySecret" placeholder="请输入accessKeySecret" autocomplete="off" type="password" />
  </el-form-item>
  <el-form-item label="签名" prop="signature">
    <el-input v-model="formData.signature" placeholder="请输入签名" autocomplete="off" />
  </el-form-item>
  <el-form-item label="模板参数" prop="code">
    <el-input v-model="formData.code" placeholder="请输入模板参数" autocomplete="off" />
  </el-form-item>
  <el-form-item label-width="0">
    <div class="w100 flex-c-center">
      <el-button type="primary" @click="submitForm(formRef)" :loading="loading" class="footer-btn">保存</el-button>
      <el-button @click="closeDialog" class="footer-btn">取消</el-button>
    </div>
  </el-form-item>
</el-form>
</template>

<style scoped lang="scss">
.item-box + .item-box {
  margin-left: 20px;
}
.footer-btn{
  width: 110px;
  height: 34px;
}
</style>
