import {ref} from "vue";
import {getGzhInfoApi} from "@/api/report/gzh";

export const useGzhInfo = () => {
  const loading = ref(false);
  const gzhInfo: any = ref({});
  const getGzhInfo = async (callback?: (res: any) => void) => {
    console.log('getGzhInfo');
    loading.value = true;
    try{
      const res: any = await getGzhInfoApi();
      gzhInfo.value = res;
      callback && callback(res);
    }catch (e){
      console.log(e);
    }finally {
      loading.value = false;
    }
  };
  return {
    loading,
    gzhInfo,
    getGzhInfo
  }
}
