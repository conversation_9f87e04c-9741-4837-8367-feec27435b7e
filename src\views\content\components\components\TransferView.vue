<template>
  <edit-dialog ref="editDialogRef" v-model:dialog-show="dialogShow" :title="dialogTitle" :customForm="true" @sure="handleSure">
    <el-form ref="editFormRef" :model="formData" :rules="rules" label-width="auto" label-position="right">
      <el-form-item label="分类" prop="catid">
        <type-select class="flex-1" v-model:value="formData.catid" :apiType="apiType" :showAll="false" :showTop="false"></type-select>
      </el-form-item>
    </el-form>
  </edit-dialog>
</template>

<script setup lang="ts">
import { computed, ref, defineAsyncComponent, watch } from 'vue'
import { ElMessage } from 'element-plus';
import { useDialog } from '@/hooks/useDialog';
import { contentTransferApi } from '@/api/content'

const { EditDialog, editDialogRef, dialogShow, dialogData, openDialog } = useDialog()

const TypeSelect = defineAsyncComponent(() => import('./TypeSelect.vue'))

const props = defineProps({
  cateType: {
    type: String,
    default: 'content'
  },
  apiType: {
    type: String,
    default: ''
  },
  apiList: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['success'])

const defaultFormData = {id: '', catid: ''}

const dialogTitle = ref('选择要转移到的分类')

watch(() => dialogShow.value, (val) => {
  if (!val) return
  let info = dialogData.value
  if (!info) {
    formData.value = JSON.parse(JSON.stringify(defaultFormData))
  } else {
    formData.value.id = info
  }
})


const formData:any = ref(JSON.parse(JSON.stringify(defaultFormData)))

const rules = ref({
  catid: [
    { required: true, message: '请选择要移到的分类', trigger: 'blur' }
  ]
})

const editFormRef = ref()
const submitLoading = ref(false)
const handleSure = async () => {
  if (submitLoading.value) return ElMessage.warning('请勿重复提交')
  await editFormRef.value.validate()
  try {
    submitLoading.value = true
    let params = formData.value
    const res = await contentTransferApi(props.apiList, params)
    ElMessage.success('操作成功')
    emit('success', res)
    dialogShow.value = false
  } catch (error) {
    console.log(error)
  } finally {
    submitLoading.value = false
  }
}

defineExpose({ openDialog })
</script>

<style scoped lang="scss">

</style>