<template>
  <div class="overview-wrap h100 p20 flex-x">
    <!-- 左侧环形图 -->
    <div class="overview-left mr20">
      <Capacity />
    </div>
    <!-- 素材空间分布 -->
    <div class="overview-right content-box flex-1">
      <div class="overview-title">素材库空间分布</div>
      <table class="w100">
        <tr class="chart" v-for="(item, index) in tableData" :key="index">
          <td>
            <img :src="item.icon" alt="">
          </td>
          <td>
            <span class="right-name" v-html="item.name"></span>
          </td>
          <td style="width: 30%">
            <progress max="100" :value="getNumPercent(item.percent)"></progress>
          </td>
          <td>
            <span class="right-percentage">{{ item.name }}占比：<i>{{ item.percent }}</i></span>
          </td>
          <td>
            <span class="right-space">{{ item.name }}占用空间：<i>{{ item.space }}</i>/<b>{{ totalSpace }}</b></span>
          </td>
          <td>
            <span class="right-nums">{{ item.name }}数量：<i>{{ item.nums }}</i></span>
          </td>
        </tr>
      </table>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, defineAsyncComponent } from 'vue'
import { getBucketSpaceApi } from '@/api/bucket'

const Capacity = defineAsyncComponent(() => import('./Overview/Capacity.vue'))

const tableData = ref([
  {
    name: '图片',
    icon: require('@/assets/images/bucket/img.png'),
    percent: '0%',
    space: '0M',
    nums: '0张',
    percentKey: 'useimg_bfb',
    spaceKey: 'used_imgsize',
    numsKey: 'useimg_nums',
    unit: '张',
  },
  {
    name: '视频',
    icon: require('@/assets/images/bucket/vedio.png'),
    percent: '0%',
    space: '0M',
    nums: '0个',
    percentKey: 'usevideo_bfb',
    spaceKey: 'used_videosize',
    numsKey: 'usevideo_nums',
    unit: '个',
  },
  {
    name: '文件',
    icon: require('@/assets/images/bucket/file.png'),
    percent: '0%',
    space: '0M',
    nums: '0个',
    percentKey: 'usefile_bfb',
    spaceKey: 'used_filesize',
    numsKey: 'usefile_nums',
    unit: '个',
  },
])
const totalSpace = ref('0M')

const getNumPercent = (val: string) => {
  if (val) return Number(val.replace('%', ''))
  else return 0
}

const getBucketSpace = async () => {
  const res: any = await getBucketSpaceApi()
  console.log(res)
  res.count && (totalSpace.value = res.count)
  tableData.value.forEach((item: any) => {
    item.percentKey && (item.percent = res[item.percentKey])
    item.spaceKey && (item.space = res[item.spaceKey])
    item.numsKey && (item.nums = res[item.numsKey] + item.unit)
  })
}

onMounted(() => {
  getBucketSpace()
})
</script>

<style lang="scss" scope>
.overview-wrap {
  $height: 300px;

  .content-box {
    background: rgba(255, 255, 255, 0.99);
    box-shadow: 0px 6px 12px 0px rgba(216, 224, 234, 0.2);
    padding: 30px 40px;
  }

  .overview-title {
    font-size: 20px;
    font-weight: bold;
    color: #1C2B4B;
    margin-bottom: 25px;
  }

  .overview-left {
    width: 300px;
    height: $height;

  }

  .overview-right {
    height: $height;

    table {
      height: calc(100% - 49px);
      font-size: 12px;
    }

    td {
      padding-right: 10px;
    }

    progress {
      width: 100%;
      height: 12px;
      background: #E6ECF5;
      box-shadow: inset 0px 4px 4px 0px rgba(168, 193, 231, 0.3);
      appearance: none;
      /*设置iOS下清除灰色背景*/
      -webkit-appearance: none;
      border-radius: 12px;
      border: none;
    }

    /*设置进度条颜色*/
    ::-ms-fill {
      background: #3765FF;
      box-shadow: inset 0px 4px 4px 0px rgba(168, 193, 231, 0.4);
      border-radius: 12px;
    }

    ::-moz-progress-bar {
      background: #3765FF;
      box-shadow: inset 0px 4px 4px 0px rgba(168, 193, 231, 0.4);
      border-radius: 12px;
    }

    ::-webkit-progress-value {
      background: #3765FF;
      box-shadow: inset 0px 4px 4px 0px rgba(168, 193, 231, 0.4);
      border-radius: 12px;
    }

    /*设置进度条背景颜色*/

    ::-webkit-progress-bar {
      background: #E6ECF5;
      box-shadow: inset 0px 4px 4px 0px rgba(168, 193, 231, 0.3);
      border-radius: 12px;
    }

    .right-name {
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: bold;
      color: #1C2B4B;
    }

    .right-percentage {
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 500;
      color: #1C2B4B;
    }

    .chart i {
      font-style: normal;
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: bold;
      color: #3765FF;
    }

    .right-space b {
      font-style: normal;
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: normal;
      color: #333;
    }
  }
}
</style>