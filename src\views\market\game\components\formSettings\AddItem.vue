<script setup lang="ts">
import {markRaw, nextTick, ref, watch} from 'vue'
import {ElMessage, FormInstance} from "element-plus";
import {addItemApi} from "@/api/game";
import {fieldType} from "@/views/market/game/components/fieldType";

const emits = defineEmits(['success', 'close']);

interface Props {
  data: any
}
const props = defineProps<Props>();

const addForm = ref({
  field_id: undefined,
  id: undefined,
  type: '',
  title: '',
  key: '',
  required: 0,
  category: 'xyx',
})
const addRules = markRaw({
  type: [{required: true, message: '请选择表单项类型', trigger: 'change'},],
  title: [{required: true, message: '请输入表单项中文名', trigger: 'blur'},],
  key: [{required: true, message: '请输入表单项英文名', trigger: 'blur'},],
})

const options = ref<string[]>([]);

watch(() => props.data, val => {
  addForm.value = {...addForm.value, ...val};
  options.value = val.values.map((item: any) => item.valtitle);
}, {immediate: true, deep: true});

const handleClose = (index: number) => {
  options.value.splice(index, 1);
};

const inputVisible = ref(false);
const saveTagInput = ref(null);
const inputValue = ref('');
/**
 * 显示输入框
 */
const showInput = () => {
  inputVisible.value = true;
  nextTick(() => {
    saveTagInput.value && saveTagInput.value.ref && saveTagInput.value.ref.focus();
  });
}
// 监听输入框
const handleInputConfirm = () => {
  if (inputValue.value && !options.value.includes(inputValue.value)) {
    options.value.push(inputValue.value);
  }
  inputVisible.value = false;
  inputValue.value = '';
}

const loading = ref(false);
const formRef = ref<FormInstance | undefined>();
const submit = () => {
  if (!formRef.value) return
  formRef.value.validate(async (valid: any) => {
    if (valid) {
      loading.value = true;
      try {
        const params = JSON.parse(JSON.stringify(addForm.value))
        params.value = options.value
        if (params.type == 'radio' || params.type == 'checkbox') {
          if (!params.value) {
            ElMessage.warning('请设置选项的值');
            return;
          } else if (params.value.length == 0) {
            ElMessage.warning('请设置选项的值');
            return;
          } else {
            params.options = params.value.toString()
          }
        }
        delete params.value

        await addItemApi(params);
        ElMessage.success('操作成功');
        emits('success');
      } finally {
        loading.value = false;
        emits('close');
      }
    }
  })
}

const close = () => {
  console.log('关闭表单');
  emits('close');
}
</script>

<template>
  <el-form ref="formRef" :model="addForm" :rules="addRules" label-width="130px">
    <el-form-item label="表单项类型" prop="type">
      <el-select v-model="addForm.type" placeholder="请选择表单项类型">
        <el-option v-for="(item, index) in fieldType" :key="index" :label="item" :value="index">
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item v-if="addForm.type == 'radio' || addForm.type == 'checkbox'" label="表单项选项" prop="value">
      <el-tag :key="tag" v-for="(tag, index) in options" closable :disable-transitions="false"
              @close="handleClose(index)">
        {{ tag }}
      </el-tag>
      <el-input class="input-new-tag" v-if="inputVisible" v-model="inputValue" ref="saveTagInput"
                @keyup.enter.native="handleInputConfirm" @blur="handleInputConfirm">
      </el-input>
      <el-button v-else class="button-new-tag" @click="showInput">+ 添加选项值</el-button>
    </el-form-item>
    <el-form-item label="表单项中文名" prop="title">
      <el-input v-model="addForm.title" placeholder="请输入表单项中文名"></el-input>
    </el-form-item>
    <el-form-item label="表单项英文名" prop="key">
      <el-input v-model="addForm.key" placeholder="请输入表单项英文名"></el-input>
      <p>（英文名称在表单项列表中必须唯一）</p>
    </el-form-item>
    <el-form-item label="表单项必填" prop="required">
      <el-radio-group v-model="addForm.required">
        <el-radio :value="1">必填</el-radio>
        <el-radio :value="0">非必填</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label-width="0">
      <div class="flex-c-center flex-1" style="margin-top: 20px;">
        <el-button type="primary" class="btn" @click="submit" :loading="loading">保存</el-button>
        <el-button class="btn" @click="close">取消</el-button>
      </div>
    </el-form-item>
  </el-form>
</template>

<style scoped lang="scss">
:deep(.el-tag) {
  & + .el-tag,
  & + .el-button,
  & + .input-new-tag {
    margin-left: 10px;
  }
}
.input-new-tag {
  width: 90px;
}
</style>
