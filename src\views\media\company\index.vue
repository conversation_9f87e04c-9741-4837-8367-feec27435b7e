<template>
  <div class="h100 flex-y">
    <page-menu v-if="navList.length" :list="navList" @click-item="navItemClick" :activeIndex="activeIndex" />
    <div class="flex-1">
      <list-view v-show="activeIndex === 0" ref="listViewRef" @changeNav="changeNav" />
      <info-add v-show="activeIndex === 1" ref="infoAddRef" @changeNav="changeNav" @success="refreshList" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineAsyncComponent, computed, watch } from 'vue'

const ListView = defineAsyncComponent(() => import('./components/List.vue'))
const InfoAdd = defineAsyncComponent(() => import('./components/InfoAdd.vue'))

const listViewRef = ref()
const infoAddRef = ref()

const navList = ref([
  { title: '新闻列表', key: 'list', component: ListView },
  { title: '添加新闻', key: 'add', component: InfoAdd, hidden: true },
])
const activeIndex = ref(0)

const navItemClick = (item: any, index: number, data: any) => {
  activeIndex.value = index
}

const rowData:any = ref({})
const changeNav = (data:any) => {
  const { key, val } = data
  if (val) rowData.value = val
  else rowData.value = {}
  infoAddRef.value.setFormData(rowData.value)
  navList.value.forEach((item:any, index:number) => {
    if (item.key === key) {
      activeIndex.value = index
      if (rowData.value.id) item.title = '编辑新闻'
      else item.title = '添加新闻'
    }
  })
}

const refreshList = () => {
  listViewRef.value.refresh()
}
</script>

<style scoped lang="scss">

</style>