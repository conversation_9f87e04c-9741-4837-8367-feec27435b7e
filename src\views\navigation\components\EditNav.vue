<template>
  <edit-dialog ref="editDialogRef" v-model:dialogShow="dialogShow" v-bind="formConfig" @sure="handleSure">
    <el-form ref="formRef" class="edit-form" :model="formData" :rules="rules" label-width="100px">
      <el-divider content-position="left">基础信息</el-divider>
      <div class="base-box flex-x">
        <div class="left-box width260">
          <el-form-item label="导航图片" prop="image_intro">
            <upload-image width="160px" height="160px" v-model:imageIntro="formData.image_intro"  />
          </el-form-item>
        </div>
        <div class="right-box flex-1 ml30">
          <el-form-item label="导航名称" prop="title">
            <el-input v-model="formData.title" placeholder="请输入导航名称" />
          </el-form-item>
          <el-form-item label="导航英文名称" prop="en_title">
            <el-input v-model="formData.en_title" placeholder="请输入导航英文名称" />
          </el-form-item>
          <el-form-item v-if="dialogData.id && dialogData.type == 2" label="外部链接" prop="url">
            <el-input v-model="formData.url" placeholder="请输入外部链接" />
          </el-form-item>
          <el-form-item label="副标题" prop="second_title">
            <el-input v-model="formData.second_title" placeholder="请输入副标题" />
          </el-form-item>
        </div>
      </div>
      <div class="flex">
        <el-divider content-position="left">优化设置</el-divider>
        <jzt-button class="active ml20 flex-shrink" name="一键设置" @click="handleSet" />
      </div>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="网页标题" prop="title_name">
            <el-input v-model="formData.title_name" placeholder="请输入网页标题" />
          </el-form-item>
          <el-form-item label="网页关键词" prop="keywords">
            <el-input v-model="formData.keywords" placeholder="请输入网页关键词" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="网页描述" prop="description">
            <el-input v-model="formData.description" type="textarea" :rows="4" placeholder="请输入网页描述" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </edit-dialog>
</template>

<script setup lang="ts">
import { ref, defineAsyncComponent, watch } from "vue";
import { ElMessage, ElLoading } from "element-plus";
import { useDialog } from "@/hooks/useDialog";
import { editNavApi, handleSetApi } from "@/api/navigation";

const { EditDialog, editDialogRef, dialogShow, dialogData, openDialog, closeDialog } = useDialog()

const UploadImage = defineAsyncComponent(() => import('@/components/Uploads/uploadImg.vue'))

const emit = defineEmits(['success'])

const formConfig = ref({
  title: '编辑导航',
  width: '70%',
  customForm: true,
})

const formData = ref({
  id: '',
  title: '',
  en_title: '',
  second_title: '',
  jianjie: '',
  image_intro: '',
  title_name: '',
  keywords: '',
  description: '',
  url: '',
})

const formRef = ref()
const rules = ref({
  title: [{ required: true, message: '请输入导航名称', trigger: 'blur' }],
})

watch(
  dialogShow,
  (val) => {
    if (val) {
      const { id, title, en_title, second_title, jianjie, image_intro, title_name, keywords, description, url } = JSON.parse(JSON.stringify(dialogData.value))
      formData.value = { id, title, en_title, second_title, jianjie, image_intro, title_name, keywords, description, url }
    }
  }
)

// 一键设置
const handleSet = () => {
  const { title_name, keywords, description } = formData.value
  if (!title_name) return ElMessage.warning('请先填写网页标题')
  if (!keywords) return ElMessage.warning('请先填写网页关键词')
  if (!description) return ElMessage.warning('请先填写网页描述')
  let loading = ElLoading.service({
    lock: true,
    text: 'Loading',
    background: 'rgba(0, 0, 0, 0.7)'
  })
  handleSetApi({
    title_name,
    keywords,
    description,
    id: dialogData.value.id
  }).then(res => {
    ElMessage.success('设置成功')
  }).finally(() => {
    loading.close()
  })
}

// 确定
const handleSure = () => {
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      let loading = ElLoading.service({
        lock: true,
        text: 'Loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      editNavApi(formData.value).then(res => {
        ElMessage.success('修改成功')
        emit('success')
        closeDialog()
      }).finally(() => {
        loading.close()
      })
    }
  })
}

defineExpose({ openDialog, closeDialog })
</script>

<style scoped lang="scss">

</style>