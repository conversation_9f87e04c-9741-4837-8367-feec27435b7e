/*粉丝列表*/
.fans-list {
  max-height: 400px;
  overflow-y: auto;
  .fans-item {
    margin-bottom: 20px;
    .head {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      object-fit: cover;
    }
    .name {
      font-size: 14px;
      font-weight: bold;
      color: #1C2B4B;
      width: calc(100% - 50px);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-left: 10px;
    }
  }
  .more {
    text-align: center;
    line-height: 32px;
    font-size: 14px;
    color: #999;
    cursor: pointer;
  }
}
/* 评论 */
.comment-list {
  .fans-item {
    .head {
      width: 30px;
      height: 30px;
    }
    .name {
      font-weight: normal;
      white-space: initial;
    }
  }
}
/* 回复 */
.reply-list {
  margin-left: 40px;
  .fans-item {
    .head {
      width: 20px;
      height: 20px;
    }
    .name {
      font-size: 12px;
      font-weight: normal;
    }
  }
}