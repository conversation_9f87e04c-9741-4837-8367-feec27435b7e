<script lang="ts" setup>
import {defineAsyncComponent, ref} from "vue";
import {infoDeleteApi, infoListApi, infoReadApi} from "@/api/interaction";
import {useDialog} from "@/hooks/useDialog";
import {useInteraction} from "@/views/content/consult/hooks/useInteraction";
import {ElMessage} from "element-plus";
import DetailDialog from "@/components/DetailDialog.vue";

const DataList = defineAsyncComponent(() => import('@/components/Global/DataList.vue'))

/**
 * 留言已读接口
 */
const readMessageApi = async (id: string) => {
  readLoading.value = true
  try {
    await infoReadApi({id: id})
    ElMessage.success('已读成功');
    refreshTable()
  } catch (e) {
    console.log(e);
  } finally {
    readLoading.value = false
  }
}

/**
 * 批量已读
 */
const batchReadMessage = (rows: any[]) => {
  const notRead = rows.filter(item => !item.is_read)
  if(notRead.length === 0){
    ElMessage.warning('所选留言已全部已读')
    return
  }
  const ids = notRead.map(item => item.id).toString()
  readMessageApi(ids)
}

const {
  dataListRef,
  tableConfig,
  actionList,
  dataAction,
  refreshTable,
  del
} = useInteraction(
    {
      api: infoListApi,
      columns: [
        {label: '内容', prop: 'content'},
        {label: '状态', prop: 'is_read', type: 'slot', width: 100},
        {label: 'IP', prop: 'ip', width: 200},
        {label: '留言时间', prop: 'create_time', sortable: true, width: 250},
        {label: '操作', prop: 'action', type: 'slot', align: 'center', width: 280}
      ]
    },
    [
      {
        name: '批量已读',
        icon: 'icon-yidu',
        key: 'batchReadMessage',
        type: 'action',
        checkSelected: true,
        action: batchReadMessage,
        position: 0
      }
    ],
    infoDeleteApi,
    '/newapi/ContentList/message_export'
)

const readLoading = ref(false)
const { editDialogRef, EditDialog, dialogShow, dialogData, openDialog, closeDialog } = useDialog()

/**
 * 留言已读
 * @param row
 */
const readMessage = (row: any) => {
  const data = []
  for (let contentKey in row.content) {
    data.push({
      label: contentKey,
      value: row.content[contentKey],
      type: 'text'
    })
  }

  if(row && row.business_license) {
    data.push({
      label: '营业执照',
      value: row.business_license,
      type: 'img'
    })
  }
  if(row && row.identity_card) {
    data.push({
      label: '身份证正反面',
      value: row.identity_card.split(','),
      type: 'imgArr'
    })
  }
  if(row && row.other) {
    data.push({
      label: '其他',
      value: row.other.split(','),
      type: 'imgArr'
    })
  }

  openDialog(data)
  if (+row.is_read === 1) {
    return
  }
  readMessageApi('' + row.id)
}
</script>

<template>
  <div class="consult h100">
    <data-list ref="dataListRef" :tableConfig="tableConfig" :action-default="false" @clickBtn="dataAction" :actionList="actionList"
               v-model:multipleSelection="tableConfig.multipleSelection">
      <template #is_read="{ row }">
        <span v-if="+row.is_read === 1">已读</span>
        <span v-if="+row.is_read === 0">未读</span>
      </template>
      <template #action="{ row }">
        <div class="table-action flex">
          <div class="item" @click="readMessage(row)">
            <i class="iconfont icon-chakanxiangqing"></i> <span>查看</span>
          </div>
          <div class="item" @click="del(row)">
            <i class="iconfont icon-shanchu1"></i> <span>删除</span>
          </div>
        </div>
      </template>
    </data-list>

    <edit-dialog ref="editDialogRef" :dialog-show="dialogShow" title="查看留言" :custom-form="true" sure-text="关闭" cancel-text="" @close="closeDialog" @sure="closeDialog">
      <div style="max-height: 500px; overflow: auto">
        <detail-dialog :dialogData="dialogData" :column="1" v-loading="readLoading"/>
      </div>
    </edit-dialog>
  </div>
</template>

<style scoped lang="scss">

</style>
