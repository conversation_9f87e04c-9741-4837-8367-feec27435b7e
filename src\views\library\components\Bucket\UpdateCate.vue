<template>
  <edit-dialog ref="editDialogRef" 
    v-model:dialog-show="dialogShow"
    v-bind="dialogConfig"
    v-model:form-data="dialogConfig.formData"
    @success="handleSuccess"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { useDialog } from '@/hooks/useDialog'
import { editCateNameApi } from '@/api/bucket'

const { EditDialog, editDialogRef, dialogShow, openDialog, closeDialog } = useDialog()

const emit = defineEmits(['success'])

const dialogConfig = ref({
  title: '编辑分类',
  width: '500px',
  formData: {},
  formConfig: {
    formList: [
      { label: '分类名称', prop: 'title', type: 'input', required: true },
    ],
    submit: editCateNameApi
  }
})

const handleSuccess = () => {
  ElMessage.success('修改成功')
  emit('success')
  closeDialog()
}

defineExpose({ openDialog })
</script>

<style scoped lang="scss">

</style>