<template>
  <div class="material-top material-clear">
    <template v-for="(item, index) in list" :key="index">
      <div :class="['material-left-list', { 'on': activeIndex == index }, { 'hidden': item.hidden }]"
        @click="clickItem(item, index)">
        <span>{{ item.title }}</span>
        <slot :name="item.key"></slot>
      </div>
    </template>
    <slot name="action"></slot>
  </div>
</template>

<script setup lang='ts'>
import { watch } from 'vue';

const props = defineProps({
  list: {
    type: Array as any,
    default: []
  },
  activeIndex: {
    type: Number,
    default: 0
  }
});

const emits = defineEmits(['clickItem'])
const clickItem = (item:any, index: number) => {
  emits("clickItem", item, index)
}

watch(() => props.activeIndex, () => {
  // console.log(props.activeIndex)
  clickItem(props.list[props.activeIndex], props.activeIndex)
})
</script>

<style scoped lang="scss">
.material-top {
  background: #fff;
  width: 100%;
  border-bottom: 1px solid #E3E5EC;
  margin-top: 9px;

  &.material-clear {
    &::after {
      content: "";
      display: block;
      clear: both;
    }
  }

  .material-left-list {
    cursor: pointer;
    display: block;
    float: left;
    width: auto;
    height: 36px;
    background: #FFFFFF;
    padding: 0 8px;
    margin: 0 20px;
    line-height: 36px;
    position: relative;

    span {
      font-size: 12px;
      font-family: PingFang SC;
      font-weight: 500;
      color: #1C2B4B;
    }

    &.on {
      background-image: none;
      border-bottom: 2px solid var(--jzt-color-main);
      &.hidden {
        display: block;
      }

      span {
        font-size: 12px;
        font-family: PingFang SC;
        font-weight: bold;
        color: #1C2B4B;
      }
    }

    &.hidden {
      display: none;
    }
  }
}
</style>