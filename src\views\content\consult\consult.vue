<template>
  <div class="h100 flex-y">
    <page-menu v-if="navList.length" :list="navList" @click-item="navItemClick" :activeIndex="activeIndex" />
    <div class="flex-1">
      <component ref="currentComponentRef" :is="currentComponent" :cate-type="cateType" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import {computed, defineAsyncComponent, markRaw, ref} from "vue";
import store from "@/store";

const isFree = computed(() => store.getters.isFree)

const navList = computed(() => {
  const list = [
    { title: '招聘管理', key: 'recruitment', component: defineAsyncComponent(() => import('@/views/content/recruitment.vue')) },
    { title: '咨询管理', key: 'consult', component: defineAsyncComponent(() => import('@/views/content/consult/components/Consult.vue')) },
    { title: '留言管理', key: 'message', component: defineAsyncComponent(() => import('@/views/content/consult/components/Message.vue')) },
    { title: '百度商桥', key: 'commercialBridge', component: defineAsyncComponent(() => import('@/views/content/consult/components/Code.vue')) },
    { title: 'QQ客服', key: 'qq', component: defineAsyncComponent(() => import('@/views/content/consult/components/Code.vue')) },
  ]
  if (!isFree.value){
    //   在list的第3个元素后插入{ title: '商户通', key: 'merchantLink', component: defineAsyncComponent(() => import('@/views/content/consult/components/Code.vue')) },
    list.splice(3, 0, { title: '商户通', key:'merchantLink', component: defineAsyncComponent(() => import('@/views/content/consult/components/Code.vue')) })
  }
  return list
})

const activeIndex = ref(0)

const navItemClick = (item: any, index: number) => {
  activeIndex.value = index
}

const currentComponent = computed(() => {
  return navList.value[activeIndex.value].component
})

const cateType = computed(() => { return navList.value[activeIndex.value].key })
</script>

<style lang="scss" scoped>

</style>