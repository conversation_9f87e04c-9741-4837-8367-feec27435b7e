<template>
  <el-select v-loading="loading" class="w100" v-model="_value" placeholder="请选择分类" @change="change">
    <template v-if="optionType == 'list'">
      <el-option label="所有分类" :value="cateType == 'text' ? '' : 0"></el-option>
      <el-option v-if="cateType != 'activity'" label="未分组" :value="cateType == 'text' ? 0 : -1"></el-option>
    </template>
    <template v-else-if="optionType == 'content-add'">
      <el-option v-if="cateType != 'activity'" label="无" :value="0"></el-option>
    </template>
    <template v-else-if="optionType == 'type-add'">
      <el-option v-if="cateType != 'activity'" label="顶级分类" :value="1"></el-option>
    </template>
    <el-option v-for="item in categoryList" :key="item.id" :label="item.type_title" :value="item.id">
      <span v-html="item.title"></span>
    </el-option>
  </el-select>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import { contentTypeApi } from '@/api/content'

const props = defineProps({
  cateType: {
    type: String,
    default: 'content'
  },
  value: {
    type: [String, Number],
    default: ''
  },
  apiType: {
    type: String,
    default: ''
  },
  optionType: {
    type: String,
    default: 'list'
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  maxNumber: {
    type: Number,
    default: 1
  }
})

const emit = defineEmits(['update:value', 'change', 'update:maxNumber'])

const _value = ref(props.value)

const categoryList:any = ref([])
const loading = ref(false)
const getCategoryList = async () => {
  try {
    loading.value = true
    // , {limit: 1000}
    const { data: res, maxnum}:any = await contentTypeApi(props.apiType, {}, { allResult: true })
    console.log(res, 'res---')
    if (props.optionType == 'type-add' && props.cateType == 'activity') {
      categoryList.value = res.filter((item: any) => item.parent_id == 1)
    } else {
      categoryList.value = res
    }
    if ((props.optionType == 'content-add' && !_value.value && !props.isEdit) 
      || (props.cateType == 'activity' && (!_value.value || _value.value == 1) )) {
      _value.value = res[0].id
      change(res[0].id)
    } else {
      change(_value.value)
    }
    emit('update:maxNumber', maxnum)
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}

const change = (val: any) => {
  // 找到对应的数据
  const item = categoryList.value.find((item: any) => item.id == val)
  emit('change', item)
  emit('update:value', val)
}

watch(() => props.value, (val: any) => {
  _value.value = val
})

const refresh = () => {
  getCategoryList()
}

onMounted(() => {
  getCategoryList()
})

defineExpose({ refresh })
</script>

<style scoped lang="scss">

</style>