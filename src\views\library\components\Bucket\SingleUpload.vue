<template>
  <div class="single-upload-wrap">
    <single-upload-img ref="singleUploadImgRef" :folder="props.folder" :currentFolder="currentFolder" @cancel="uploadRef.value = ''"  @success="uploadSuccess" />
    <!-- 图片 视频 音频 zip word excel ppt pdf  -->
    <input class="upload-input" ref="uploadRef" type="file" @change="handleChange" accept=".jpg,.jpeg,.png,.gif,.JPG,.JPEG,.PNG,.GIF,.x-icon,.mp4,.MP4,.mp3,.flv,.m3u8,.wmv,.m4v,.rar,.pdf,.xls,.xlsx,.txt,.doc,.docx,.ppt,.pptx,.zip" />
  </div>
</template>

<script setup lang="ts">
import { ref, defineAsyncComponent } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import { uploadVideoApi, uploadFile<PERSON>pi, checkSpace<PERSON>pi } from '@/api/bucket'

const SingleUploadImg = defineAsyncComponent(() => import('./SingleUpload/SingleUploadImg.vue'))
const singleUploadImgRef = ref()

const props = defineProps({
  folder: {
    type: Number,
    default: 0
  },
  currentFolder: {
    type: Object,
    default: () => {}
  }
})

const emit = defineEmits(['success'])

const uploadRef = ref<any>(null)

const uploadClick = () => {
  uploadRef.value.click()
}

const handleChange = () => {
  console.log(uploadRef.value.files)
  // 判断文件类型
  const file = uploadRef.value.files[0]
  const fileName = file.name
  const fileExtension = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase()

  // 允许的文件扩展名列表（全部小写，因为我们已经将文件扩展名转换为小写）
  const allowedExtensions = [
    'jpg', 'png', 'gif', 'jpeg', 'x-icon',
    'mp4', 'mp3', 'flv', 'm3u8', 'wmv', 'm4v',
    'rar', 'pdf', 'xls', 'xlsx', 'txt', 'doc', 'docx', 'ppt', 'pptx', 'zip'
  ]

  // 检查文件扩展名是否在允许列表中
  if (!allowedExtensions.includes(fileExtension)) {
    ElMessage.error('文件类型不支持')
    uploadRef.value.value = ''
    return
  }

  // 只通过文件后缀判断文件类型，不使用MIME类型验证
  console.log(`文件类型: ${fileExtension}`)
  // 图片最大5M 视频最大100M 其余15M
  const imageMaxSize = 5 * 1024 * 1024, videoMaxSize = 100 * 1024 * 1024, otherMaxSize = 15 * 1024 * 1024

  // 根据文件扩展名判断文件类型
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'x-icon']
  const videoExtensions = ['mp4', 'flv', 'm3u8', 'wmv', 'm4v']
  const audioExtensions = ['mp3']

  let fileType = 'default'
  if (imageExtensions.includes(fileExtension)) {
    fileType = 'image'
  } else if (videoExtensions.includes(fileExtension)) {
    fileType = 'video'
  } else if (audioExtensions.includes(fileExtension)) {
    fileType = 'audio'
  }

  const maxSizeMap: any = {
    'image': imageMaxSize,
    'video': videoMaxSize,
    'audio': videoMaxSize, // 音频文件使用与视频相同的大小限制
    'default': otherMaxSize
  };

  const maxSize = maxSizeMap[fileType];
  console.log(maxSize, 'maxSize', fileType, 'fileType')

  if (file.size > maxSize) {
    const maxMB = maxSize / 1024 / 1024;
    ElMessage.error(`文件大小不能超过${maxMB.toFixed(2)}M`);
    uploadRef.value.value = ''
    return
  }

  // 检查空间大小
  checkSpaceApi({size: file.size}).then((res: any) => {
    console.log(res, 'checkSpaceApi')

    // 根据文件类型调用不同的上传方法
    if (fileType === 'image') {
      uploadImg(file)
    } else if (fileType === 'video' || fileType === 'audio') {
      uploadVideo(file)
    } else {
      uploadFile(file)
    }
  }).catch((err: any) => {
    console.log(err, 'checkSpaceApi')
    uploadRef.value.value = ''
  })
}

const uploadImg = (file: any) => {
  console.log(file, 'uploadImgApi')
  singleUploadImgRef.value.openDialog(file)
}

const uploadMedia = (file: any, api: any, field: string) => {
  console.log(file, `${field}Api`)
  const formData = new FormData()
  formData.append(field, file)
  if(field == 'video') formData.append('title', file.name)
  formData.append('f_id', props.folder.toString())
  const loadingInstance = ElLoading.service({
    lock: true,
    text: '上传中...',
    background: 'rgba(0, 0, 0, 0.7)'
  })
  api(formData).then((res: any) => {
    console.log(res, `${field}Api`)
    ElMessage.success('上传成功')
    emit('success')
  }).catch((err: any) => {
    console.log(err, `${field}Api`)
  }).finally(() => {
    loadingInstance.close()
    uploadRef.value.value = ''
  })
}

const uploadVideo = (file: any) => {
  uploadMedia(file, uploadVideoApi, 'video')
}

const uploadFile = (file: any) => {
  uploadMedia(file, uploadFileApi, 'file')
}

// 上传成功
const uploadSuccess = () => {
  console.log('uploadSuccess')
  emit('success')
}

defineExpose({ uploadClick })
</script>

<style scoped lang="scss">
.single-upload-wrap {
  position: relative;
  .upload-input {
    position: absolute;
    top: 1000px;
    left: 1000px;
    width: 100%;
    height: 100%;
    opacity: 0;
  }
}
</style>