<template>
  <div class="main-box h100">
    <content-tips v-if="tips" class="mb10" :tips="tips"></content-tips>
    <marketing-view v-if="isAgent"></marketing-view>
    <normal-view v-else></normal-view>
  </div>
</template>

<script setup lang="ts">
import { computed, defineAsyncComponent } from 'vue'
import store from '@/store'

const ContentTips = defineAsyncComponent(() => import('@/components/ContentTips.vue'))
const normalView = defineAsyncComponent(() => import('./components/normal.vue'))
const marketingView = defineAsyncComponent(() => import('./components/marketing.vue'))

const isAgent = computed(() => store.getters.isAgent)
const tips = computed(() => {
  return store.getters.siteInfo.fhtext || ''
})
</script>

<style scoped lang="scss">
.main-box {
  padding: 20px;
  box-sizing: border-box;
}
</style>
