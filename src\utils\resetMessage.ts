/** 重置message，防止同时多个重复弹出message弹框 */
import { ElMessage } from 'element-plus';
 
let messageInstance: any = null
const resetMessage:any = (options: string | object) => {
  if (messageInstance) {
    messageInstance.close()
  }
  messageInstance = ElMessage(options)
}
['error', 'success', 'info', 'warning'].forEach((type: string) => {
  resetMessage[type] = (options: any) => {
    if (typeof options === 'string') {
      options = {
        message: options
      }
    }
    options.type = type
    return resetMessage(options)
  }
})
export default resetMessage

