import App from "./App.vue";
import router from "./router";
import { setupStore } from "/@/store";
import { getServerConfig } from "./config";
import { createApp, Directive } from "vue";
import { MotionPlugin } from "@vueuse/motion";
import useElementPlus from "element-plus";
import zhCn from "element-plus/es/locale/lang/zh-cn";
import "element-plus/dist/index.css";
import { injectResponsiveStorage } from "/@/utils/storage/responsive";

import "animate.css";
import "virtual:windi.css";
// 导入公共样式
import "./style/index.scss";
import "/@/style/table.scss"; //表格样式
import "./style/element-plus/index.scss";
import "@pureadmin/components/dist/index.css";
import "@pureadmin/components/dist/theme.css";
// 导入字体图标
import "./assets/iconfont/iconfont.js";
import "./assets/iconfont/iconfont.css";
import * as ElementPlusIconsVue from "@element-plus/icons-vue";

// 图片裁剪

const app = createApp(App);

// 自定义指令
import * as directives from "/@/directives";
Object.keys(directives).forEach(key => {
  app.directive(key, (directives as { [key: string]: Directive })[key]);
});

// 引入element图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

// 全局注册`@iconify/vue`图标库
import {
  IconifyIconOffline,
  IconifyIconOnline,
  FontIcon
} from "./components/ReIcon";
app.component("IconifyIconOffline", IconifyIconOffline);
app.component("IconifyIconOnline", IconifyIconOnline);
app.component("FontIcon", FontIcon);

//全局注册自定义按钮组件
import jztButton from "/@/components/common/jztButton";
app.component("jztButton", jztButton);

//全局注册自定义分页组件
import jztPages from "/@/components/common/jztPages";
import { useSiteStore } from "/@/store/modules/site";
app.component("jztPage", jztPages);

getServerConfig(app).then(async config => {
  setupStore(app);
  if (import.meta.env.MODE === "development") {
    await useSiteStore().updateSiteInfoSync(); // 解决站点id不存在问题
  }
  app.use(router);
  await router.isReady();
  injectResponsiveStorage(app, config);
  app.use(MotionPlugin).use(useElementPlus, {
    locale: zhCn
  });
  app.mount("#app");
});
