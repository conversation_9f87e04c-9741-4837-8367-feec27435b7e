<script setup lang="ts">
import DataList from "@/components/Global/DataList.vue";
import {markRaw, ref} from "vue";
import {deleteFormDataApi, getFormDataListApi, readFormDataApi} from "@/api/game";
import {ElMessage, ElMessageBox} from "element-plus";
import {useDialog} from "@/hooks/useDialog";

const emits = defineEmits(['changeNav']);

/**
 * 返回列表
 */
const goBack = () => {
  emits('changeNav', 0);
};

const dataListRef = ref();

const loading = ref(false);

/**
 * 删除接口
 * @param id
 * @param showLoading
 */
const delApi = async (id: number | string, showLoading = false) => {
  ElMessageBox.confirm('确认删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    if(showLoading) loading.value = true;
    try {
      await deleteFormDataApi({id});
      ElMessage.success('删除成功');
      dataListRef.value.refresh();
    } finally {
      loading.value = false;
    }
  }).catch(() => {
  });
};

/**
 * 删除
 * @param row
 */
const delItems = (row: any) => {
  if(row.length === 0) return ElMessage.warning('请先选择数据');
  const ids = row.map((item: any) => item.id).join(',');
  delApi(ids, true);
};
/**
 * 设置阅读状态
 * @param ids
 * @param showLoading
 */
const read = async (ids: string | number, showLoading = false) => {
  showLoading && (loading.value = true);
  try {
    await readFormDataApi({id: ids});
    ElMessage.success('已读成功');
    dataListRef.value.refresh();
  } finally {
    loading.value = false;
  }
}
/**
 * 批量已读
 * @param row
 */
const batchRead = (row: any) => {
//   过滤掉已读数据
  const ids = row.filter((item: any) => item.state === 0).map((item: any) => item.id).join(',');
  if(!ids) return ElMessage.warning('所选数据全部已读！');
  read(ids, true);
}

const actionList = ref([
  {
    name: "批量已读",
    icon: "icon-yidu",
    action: batchRead,
    checkSelected: true
  },
  {
    name: "删除",
    icon: "icon-shanchu1",
    action: delItems,
    checkSelected: true
  },
  {
    name: '返回列表',
    icon: 'icon-fanhui1',
    key: 'back',
    action: goBack
  },
]);
/**
 * 按钮点击事件
 * @param button
 */
const dataAction = (button: any) => {
  if (button.checkSelected) {
    if (!tableConfig.value.multipleSelection.length) {
      ElMessage.warning('请先选择数据')
      return
    }
  }
  button.action(tableConfig.value.multipleSelection)
}

const tableConfig = ref({
  api: getFormDataListApi,
  multipleSelection: [],
  params: {
    title: ''
  },
  columns: [
    {
      label: "内容",
      prop: "info",
      type: "slot"
    },
    {
      label: "状态",
      prop: "state",
      type: "slot",
      width: 80
    },
    {
      label: "IP",
      prop: "ip",
      width: 160
    },
    {
      label: "提交时间",
      prop: "create_time",
      width: 200
    },
    {
      label: "操作",
      prop: "action",
      width: 160,
      type: "slot",
      align: 'center'
    },
  ],
});
const stateType = markRaw<{ [key: number]: string }>({0: '未读', 1: '已读'});

const { EditDialog, editDialogRef, dialogData, dialogShow, openDialog, closeDialog } = useDialog();
/**
 * 查看
 */
const view = (row: any) => {
  openDialog(row);
  if(row.state === 0){
    read(row.id);
  }
}

/**
 * 删除
 */
const del = (id: number | string) => {
  if (!id) return
  delApi(id);
}
</script>

<template>
<div class="wrap h100" v-loading="loading">
  <data-list ref="dataListRef" :tableConfig="tableConfig" :action-default="false" @clickBtn="dataAction"
             :actionList="actionList" v-model:multipleSelection="tableConfig.multipleSelection">
    <template #info="{row}">
      <div v-if="row.info" class="info-box flex">
        <template v-for="(value, key, index) in row.info">
          <div v-if="index < 4" :key="index" class="info-item">{{key}}: {{value}}</div>
        </template>
      </div>
      <div v-else>未填写</div>
    </template>
    <template #state="{row}">
      {{ stateType[row.state] || '未知' }}
    </template>
    <template #action="{ row }">
      <div class="table-action flex">
        <div class="item" @click="view(row)">
          <i class="iconfont icon-chakanxiangqing"></i> <span>查看</span>
        </div>
        <div class="item" @click="del(row.id)">
          <i class="iconfont icon-shanchu1"></i> <span>删除</span>
        </div>
      </div>
    </template>
  </data-list>

  <edit-dialog ref="editDialogRef" :dialog-data="dialogData" :dialog-show="dialogShow" @close="closeDialog" @sure="closeDialog" title="查看数据" :custom-form="true" :show-bottom-btn="false" width="600px">
    <el-descriptions :column="1">
      <el-descriptions-item v-for="(value, key, index) in dialogData.info" :key="index" :label="key + '：'">{{ value }}</el-descriptions-item>
    </el-descriptions>
  </edit-dialog>
</div>
</template>

<style scoped lang="scss">
.wrap{
  overflow: auto;
}
</style>
