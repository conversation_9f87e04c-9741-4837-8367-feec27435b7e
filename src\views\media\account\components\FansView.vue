<template>
  <edit-dialog ref="editDialogRef" v-model:dialog-show="dialogShow" v-bind="formConfig">
    <div class="fans-list">
      <div class="fans-item flex" v-for="(item, index) in appData.fansList" :key="index">
        <img :src="item.avatar" alt="" class="head">
        <div class="name">{{ item.nickname }}</div>
      </div>
      <div class="more" v-if="appData.fansHasMore && appData.fansList.length > 0"
        @click="getFansList(appData.fansType)">点击查看更多</div>
    </div>
  </edit-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive } from 'vue'
import { useDialog } from '@/hooks/useDialog'
import { getFansListApi, getFollowListApi } from '@/api/media/account'

const { EditDialog, editDialogRef, dialogData, dialogShow, openDialog } = useDialog()

const props = defineProps({
  nowInfoId: {
    type: Number,
    default: 0
  }
})

const formConfig = ref({
  title: '列表',
  width: '600px',
  customForm: true,
  showBottomBtn: false
})

/* 粉丝 关注 */
const appData = reactive({
  fansList: [] as any[],   //粉丝列表
  fansType: 'fans',   //粉丝列表
  fansName: '粉丝',  // 粉丝列表 弹框标题
  fanCursor: null, //粉丝列表 分页
  fansHasMore: false,  //是否更多
})

const loading = ref(false)
const getFansList = async (type: string) => {
  loading.value = true
  try {
    let api = getFansListApi
    appData.fansType = type
    if (type == 'fol') api = getFollowListApi
    if (type == 'fan') api = getFansListApi
    const res: any = await api({ id: props.nowInfoId, page: appData.fanCursor })
    appData.fansList = res.list
    appData.fansHasMore = res.has_more
    appData.fanCursor = res.cursor
  } catch (error) {
    console.log(error, '粉丝列表')
  } finally {
    loading.value = false
  }
}

watch(dialogShow, (val) => {
  if (val && dialogData.value) {
    const { type } = dialogData.value
    if (type == 'fan') formConfig.value.title = '粉丝列表'
    if (type == 'fol') formConfig.value.title = '关注列表'
    getFansList(dialogData.value)
  }
})

defineExpose({ openDialog })

</script>

<style scoped lang="scss">

</style>