<script setup lang="ts">
import DataList from "@/components/Global/DataList.vue";
import {markRaw, ref} from "vue";
import {deleteRecycleApi, getRecycleListApi, restoreRecycleApi} from "@/api/system";
import {cateTypeDic} from "@/utils/contentType";
import {useDialog} from "@/hooks/useDialog";
import DetailDialog from "@/views/system/components/DetailDialog.vue";
import {ElMessage, ElMessageBox} from "element-plus";

const {EditDialog, dialogShow, dialogData, openDialog, closeDialog} = useDialog()

const tableConfig = ref({
  api: getRecycleListApi,
  apiType: undefined,
  showIndex: false,
  columns: [
    {label: '封面', prop: 'image_intro', type: 'image'},
    {label: '栏目分类', prop: 'cate', type: 'slot'},
    {label: '分类', prop: 'category', type: 'slot'},
    {label: '名称', prop: 'title'},
    {label: '添加时间', prop: 'created', sortable: 'custom'},
    {label: '状态', prop: 'state', type: 'state'},
    {label: '操作', prop: 'action', type: 'slot', align: 'center', width: 280}
  ],
  multipleSelection: [],
  params: {
    cate: '0',
    title: ''
  }
})

const dataListRef = ref<any>(null)

/**
 * 刷新列表
 */
const refreshTable = () => {
  dataListRef.value.refresh()
}

const getCate = (cate: string) => {
  return cateTypeDic[cate]?.name
}

/**
 * 恢复接口
 * @param id
 */
const recoverApi = (id: string) => {
  ElMessageBox.confirm('确定要恢复信息吗？', '提示').then(async () => {
    await restoreRecycleApi({id})
    ElMessage.success('恢复成功')
    refreshTable()
  }).catch(() => {
    console.log('取消恢复该内容');
  })
}

/**
 * 批量恢复
 */
const recoverSelected = () => {
  if (tableConfig.value.multipleSelection.length === 0) {
    ElMessage.warning('请选择要恢复的内容')
    return
  }
  const ids = tableConfig.value.multipleSelection.map((item: any) => item.id).toString()
  recoverApi(ids)
}

/**
 * 删除接口
 * @param id
 */
const delApi = (id: string) => {
  ElMessageBox.confirm('确定要删除该内容吗？', '提示').then(async () => {
    await deleteRecycleApi({id})
    ElMessage.success('删除成功')
    refreshTable()
  }).catch(() => {
    console.log('取消删除该内容');
  })
}

const deleteSelected = () => {
  if (tableConfig.value.multipleSelection.length === 0) {
    ElMessage.warning('请选择要删除的内容')
    return
  }
  const ids = tableConfig.value.multipleSelection.map((item: any) => item.id).toString()
  delApi(ids)
}

const actionList = ref([
  {
    name: '恢复',
    icon: 'icon-chexiao',
    key: 'recover',
    type: 'action',
    checkSelected: false,
    action: recoverSelected,
  },
  {name: '删除', icon: 'icon-shanchu1', key: 'delete', type: 'action', checkSelected: true, action: deleteSelected},
])

const dataAction = (action: any) => {
  if (action.checkSelected) {
    if (!tableConfig.value.multipleSelection.length) {
      ElMessage.warning('请先选择数据')
      return
    }
  }
  action.action(tableConfig.value.multipleSelection)
}

const del = (row: any) => {
  delApi(row.id);
}

const detail = (row: any) => {
  if (row.fulltext === '<p>null</p>') {
    row.fulltext = ''
  }
  openDialog(row)
}

const recover = (row: any) => {
  if (!row.id) {
    return
  }
  recoverApi(row.id);
}

const options = markRaw({
  '0': '所有栏目',
  'content': '文章',
  'goods': '产品'
})
</script>

<template>
  <div class="h100 wrap">
    <data-list ref="dataListRef" :tableConfig="tableConfig" :action-default="false" @clickBtn="dataAction"
               :actionList="actionList" v-model:multipleSelection="tableConfig.multipleSelection">
      <template #top-right>
        <div class="flex">
          <el-select v-model="tableConfig.params.cate" style="width: 100px; flex-shrink: 0">
            <el-option v-for="(item, key) in options" :key="key" :label="item" :value="key"/>
          </el-select>
          <el-input placeholder="请输入标题搜索" v-model="tableConfig.params.title" style="margin-left: 10px; width: 300px"/>
        </div>
      </template>
      <template #cate="{ row }">
        <span class="cate">{{ getCate(row.cate) }}</span>
      </template>
      <template #category="{ row }">
        <span class="cate">{{ row?.category?.title || '未知' }}</span>
      </template>
      <template #action="{ row }">
        <div class="table-action flex">
          <div class="item" @click="detail(row)">
            <i class="iconfont icon-chakanxiangqing"></i> <span>查看</span>
          </div>
          <div class="item" @click="recover(row)">
            <i class="iconfont icon-chexiao"></i> <span>恢复</span>
          </div>
          <div class="item" @click="del(row)">
            <i class="iconfont icon-shanchu1"></i> <span>删除</span>
          </div>
        </div>
      </template>
    </data-list>

    <edit-dialog ref="editDialogRef" :dialog-show="dialogShow" title="查看详情" :custom-form="true" :show-bottom-btn="false"
                 @close="closeDialog" @sure="closeDialog">
      <detail-dialog v-if="dialogShow" :dialog-data="dialogData"/>
    </edit-dialog>
  </div>
</template>

<style scoped lang="scss">
.cover {
  width: 78px;
  height: 40px;
}
.icon-chexiao {
  font-size: 22px;
  font-weight: bold;
}
:deep(.icon-chexiao){
  font-size: 22px;
}
</style>
