<script setup lang="ts">
import {computed, markRaw, ref, watch} from "vue";
import {
  getChannelStructureApi,
  getContentAnalysisApi,
  getGzhListApi, getOverviewDataApi,
  getUserAnalysisApi
} from "@/api/report/gzh";
import LineChart from "./components/LineChart.vue";
import RadioButtonGroup from "./components/RadioButtonGroup.vue";
import PieChart from "./components/PieChart.vue";
import PdfPage from "@/views/media/components/PdfPage.vue";
import StatisticsList from "../../components/StatisticsList.vue";
import {exportPDF} from "@/utils/htmlToPdf";
import {useGzhInfo} from "@/hooks/useGzhInfo";
import GzhName from "./components/GzhName.vue";
import ExportText from "./components/ExportText.vue";

/*昨日关键数据*/
const yesterdayData = ref([
  {
    icon: require('@/assets/images/report/item01.png'),
    title: '阅读次数',
    value: 0
  },
  {
    icon: require('@/assets/images/report/item02.png'),
    title: '分享次数',
    value: 0
  },
  {
    icon: require('@/assets/images/report/item03.png'),
    title: '完成阅读次数',
    value: 0
  },
  {
    icon: require('@/assets/images/report/item04.png'),
    title: '新关注人数',
    value: 0
  },
  {
    icon: require('@/assets/images/report/item05.png'),
    title: '取消关注人数',
    value: 0
  },
  {
    icon: require('@/assets/images/report/item06.png'),
    title: '净增关注人数',
    value: 0
  },
  {
    icon: require('@/assets/images/report/item07.png'),
    title: '累计关注人数',
    value: 0
  },
]);

/*获取公众号信息*/
const {loading, gzhInfo, getGzhInfo} = useGzhInfo();
getGzhInfo((res: any) => {
  if(res.list){
    yesterdayData.value[0].value = res.list.read_num || 0;
    yesterdayData.value[1].value = res.list.share_num || 0;
    yesterdayData.value[2].value = res.list.w_read_num || 0;
    yesterdayData.value[3].value = res.list.new_user || 0;
    yesterdayData.value[4].value = res.list.cancel_user || 0;
    yesterdayData.value[5].value = res.list.j_new_user || 0;
    yesterdayData.value[6].value = res.list.cumulate_user || 0;
  }
})

/*获取公众号列表*/
const listLoading = ref(false);
const gzhList = ref([]);
const listPage = ref({
  page: 1,
  size: 10,
});
const listTotalPage = ref(1);
const getGzhList = async () => {
  listLoading.value = true;
  try{
    const res: any = await getGzhListApi({catid: '公众号', ...listPage.value});
    gzhList.value = res.data.map((v: any) => ({label: v.title, selected: false}));
    listTotalPage.value = res.last_page;
  }catch (e){
    console.log(e);
  }finally {
    listLoading.value = false;
  }
};
getGzhList();
watch(() => [gzhInfo.value.title, gzhList.value], () => {
  if (gzhInfo.value.title && gzhList.value.length){
    gzhList.value.forEach((v: any) => {
      if(v.label === gzhInfo.value.title) v.selected = true;
    });
  }
}, {deep: true, immediate: true});

interface OptionsList {
  label: string;
  value: string;
}

/*内容分析筛选选项*/
const dataType = ref<OptionsList[]>([
  {
    label: '阅读',
    value: 'yd'
  },
  {
    label: '分享',
    value: 'fx'
  },
  {
    label: '群发篇数',
    value: 'qf'
  },
  {
    label: '微信收藏',
    value: 'sc'
  },
  {
    label: '跳转阅读全文',
    value: 'qw'
  },
])
const channel = ref<OptionsList[]>([
  {
    label: '全部',
    value: 'qb'
  },
  {
    label: '公众号消息',
    value: 'gzh'
  },
  {
    label: '聊天会话',
    value: 'lthh'
  },
  {
    label: '朋友圈',
    value: 'pyq'
  },
  {
    label: '朋友在看',
    value: 'pyzk'
  },
  {
    label: '公众号主页',
    value: 'gzhzy'
  },
  {
    label: '其他',
    value: 'qt'
  },
])
const form = ref({
  contype: 'yd',
  contypetwo: 'qb',
  datedate: []
})
const disabledDate = (time: Date) => {
  return time.getTime() > Date.now();
};
const changeDataType = () => {
  form.value.contypetwo = 'qb'
}
const contentDate = computed(() => {
  return form.value.datedate? form.value.datedate.join(' ~ ') : '';
})
const contentDataType = computed(() => {
  return dataType.value.find((v: any) => v.value === form.value.contype)?.label || '';
})
const contentChannel = computed(() => {
  return channel.value.find((v: any) => v.value === form.value.contypetwo)?.label || '';
})

/*内容分析数据*/
interface ContentData {
  date: string[];
  data: any[];
}
const contentData = ref<ContentData>({
  date: [],
  data: []
});
const chartLoading = ref(false);

/*获取数据*/
const getContentData = async () => {
  chartLoading.value = true;
  try{
    const data = JSON.parse(JSON.stringify(form.value));
    data.datedate = form.value.datedate ? form.value.datedate.join(' ~ ') : '';
    const res: any = await getContentAnalysisApi(data);
    contentData.value.date = res.datenewlist;
    contentData.value.data = [
      {name: res.nameone, list: res.content_cnum}, {name: res.nametwo, list: res.content_rnum}
    ];
  }catch (e){
    console.log(e);
  }finally {
    chartLoading.value = false;
  }
}
watch(() => form.value, () => {
  getContentData();
}, {deep: true})

/*内容分析echarts设置*/
const contentSettings = markRaw({
  colors: ['rgba(53, 131, 251, 1)', 'rgba(253, 178, 5, 1)']
})

/*渠道构成筛选表单*/
const channelForm = ref({
  qdtype: 'ydyd'
})
const channelComposition = ref<OptionsList[]>([
  {
    label: '阅读',
    value: 'ydyd'
  },
  {
    label: '分享',
    value: 'fxfx'
  }
]);
const channelDataType = computed(() => {
  return channelComposition.value.find((v: any) => v.value === channelForm.value.qdtype)?.label || '';
})

/*渠道构成echarts设置*/
const channelSettings = markRaw({
  colors: ['rgba(255, 120, 102, 1)', 'rgba(209, 239, 209, 1)', 'rgba(253, 178, 5, 1)', 'rgba(53, 131, 251, 1)', 'rgba(61, 207, 82, 1)']
});

/*渠道构成数据*/
const channelData = ref<{ name: string, value: number }[]>([
  {name: '公众号会话', value: 0},
  {name: '朋友圈', value: 0},
  {name: '好友', value: 0},
  {name: '看一看', value: 0},
  {name: '其他', value: 0},
]);
const channelLoading = ref(false);
const getChannelData = async () => {
  channelLoading.value = true;
  try{
    const res: any = await getChannelStructureApi(channelForm.value);
    channelData.value = res.qdlist;
  }catch (e){
    console.log(e);
  }finally {
    channelLoading.value = false;
  }
};

/*用户分析表单*/
const userForm = ref({
  datatype: 'xzrs'
})
const userDataOptions = ref<OptionsList[]>([
  {
    label: '新增用户',
    value: 'xzrs'
  },
  {
    label: '净增人数',
    value: 'jzrs'
  },
  {
    label: '累积人数',
    value: 'ljrs'
  },
  {
    label: '取消关注人数',
    value: 'qgrs'
  },
]);
const userDataType = computed(() => {
  return userDataOptions.value.find((v: any) => v.value === userForm.value.datatype)?.label || '';
})

/*用户分析数据*/
const userLoading = ref(false);
const userData = ref<ContentData>({
  date: [],
  data: []
});
const getUserData = async () => {
  userLoading.value = true;
  try{
    const res: any = await getUserAnalysisApi(userForm.value);
    userData.value.date = res.gzdate;
    userData.value.data = [
      {name: res.gzname, list: res.gzdata}
    ];
  }catch (e){
    console.log(e);
  }finally {
    userLoading.value = false;
  }
}

/*导出*/
const pdfWidth = "calc(841.89px * 1.5)";
const pdfHeight = "calc(595.28px * 1.5)";
const exportPdf = () => {
  setTimeout(() => {
    exportPDF('公众号使用报告', 'export')
  }, 100)
}

const contentList = computed(() => {
  return [
    {name: '时间', value: contentDate.value},
    {name: '数据类型', value: contentDataType.value},
    {name: '渠道类型', value: contentChannel.value},
  ]
})
const channelCompositionList = computed(() => {
  return [
    {name: '数据类型', value: channelDataType.value},
  ]
})
const userDataList = computed(() => {
  return [
    {name: '数据类型', value: userDataType.value},
  ]
})

/*获取所有数据*/
const getAllData = async () => {
  const res:any = await getOverviewDataApi();
  contentData.value.date = res.datenewlist;
  contentData.value.data = [
    {name: res.nameone, list: res.content_cnum}, {name: res.nametwo, list: res.content_rnum}
  ];
  channelData.value = res.qdlist;
  userData.value.date = res.gzdate;
  userData.value.data = [
    {name: res.gzname, list: res.gzdata}
  ];
}
getAllData();
</script>

<template>
<div class="h100" style="overflow: hidden">
  <div class="wrap h100">
    <div class="content-box">
      <div v-loading="loading">
        <div class="gzh-list flex-between flex-align-center">
          <gzh-name :show-list="true" />
          <div class="btns">
            <el-button type="primary" @click="exportPdf">
              <i class="iconfont icon-daochu" />
              导出
            </el-button>
          </div>
        </div>
        <div class="yesterday-data mt-20 border">
          <div class="p-title">昨日关键数据</div>
          <statistics-list :data="yesterdayData"/>
        </div>
      </div>
      <div class="analysis mt-20 border" v-loading="chartLoading">
        <div class="p-title">内容分析</div>
        <div class="filter-box mt-20">
          <el-form :model="form" label-position="left" label-width="100px">
            <el-form-item label="数据类型">
              <radio-button-group v-model:value="form.contype" size="large" @change="changeDataType" :list="dataType" type="primary" />
            </el-form-item>
            <el-form-item label="时间选择">
              <div style="width: 300px">
                <el-date-picker v-model="form.datedate" :disabled-date="disabledDate" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="YYYY-M-D" />
              </div>
            </el-form-item>
            <el-form-item label="渠道">
              <radio-button-group v-model:value="form.contypetwo" size="large" :list="channel" type="default" />
            </el-form-item>
          </el-form>
        </div>
        <div class="content-chart w100">
          <line-chart :colors="contentSettings.colors" width="100%" height="500px" :data="contentData.data" :x-axis-data="contentData.date" />
        </div>
      </div>
      <div class="channel mt-20 border" v-loading="channelLoading">
        <div class="p-title">渠道构成</div>
        <div class="filter-box mt-20">
          <el-form :model="channelForm" label-position="left" label-width="100px">
            <el-form-item label="数据类型">
              <radio-button-group v-model:value="channelForm.qdtype" size="large" :list="channelComposition" type="primary" @change-data="getChannelData" />
            </el-form-item>
          </el-form>
        </div>
        <div class="content-chart w100">
          <pie-chart :colors="channelSettings.colors" width="100%" height="500px" :data="channelData" name="渠道构成" />
        </div>
      </div>
      <div class="user mt-20 border" v-loading="userLoading">
        <div class="p-title">用户分析</div>
        <div class="filter-box mt-20">
          <el-form :model="userForm" label-position="left" label-width="100px">
            <el-form-item label="类型">
              <radio-button-group v-model:value="userForm.datatype" size="large" :list="userDataOptions" type="primary" @change-data="getUserData" />
            </el-form-item>
          </el-form>
        </div>
        <div class="content-chart w100">
          <line-chart :colors="contentSettings.colors" width="100%" height="500px" :data="userData.data" :x-axis-data="userData.date" />
        </div>
      </div>
    </div>
  </div>

  <div id="export" :style="`background: #fff;width: ${pdfWidth}; margin-top: 200px;`">
    <div
        class="page-1"
        :style="`width: ${pdfWidth};height: ${pdfHeight};display: flex;flex-direction: column;align-items: center;justify-content: center;page-break-after: always;`"
    >
      <h1 class="title" style="font-weight: bold; font-size: 52px;">
        微信公众号运营报告
      </h1>
      <h2 style="font-weight: bold; margin-top: 20px; font-size: 26px;">
        公众号名称：{{ gzhInfo.title }}
      </h2>
    </div>
    <pdf-page>
      <template #textInfo>
        <div style="padding: 50px;text-align: left;">
          <p style="font-size: 18px;">昨日关键数据（{{ gzhInfo.ttdate }}）</p>
        </div>
        <div style="padding: 0 30px">
          <yesterday-list :data="yesterdayData"/>
        </div>
      </template>
    </pdf-page>
    <pdf-page>
      <template #textInfo>
        <export-text title="内容分析" :list="contentList" />
      </template>
      <template #canvas>
        <div style="width: 100%; margin-top: 20px; padding: 0 30px">
          <line-chart :colors="contentSettings.colors" width="100%" height="500px" :data="contentData.data" :x-axis-data="contentData.date" />
        </div>
      </template>
    </pdf-page>
    <pdf-page>
      <template #textInfo>
        <export-text title="渠道构成" :list="channelCompositionList" />
      </template>
      <template #canvas>
        <div style="width: 100%; margin-top: 20px; padding: 0 30px">
          <pie-chart :colors="channelSettings.colors" width="100%" height="500px" :data="channelData" name="渠道构成" />
        </div>
      </template>
    </pdf-page>
    <pdf-page>
      <template #textInfo>
        <export-text title="用户分析" :list="userDataList" />
      </template>
      <template #canvas>
        <div style="width: 100%; margin-top: 20px; padding: 0 30px">
          <line-chart :colors="contentSettings.colors" width="100%" height="500px" :data="userData.data" :x-axis-data="userData.date" />
        </div>
      </template>
    </pdf-page>
  </div>
</div>
</template>

<style scoped lang="scss">
@import "src/views/media/report/gzh/style/common";
.wrap{
  overflow: auto;
  .content-box {
    h500{
      height: 500px;
    }
  }
}
</style>
