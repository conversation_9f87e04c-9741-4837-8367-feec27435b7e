<template>
  <el-form ref="editFormRef" class="content-form w100 h100" :model="formData" :rules="rules" label-width="auto">
    <template v-if="type == 'video'">
      <el-form-item class="flex-y mb10" label="标题：" prop="title">
        <el-input v-model="formData.title" placeholder="请输入标题(5-50字)" maxlength="50" show-word-limit />
      </el-form-item>
      <el-form-item class="flex-y mb10" label="简介：" prop="content">
        <el-input v-model="formData.content" type="textarea" :rows="3" placeholder="请输入简介" maxlength="200" show-word-limit resize="none" />
      </el-form-item>
      <div class="flex-x">
        <el-form-item class="flex-y" label="封面：" prop="cover">
          <upload-image :uploadBtn="true" width="200px" height="354px" from="media" position="right-start" v-model:imageIntro="formData.cover" />
        </el-form-item>
        <el-form-item class="flex-y ml20" label="视频：" prop="video">
          <upload-image :uploadBtn="true" width="200px" height="354px" from="media" type="video" position="right-start" v-model:imageIntro="formData.video">
            <template #button>
              <jzt-button class="mt10" name="我的视频选择"></jzt-button>
            </template>
          </upload-image>
        </el-form-item>
      </div>
    </template>
    <template v-else>
      <el-form-item label="选择内容导入：">
        <el-popover ref="popoverRef" placement="bottom" width="100%" trigger="click" :teleported="false">
          <template #reference>
            <el-input class="cursor-pointer" v-model="importData.title" placeholder="请选择内容导入" readonly>
              <template #suffix>
                <el-icon><ArrowDown /></el-icon>
              </template>
            </el-input>
          </template>
          <data-list ref="dataListRef" class="w100 height400 form-table" :showTop="false" :tableConfig="tableConfig" @changeCurrent="changeCurrent"></data-list>
        </el-popover>
      </el-form-item>
      <div class="flex-x">
        <el-form-item class="flex-y mb10" label="封面：" prop="cover">
          <upload-image :uploadBtn="true" width="210px" height="130px" from="media" v-model:imageIntro="formData.cover" />
        </el-form-item>
        <div class="flex-1 ml20">
          <el-form-item class="flex-y mb10" label="标题：" prop="title">
            <el-input v-model="formData.title" placeholder="请输入标题(5-50字)" maxlength="50" show-word-limit />
          </el-form-item>
          <el-form-item class="flex-y mb10" label="作者：" prop="author">
            <el-input v-model="formData.author" placeholder="请输入作者" />
          </el-form-item>
          <!-- 单选 是否原创 -->
          <el-form-item class="mb1" label="是否原创：" prop="is_original">
            <el-radio-group v-model="formData.is_original">
              <el-radio :value="1">原创</el-radio>
              <el-radio :value="0">非原创</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
      </div>
      <el-form-item class="flex-y mb10" label="简介：" prop="content">
        <el-input v-model="formData.content" type="textarea" :rows="3" placeholder="请输入简介" maxlength="200" show-word-limit resize="none" />
      </el-form-item>
      <el-form-item class="flex-y mb10" label="详情：" prop="details">
        <tinymce-editor :height="500" v-model:content="formData.details" />
      </el-form-item>
    </template>
    <send-info-view ref="sendInfoRef" :type="type" />
  </el-form>
</template>

<script setup lang="ts">
import { ref, defineAsyncComponent, watch, computed } from 'vue'
import { ElLoading, ElMessage } from 'element-plus'
import { useDataList } from '@/hooks/useDataList'
import { contentListApi } from '@/api/content'
import { saveVideoApi, saveArticleApi } from '@/api/media'
import {ArrowDown} from "@element-plus/icons-vue";

const { DataList, dataListRef } = useDataList()

const UploadImage = defineAsyncComponent(() => import('@/components/Uploads/uploadImg.vue'))
const TinymceEditor = defineAsyncComponent(() => import('@/components/Global/TinymceEditor.vue'))

const SendInfoView = defineAsyncComponent(() => import('./SendInfo.vue'))
const sendInfoRef = ref()

const props = defineProps({
  form: {
    type: Object as any,
    default: () => ({})
  },
  type: {
    type: String,
    default: 'video'
  },
  checkList: {
    type: Array as any,
    default: () => []
  }
})

// 选择内容导入
const tableConfig = ref({
  api: contentListApi,
  apiType: 'Contentnew',
  showIndex: false,
  showMultiple: false,
  isSingle: true,
  columns: [
    { label: '标题', prop: 'title' },
    { label: '添加时间', prop: 'created', width: 180, sortable: true }
  ]
})

const popoverRef = ref()
const importData:any = ref({})
const changeCurrent = (val: any) => {
  console.log(val, 'changeCurrent')
  formData.value.title = val.title
  formData.value.cover = val.image_intro
  formData.value.content = val.introtext
  formData.value.details = val.fulltext
  importData.value = val
  popoverRef.value.hide()
}

const emit = defineEmits(['success'])

const formConfig:any = {
  'video': {
    api: saveVideoApi,
    defaultVal: { title: '', content: '', cover: '', video: '' },
    rules: {
      title: [
        { required: true, message: '请输入标题', trigger: 'blur' },
        { min: 5, max: 50, message: '请输入5-50字', trigger: 'blur' }
      ],
      content: [
        { required: true, message: '请输入简介', trigger: 'blur' },
        { min: 0, max: 200, message: '请输入0-200字', trigger: 'blur' }
      ],
      cover: [
        { required: true, message: '请选择或上传封面', trigger: 'blur' }
      ],
      video: [
        { required: true, message: '请选择或上传视频', trigger: 'blur' }
      ]
    }
  },
  'article': {
    api: saveArticleApi,
    defaultVal: { cover: '', title: '', author: '', is_original: 1, content: '', details: '' },
    rules: {
      title: [
        { required: true, message: '请输入标题', trigger: 'blur' },
        { min: 5, max: 50, message: '请输入5-50字', trigger: 'blur' }
      ],
      cover: [
        { required: true, message: '请选择或上传封面', trigger: 'blur' }
      ],
      is_original: [
        { required: true, message: '请选择是否原创', trigger: 'blur' }
      ],
      details: [
        { required: true, message: '请输入详情', trigger: 'blur' }
      ],
    }
  }
}

// 简介是否必填
const isContentRequired = computed(() => {
  return props.checkList.filter((item:any) => item.types == 3).length > 0
})

// 作者是否必填
const isAuthorRequired = computed(() => {
  return props.checkList.filter((item:any) => item.types == 7).length > 0
})

const formData:any = ref({})

watch(() => props.form, (val:any) => {
  console.log(val, 'val-content')
  if (val) {
    console.log(formConfig[props.type].defaultVal, 'formConfig[props.type].defaultVal')
    for (let key in formConfig[props.type].defaultVal) {
      formData.value[key] = val[key]
    }
    formData.value.id = val.id
  } else formData.value = JSON.parse(JSON.stringify(formConfig[props.type].defaultVal))
  importData.value = {}
}, { immediate: true })

const rules = ref({})

// 保存
const editFormRef = ref()
const saveInfo = (type:string = 'save') => {
  return new Promise((resolve, reject) => {
    editFormRef.value.validate((valid:boolean) => {
      if (valid) {
        if (isAuthorRequired.value && !formData.value.author) {
          ElMessage.error('请输入作者')
          if (type == 'send') reject('error')
          return
        }
        if (isContentRequired.value && !formData.value.content) {
          ElMessage.error('请输入简介')
          if (type == 'send') reject('error')
          return
        }
        const loading = ElLoading.service({
          lock: true,
          text: '保存中...',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        formConfig[props.type].api(formData.value).then((res:any) => {
          console.log(res)
          ElMessage.success('保存成功')
          if (type == 'save') emit('success')
          resolve(res)
        }).catch((err:any) => {
          reject(err)
        }).finally(() => {
          loading.close()
        })
      }
    })
  })
}

// 发布
const sendInfo = async (accountList:any) => {
  console.log('点了发布')
  const res = await saveInfo('send')
  console.log(res)
  sendInfoRef.value.sendInfo(accountList, { ...formData.value, id: res })
}

watch(() => props.type, (val:string) => {
  formData.value = JSON.parse(JSON.stringify(formConfig[val].defaultVal))
  rules.value = JSON.parse(JSON.stringify(formConfig[val].rules))
}, { immediate: true })

defineExpose({ saveInfo, sendInfo })

</script>

<style scoped lang="scss">
.content-form {
  overflow-y: auto;
  :deep(.material-upload) {
    padding: 0 6px;
  }
  :deep(.el-form-item) {
    .el-form-item__label-wrap {
      margin-left: 0 !important;
    }
    .cursor-pointer {
      .el-input__inner {
        cursor: pointer;
      }
    }
  }
}
:deep(.el-popper) {
  .form-table {
    .table-box {
      margin: 0 !important;
    }
    .page-box {
      height: 40px;
    }
  }
}
</style>
