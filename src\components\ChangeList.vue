<script setup lang="ts">
interface Props {
  title: string;
  list: {
    label: string;
    selected: boolean;
    id: number|string;
    [key: string]: any;
  }[]
}

const props = defineProps<Props>();

const emits = defineEmits(['loadMore', 'change']);
const load = () => {
  emits('loadMore');
}

const changeData = (item: any) => {
  props.list.forEach((v: any) => {
    v.selected = false;
  });
  item.selected = true;
  emits('change', item);
}
</script>

<template>
  <div class="change-btn flex" style="padding-right: 8px;" v-if="list.length">
    <img src="@/assets/images/index/icon01.png" alt="">
    <span>{{ title }}</span>

    <div class="site-list">
      <ul v-infinite-scroll="load">
        <li v-for="(item, index) in list" :key="index" @click="() => changeData(item)">
          <slot name="default" :item="item" v-if="$slots.default"></slot>
          <a v-else :class="{selected: item.selected}">{{ item.label }}</a>
        </li>
      </ul>
    </div>
  </div>
</template>

<style scoped lang="scss">
.change-btn {
  height: 34px;
  background: #ffffff;
  box-shadow: 0px 4px 10px 0px rgba(225, 231, 238, 0.5);
  border-radius: 16px;
  margin-left: 20px;
  font-size: 14px;
  font-weight: bold;
  color: #636ea7;
  padding-left: 4px;
  box-sizing: border-box;
  position: relative;
  cursor: pointer;
  transition: all ease-in-out 0.3s;
  .site-list{
    display: none;
    position: absolute;
    top: 100%;
    z-index: 999;
    left: 0;
    right: 0;
    padding-top: 10px;
    ul {
      max-height: 800px;
      background: #FFFFFF;
      box-shadow: 0px 0px 10px 0px rgba(225, 231, 238, .5), 0px 4px 10px 0px rgba(225, 231, 238, .5);
      border-radius: 2px;
      height: 200px;
      overflow-y: auto;
      li{
        list-style: none;
        text-align: right;
        height: 40px;
        line-height: 40px;
        padding: 0 20px;
        a{
          font-size: 12px;
          font-weight: 500;
          color: #7F8294;
          display: block;
          width: 100%;
          transition: all ease-in-out 0.3s;
          &.selected {
            font-size: 16px;
            color: rgb(99, 110, 167);
            font-weight: bold;
          }
        }
      }
    }
  }
  &:hover {
    background-color: #f3f6ff;
    .site-list {
      display: block;
      overflow: auto;
    }
  }
  span {
    margin-left: 5px;
  }
}
</style>
