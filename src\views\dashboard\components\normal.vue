<template>
  <div class="flex-wrap" style="align-items: flex-start">
    <!--登录时间 剩余时长-->
    <div class="content-box part01 col-01 row-01">
      <div class="item" style="margin-bottom: 8px">
        <span class="key">更新日志：</span>
        <swiper v-if="updateLog.length > 0" direction="vertical" :slides-per-view="3" :space-between="0" :speed="500"
          :loop="true" :modules="modules" :autoplay="autoplay">
          <swiper-slide v-for="(item, index) in updateLog" :key="index">
            <router-link to="/system/update" v-html="item.content" />
          </swiper-slide>
        </swiper>
      </div>
      <div class="item flex-between">
        <span class="key">上次登录时间：</span>
        <span class="value">{{ dateInfo?.last_login }}</span>
      </div>
    </div>
    <!--媒体管理 -->
    <media-view class="part02 col-02 row-01" title="媒体管理" :column="3" />
    <!--最近使用记录-->
    <div class="content-box part03 col-03 row-01">
      <p class="new-title">最近使用</p>
      <div class="item-box flex-wrap" style="justify-content: flex-start">
        <template v-for="(item, index) in menuLog" :key="index">
          <router-link v-if="index < 6 && item" :to="item.path" class="item" style="width: calc(100% / 2 - 10px); margin-right: 10px">{{
            item.title
          }}
          </router-link>
        </template>
      </div>
    </div>
    <!--网站使用报告-->
    <website-usage-report class="part04 col-01 row-02" />
    <!--各端二维码-->
    <q-r-code-view class="part05 col-02 row-02" />
    <!--常用菜单-->
    <div class="content-box part03 col-03 row-02">
      <p class="new-title">常用操作</p>
      <div class="item-box flex-wrap">
        <template v-for="(item, index) in normalLog" :key="index">
          <router-link class="item" :to="item.path">{{
            item.title
          }}</router-link>
        </template>
      </div>
    </div>
    <!--空间占比-->
    <div class="content-box part06 part05 flex col-01 row-03">
      <div v-if="spaceInfo.count_type == 2" class="total-wrap flex-yx-center">
        <img src="@/assets/images/index/icon7.png" alt="">
        <div class="total">{{ spaceInfo.bucket_count }}</div>
        <div class="tips">网站空间已使用（{{ spaceInfo.bucket_dw || 'M' }}）</div>
      </div>
      <template v-if="spaceInfo.count_type == 1">
        <space-pie :data="spaceInfo" width="180px" height="208px"></space-pie>
        <div class="right-text">
          <div class="text-item">
            <div class="t-title">网站空间</div>
            <div class="t-num">{{ spaceInfo?.used_count }}</div>
            <div class="t-p">{{ spaceInfo?.used_percent }}</div>
          </div>
          <div class="text-item">
            <div class="t-title">素材库</div>
            <div class="t-num">{{ spaceInfo?.bucket_count }}</div>
            <div class="t-p">{{ spaceInfo?.bucket_percent }}</div>
          </div>
        </div>
      </template>
    </div>
    <!--访问量统计-->
    <div class="content-box part07 col-04 row-03">
      <router-link class="more flex" to="/site/visit">查看更多&nbsp;&nbsp;
        <img src="@/assets/images/index/right-.png" alt=""></router-link>
      <visit-line :data="visitInfo" width="100%" height="300px"></visit-line>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, defineAsyncComponent } from "vue";
import { latelyUse, commonUse, siteUsage, siteCount } from "@/api/site";

import { Swiper, SwiperSlide } from "swiper/vue";
import { Pagination, Autoplay } from "swiper/modules";
import "swiper/css";
const modules = [Autoplay];
const autoplay:any = ref({ delay: 3000, disableOnInteraction: false })

import useDashboard from '@/views/dashboard/hooks/useDashboard'
const { updateLog, getUpdate, WebsiteUsageReport, QRCodeView, MediaView, dateInfo, getLastTime } = useDashboard()

/* 导入组件 */
const SpacePie = defineAsyncComponent(() => import("@/components/ECharts/SpacePie.vue"));
const VisitLine = defineAsyncComponent(() => import("@/components/ECharts/VisitLine.vue"));

// 获取控制台数据
const initDashboard = () => {
  getUpdate();
  getLastTime();
  getLatelyUse();
  getCommon();
  getSiteUsage();
  getVisits()
};

// 获取最近使用操作
const menuLog:any = ref([]);
const getLatelyUse = () => {
  latelyUse().then((data: any) => {
    menuLog.value = data;
  });
};

// 获取常用操作
const normalLog:any = ref([]);
const getCommon = () => {
  commonUse().then((data: any) => {
    normalLog.value = data;
  });
};

// 获取网站使用空间
const spaceInfo = ref<any>({});
const getSiteUsage = () => {
  siteUsage().then((data: any) => {
    spaceInfo.value = data;
  })
}

const visitInfo = ref({})
// 获取访问统计
const getVisits = () => {
  siteCount({ typevalue: 2 }).then((data: any) => {
    visitInfo.value = data;
  })
}

// 跳转外链
const toPath = (url: string) => {
  window.open(url, '_blank')
}

onMounted(() => {
  initDashboard();
});

</script>

<style lang="scss" scoped>
@import "../style/index.scss";
</style>