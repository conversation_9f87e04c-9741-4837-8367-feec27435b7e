<script setup lang="ts">
import DataTable from "@/components/Global/DataTable.vue";
import {getSiteOverviewApi} from "@/api/siteManage";
import {markRaw, ref} from "vue";

const statusMap = markRaw<any>({
  1: '待审核',
  2: '审核通过',
  3: '审核拒绝',
  4: '已到期',
})

const tableConfig = ref({
  api: getSiteOverviewApi,
  columns: [
    {
      label: '名称',
      prop: 'web_name',
    },
    {
      label: '域名',
      prop: 'domain',
      type: 'slot',
    },
    {
      label: '制作人',
      prop: 'producer',
      width: 120,
    },
    {
      label: '状态',
      prop: 'shzt',
      type: 'slot',
      width: 120,
    },
    {
      label: '时间',
      prop: 'create_time',
      sortable: 'custom',
      width: 150,
    },
    {
      label: 'SSL证书',
      prop: 'is_ssl',
      sortable: 'custom',
      type: 'slot',
      width: 130,
    },
    {
      label: '操作',
      prop: 'action',
      type: 'slot',
      width: 130,
    }
  ]
})

const interview = (row: any) => {
  if(!row.domain) return
  if(row.domain.indexOf('http') === -1 || row.domain.indexOf('https') === -1) {
    row.domain = 'http://' + row.domain
  }
  window.open(row.domain)
}
</script>

<template>
  <div class="h100 wrap">
    <data-table :show-multiple="false" :columns="tableConfig.columns" :api="tableConfig.api">
      <template #domain="{row}">
        <span v-if="row.domain">{{ row.domain }}</span>
        <span v-else>未绑定域名</span>
      </template>
      <template #shzt="{row}">
        <span>{{ statusMap[row.shzt] }}</span>
      </template>
      <template #is_ssl="{row}">
        <span v-if="row.is_ssl">已上传</span>
        <span v-else>未上传</span>
      </template>
      <template #action="{row}">
        <div class="table-action">
          <div class="item" @click="interview(row)" v-if="row.domain">
            <i class="iconfont icon-lianjie-"></i> <span>访问</span>
          </div>
        </div>
      </template>
    </data-table>
  </div>
</template>

<style scoped lang="scss">
.wrap {
  padding: 10px 0;
}
</style>
