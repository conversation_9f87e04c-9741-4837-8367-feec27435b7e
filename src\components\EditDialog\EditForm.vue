<template>
  <el-form ref="formRef" :model="formData" :rules="rules" :label-width="labelWidth" :label-position="align" class="demo-ruleForm">
    <template v-for="(item, index) in formList" :key="index">
      <el-form-item :label="item.label" :prop="item.prop">
        <el-select v-if="item.type === 'select'" v-model="formData[item.prop]" :placeholder="item.placeholder"
          style="width: 100%;">
          <el-option v-for="(option, index) in item.options" :key="index" :label="option[item.labelKey || 'label']"
            :value="option[item.valueKey || 'value']"></el-option>
        </el-select>
        <el-cascader v-else-if="item.type === 'cascader'" style="width: 100%;" v-model="formData[item.prop]"
          :options="item.options" :props="itemProps(item)">
          <template #default="{ node, data }">
            <span>{{ data[item.labelKey || 'label'] }}</span>
            <template v-if="!node.isLeaf">
              <span v-if="data[item.childrenKey || 'children']"> ({{ data[item.childrenKey || 'children'].length }}) </span>
            </template>
          </template>
        </el-cascader>
        <el-input-number v-else-if="item.type === 'number'" v-model="formData[item.prop]"
          :placeholder="item.placeholder" :min="0" :max="9999999" :step="1"></el-input-number>
        <el-date-picker v-else-if="item.type === 'date'" v-model="formData[item.prop]" :placeholder="item.placeholder"  value-format="YYYY-MM-DD" format="YYYY-MM-DD"
          type="date" style="width: 100%;"></el-date-picker>
        <el-date-picker v-else-if="item.type === 'datetime'" v-model="formData[item.prop]"
          :placeholder="item.placeholder" type="datetime"></el-date-picker>
        <el-switch v-else-if="item.type === 'switch'" v-model="formData[item.prop]" :placeholder="item.placeholder"
          active-color="#13ce66" inactive-color="#ff4949" :active-value="1" :inactive-value="0"></el-switch>
        <el-input v-else-if="item.type === 'textarea'" v-model="formData[item.prop]" :placeholder="item.placeholder"
          type="textarea"></el-input>
        <upload-img v-else-if="item.type === 'uploadImg'" v-model:imageIntro="formData[item.prop]" :placeholder="item.placeholder"
          v-bind="item.uploadConfig" style="width: 100%;"></upload-img>
        <el-input v-else v-model="formData[item.prop]" :placeholder="item.placeholder"
          :type="item.inputType"></el-input>
        <p v-if="item.tips" style="color: #ff4949" v-html="item.tips"></p>
      </el-form-item>
    </template>
  </el-form>
</template>

<script setup lang="ts">
import { ref, computed, watch, defineAsyncComponent } from 'vue';
import { FormInstance } from 'element-plus';

const UploadImg = defineAsyncComponent(() => import('@/components/Uploads/uploadImg.vue'))

const props = defineProps({
  align: {
    type: String,
    default: 'right'
  },
  labelWidth: {
    type: String,
    default: '100px'
  },
  formList: {
    type: Array as any,
    default: () => []
  },
  data: {
    type: Object as any,
    default: () => {}
  }
})

const emit = defineEmits(['update:data'])

const formRef = ref<FormInstance>()

const formData: any = ref(props.data)
props.formList.forEach((item: any) => {
  if (props.data[item.prop] != undefined && props.data[item.prop] != null) formData.value[item.prop] = props.data[item.prop]
  else formData.value[item.prop] = item.value
})
watch(() => formData.value, (newVal: any) => {
  console.log(newVal, 'formData-edit-form')
  emit('update:data', newVal)
})

const rules = computed(() => {
  let rule:any = {}
  props.formList.forEach((item: any) => {
    if (item.required) {
      let message = item.placeholder || `${item.label}不能为空`
      rule[item.prop] = [
        { required: true, message, trigger: 'blur' }
      ]
    }
  })
  return rule
})

const itemProps = (item: any) => {
  return {
    label: item.labelKey || 'label',
    value: item.valueKey || 'value',
    children: item.childrenKey || 'children',
    emitPath: false,
    checkStrictly: true,
    expandTrigger: 'hover',
    clearable: true,
    style: 'width: 100%'
  }
}

const submitForm = () => {
  return new Promise((resolve, reject) => {
    if (!formRef.value) return reject(false)
    formRef.value.validate((valid:any) => {
      if (valid) {
        resolve(true)
      } else {
        console.log('error submit!')
        reject(false)
      }
    })
  })
}

const resetForm = () => {
  const formEl = formRef.value
  if (!formEl) return
  formEl.resetFields()
}

watch(() => props.data, (newVal: any) => {
  console.log(newVal, 'props.data')
  props.formList.forEach((item: any) => {
    if (newVal[item.prop] != undefined && newVal[item.prop] != null) formData.value[item.prop] = newVal[item.prop]
    else formData.value[item.prop] = item.value
  })
}, { deep: true, immediate: true })

defineExpose({ submitForm, resetForm })
</script>

<style scoped lang="scss">

</style>