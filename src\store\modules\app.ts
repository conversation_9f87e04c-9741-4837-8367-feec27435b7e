import { getMenuApi, getUserIpApi } from '@/api/app'
import { getNavigationLeftListApi } from '@/api/navigation';
import { useRouter } from 'vue-router';

const state = {
  menuList: [],
  // 展开的菜单
  openMenuList: [],
  userIp: '',
  // 导网站导航列表
  navigationList: [],
  // 左侧菜单
  leftMenuList: []
}

const mutations = {
  SET_MENU_LIST(state: any, menuList: any) {
    state.menuList = menuList
  },
  SET_OPEN_MENU_LIST(state: any, openMenuList: any) {
    state.openMenuList = openMenuList
  },
  SET_USER_IP(state: any, userIp: string) {
    state.userIp = userIp
  },
  SET_NAVIGATION_LIST(state: any, navigationList: any) {
    state.navigationList = navigationList
  },
  SET_LEFT_MENU_LIST(state: any, {result, routes}: any) {
    routes.forEach((item: any) => {
      if (item.name == 'home') {
        let navList = item.children.filter((item: any) => item.meta && item.meta.isNav)
        navList = navList.map((item: any) => ({ ...item, pid: 0 }))
        navList.forEach((child: any) => {
          if (child.name == 'index') child.children = result
        })
        state.leftMenuList = navList
      }
    })
  }
}

const actions = {
  getMenuList({ commit, dispatch }: any) {
    return new Promise<void>(async (resolve:any, reject) => {
      const result = await getMenuApi()
      // console.log(result)
      commit('SET_MENU_LIST', result)
      dispatch('setOpenMenuList', result)
      resolve(result)
    })
  },
  setOpenMenuList({ commit, state }: any, menu: any) {
    const openMenuList:any = []
    const findMenu = (menuList: any) => {
      for (const item of menuList) {
        openMenuList.push(item)
        if (item.children && item.children.length > 0) {
          findMenu(item.children)
        }
      }
    }
    findMenu(menu)
    commit('SET_OPEN_MENU_LIST', openMenuList)
  },
  getUserIp({ commit }: any) {
    // https://baota.baina666.com/api/ip/ip
    return new Promise<void>(async (resolve:any, reject) => {
      if (state.userIp) return resolve()
      const result = await getUserIpApi()
      console.log(result)
      commit('SET_USER_IP', result)
      resolve()
    })
  },
  getNavigationList({ commit }: any, routes: any) {
    return new Promise<void>(async (resolve:any, reject) => {
      const result = await getNavigationLeftListApi()
      commit('SET_NAVIGATION_LIST', result)
      commit('SET_LEFT_MENU_LIST', {result, routes})
      resolve(result)
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
