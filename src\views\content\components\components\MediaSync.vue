<template>
  <div class="table-action async">
    <div :class="['item flex-c-center', {'none': row.article_id == 0 || row.article_id == null}]" @click="syncMedia(row, 'bjh')">
      <img src="@/assets/images/system/icon05.png"/>&nbsp;<span>{{ (row.article_id == 0 || row.article_id == null) ? '同步至百家号' : '已同步百家号' }}</span>
    </div>
    <div :class="['item flex-c-center', {'none': row.media_id == 0 || row.article_id == null}]" @click="syncWx(row)">
      <img src="@/assets/images/system/icon06.png"/>&nbsp;<span>{{ (row.media_id == 0  || row.article_id == null) ? '同步至公众号' : '已同步公众号' }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus';
import store from '@/store';
import { getAccountListApi, syncToBaiJiaApi } from '@/api/media/account';
import { useContent } from '../../hooks';

const { getConfigList, configList } = useContent()

const props = defineProps({
  row: {
    type: Object,
    default: () => {}
  }
})

const emit = defineEmits(['seletAccount', 'syncWx', 'refresh'])

const isFree = computed(() => store.getters.isFree)

// 同步百家号
const syncMedia = async (row: any, type: string) => {
  if (isFree.value) return ElMessage.error('免费版暂不支持此功能，如需升级请联系商务')
  if (row.article_id == 0 || row.article_id == null) {
    getConfigList(2).then(res => {
      if (configList.value.length == 0) {
        ElMessage.error('请先去媒体管理--账号管理添加账号')
      } else if (configList.value.length > 1) {
        emit('seletAccount', {
          type, list: configList.value, id: row.id
        })
      } else {
        layer_alert(configList.value[0].id, row.id)
      }
    })
  } else ElMessage.warning('已经同步过了，换个文章试试')
}

// 执行同步百家号方法
const layer_alert = (account: number, id: number) => {
  let params:any = { id }
  if (account) params.config_id = account;
  ElMessageBox.confirm( '确定要同步吗？', '温馨提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    let loading = ElLoading.service({
      lock: true,
      text: '同步中...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    syncToBaiJiaApi(params).then(res => {
      loading.close()
      ElMessage.success('同步成功')
      emit('refresh')
    }).catch(err => {
      loading.close()
    })
  })
}

// 同步微信公众号
const syncWx = (row: any) => {
  console.log(row)
  if (isFree.value) return ElMessage.error('免费版暂不支持此功能，如需升级请联系商务')
  if (row.media_id == 0) {
    emit('syncWx', row)
  } else ElMessage.warning('已经同步过了，换个文章试试')
}

defineExpose({ layer_alert })
</script>

<style scoped lang="scss">
.table-action.async {
  .item {
    margin: auto;
    margin-right: 0;
    width: 127px;
    height: 30px;
    background: #FFFFFF;
    color: #2859FF;
    border: 1px solid #97BFFF;
    border-radius: 2px;
    margin-bottom: 7px;
    img {
      display: inline-block;
    }
    &.none {
      border: 1px solid #E4E9F3;
      background: #E4E9F3;
      color: #636EA7;
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>