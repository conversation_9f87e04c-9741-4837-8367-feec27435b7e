<template>
  <!--网站使用报告-->
  <div class="content-box report-container">
    <p class="new-title flex-between">
      <span class="s-name">网站使用报告</span>
      <!-- <a href="/newclient/report/index" class="more flex">查看更多&nbsp;&nbsp;
        <img :src="getIndexAssetsFile('right-.png')" alt=""></a> -->
    </p>
    <div class="tabs-box flex-between">
      <div :class="['item', { active: reportDate == 'day' }]" @click="reportDate = 'day'">
        昨日统计
      </div>
      <div :class="['item', { active: reportDate == 'month' }]" @click="reportDate = 'month'">
        按月统计
      </div>
      <div :class="['item', { active: reportDate == 'year' }]" @click="reportDate = 'year'">
        按年统计
      </div>
    </div>
    <div class="item-box flex-wrap">
      <template v-for="(item, index) in statisticsList" :key="index">
        <div class="item flex">
          <img :src="require('@/assets/images/index/bg0' + (index + 1) + '.png')" alt="" />
          <div class="content-b">
            <p class="s-name">{{ item.name }}</p>
            <p class="value">
              {{ item.num }}
              <span class="unit">{{ item.unit }}</span>
            </p>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted } from 'vue'
import useDashboard from '@/views/dashboard/hooks/useDashboard'

const { statisticsList, reportDate, getSiteReport } = useDashboard()

onMounted(() => {
  getSiteReport()
})

</script>

<style lang="scss" scoped>
/*网站使用报告*/
.report-container {
  .new-title {
    margin-bottom: 20px;
    .name {
      margin-top: 0;
    }
    .more {
      font-size: 12px;
      color: #7f8294;
    }
  }
  .tabs-box {
    justify-content: space-around;
    .item {
      width: 100px;
      height: 28px;
      text-align: center;
      line-height: 28px;
      background: #ffffff;
      border: 1px solid #e3e5ec;
      border-radius: 14px;
      font-size: 12px;
      /*font-family: PingFang SC;*/
      font-weight: 500;
      color: #7f8294;
      transition: all ease-in-out 0.3s;
      cursor: pointer;
      &.active {
        background: var(--jzt-color-main);
        box-shadow: 0px 4px 10px 0px rgba(40, 89, 255, 0.2);
        color: #ffffff;
        &:hover {
          background: var(--jzt-color-main);
          box-shadow: 0px 4px 10px 0px rgba(40, 89, 255, 0.2);
          color: #ffffff;
        }
      }
      &:hover {
        border-color: #a0b6ff;
        color: var(--jzt-color-main);
      }
    }
  }
  .item-box {
    .item {
      width: calc(100% / 2);
      margin-top: 35px;
      img {
        margin-right: 10px;
      }
      .s-name {
        font-size: 14px;
        color: #696c7d;
      }
      .value {
        font-size: 18px;
        /*font-family: PangMenZhengDao;*/
        font-weight: bold;
        color: #0e1626;
        .unit {
          font-size: 14px;
        }
      }
    }
  }
}
</style>