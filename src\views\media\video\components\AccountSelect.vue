<template>
  <div class="account-select-wrap">
    <div class="flex-between">
      <p class="s-r-t">账号选择</p>
      <div class="add-account-btn-box">
        <div class="flex add-account-btn">
          <i class="iconfont icon-tianjia"></i>
          <span>添加授权账号</span>
        </div>
        <div class="add-account-alt-box">
          <div class="add-account-alt">
            <template v-for="(item, index) in typeList">
              <div class="item flex-between" @click="addAccount(index)">
                <div class="icon-text">
                  <span>{{ item.title }}</span>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
    <div class="s-account-box">
      <template v-if="accountList.length > 0">
        <div class="s-a-top flex">
          <el-checkbox v-model="accountCheckAll" @change="itemCheckAll">全选</el-checkbox>
        </div>
        <div class="s-a-list">
          <div class="s-a-item flex" v-for="(item, index) in accountList">
            <el-checkbox v-model="item.isCheck"></el-checkbox>
            <div class="item-info flex-1 flex ml10" @click="itemCheck(index)">
              <img :src="item.avatar" class="avatar" alt="">
              <div class="info-box">
                <p class="info-user">{{ item.title }}</p>
                <p class="info-type">{{ item.type }}</p>
              </div>
            </div>
          </div>
        </div>
      </template>
      <template v-else>
        <div class="no-account">
          <img src="@/assets/images/media/no-account.png" alt="">
          <p class="no-tips">当前暂无已授权账号</p>
        </div>
      </template>
    </div>
    <div class="btn-box flex-end mt10">
      <jzt-button class="mr10" name="取消" @click="cancel"></jzt-button>
      <jzt-button class="mr10" name="保存" @click="saveInfo"></jzt-button>
      <jzt-button v-if="accountList.length > 0" class="active" name="发布" icon="icon-fabu" @click="sendInfo"></jzt-button>
    </div>
    <add-account-view ref="addAccountRef" :accountTypeList="typeList" :showAccountList="false" @success="getAccountList"></add-account-view>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineAsyncComponent, computed, watch } from 'vue'
import { accountTypeApi, accountListsApi } from '@/api/media/account'

const AddAccountView = defineAsyncComponent(() => import('@/views/media/account/components/AddAccount.vue'))
const addAccountRef = ref()

const props = defineProps({
  type: {
    type: String,
    default: 'video'
  },
  checkList: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['cancel', 'save', 'send', 'update:checkList'])

const typeList:any = ref([])

const accountList:any = ref([])
const accountCheckAll = ref(true)

// 获取平台类表
const getPlatformList = () => {
  let params = { type: props.type === 'video' ? 1 : 2 }
  accountTypeApi(params).then((res:any) => {
    typeList.value = res
  })
}

// 获取账号列表
const getAccountList = () => {
  let params = { type: props.type === 'video' ? 1 : 2 }
  accountListsApi(params).then((res:any) => {
    let list = JSON.parse(JSON.stringify(res))
    accountList.value = list.map((item:any) => ({ ...item, isCheck: true }))
  })
}

// 添加账号
const addAccount = (index:number) => {
  addAccountRef.value.addAccount(index)
}

// 选中账号
const itemCheck = (index:number) => {
  console.log('选中账号', index)
  accountList.value[index].isCheck = !accountList.value[index].isCheck
}

const itemCheckAll = (value:boolean) => {
  console.log('全选', value)
  accountList.value.forEach((item:any) => {
    item.isCheck = value
  })
}

const checkAccount = computed(() => accountList.value.filter((item:any) => item.isCheck))

const cancel = () => {
  console.log('取消')
  emit('cancel')
}

const saveInfo = () => {
  console.log('保存')
  emit('save', checkAccount.value)
}

const sendInfo = () => {
  console.log('发布', checkAccount.value)
  emit('send', checkAccount.value)
}

watch(() => checkAccount.value, (val:any) => {
  if (val.length > 0 && val.length == accountList.value.length) accountCheckAll.value = true
  else accountCheckAll.value = false
  emit('update:checkList', val)
})

onMounted(() => {
  getPlatformList()
  getAccountList()
})
</script>

<style scoped lang="scss">
.account-select-wrap {
  width: 260px;
  .s-r-t {
    margin-bottom: 10px;
  }
  .add-account-btn-box {
    position: relative;
    .add-account-btn {
      font-size: 14px;
      color: #2859FF;
      cursor: pointer;
      .iconfont {
        font-size: 14px;
        margin-right: 4px;
      }
    }
    .add-account-alt-box {
      position: absolute;
      width: 102px;
      height: max-content;
      top: calc(100% + 0px);
      display: none;
      padding-top: 6px;
      .add-account-alt {
        width: 100%;
        background: #FFFFFF;
        box-shadow: 0px 4px 10px 0px rgba(225, 231, 238, .5);
        border-radius: 3px;
        .item {
          cursor: pointer;
          line-height: 36px;
          padding: 0 5px;
          margin: 0 10px;
          font-size: 12px;
          color: #1C2B4B;
          border-bottom: solid 1px #F0F2F6;
          transition: all ease-in-out 0.3s;
          &:hover {
            color: #2859FF;
          }
          &:last-child {
            border-bottom: none;
          }
        }
      }
    }
    &:hover {
      .add-account-alt-box {
        display: block;
      }
    }
  }
  .s-account-box {
    border: 1px solid #E3E5EC;
    border-radius: 3px;
    height: 570px;
    .no-account {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .no-tips {
        font-size: 14px;
        color: #7F8294;
        margin-top: 20px;
      }
    }
    .s-a-top {
      height: 34px;
      background: #F6F8FB;
      border-bottom: 1px solid #E3E5EC;
      font-size: 14px;
      color: #7F8294;
      padding: 0 14px;
    }
    .s-a-list {
      margin: 16px 0;
      max-height: 500px;
      overflow-y: auto;
      .s-a-item {
        padding: 0 14px;
        margin-bottom: 16px;
        cursor: pointer;
        .item-info {
          width: calc(100% - 28px);
          .avatar {
            width: 56px;
            height: 56px;
            background: transparent;
            border-radius: 6px;
            margin-right: 10px;
            box-sizing: border-box;
          }
          .info-box {
            width: calc(100% - 66px);
            .info-user {
              font-size: 14px;
              font-weight: bold;
              color: #1C2B4B;
              margin-bottom: 5px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .info-type {
              font-size: 12px;
              color: #7F8294;
            }
          }
        }
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>