<template>
  <a v-if="href" :class="['material-upload', 'flex', size]" :href="href" :target="target">
    <i :class="['iconfont', props.icon]" v-if="props.icon" style="margin-right: 5px;"></i>
    <span>{{ props.name }}</span>
    <slot></slot>
  </a>
  <div v-else :class="['material-upload', 'flex', size]" @click="handleClick">
    <i :class="['iconfont', props.icon]" v-if="props.icon" style="margin-right: 5px;"></i>
    <span>{{ props.name }}</span>
    <slot></slot>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus';

const props = defineProps({
  name: {
    type: String,
    default: ''
  },
  icon: {
    type: String,
    default: ''
  },
  size: {
    type: String,
    default: ''
  },
  href: {
    type: String,
    default: ''
  },
  target: {
    type: String,
    default: ''
  },
  // 验证重复点击
  reClick: {
    type: Boolean,
    default: false
  },
  canClick: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['click']);

const handleClick = () => {
  if (props.reClick) {
    if (!props.canClick) return ElMessage.warning('请勿重复点击');
  }
  emit('click');
}

</script>

<style scoped lang="scss">
@import '@/assets/css/common.scss';
.material-upload {
  height: 34px;
  line-height: 34px;
  padding: 0 15px;
  background: #FFFFFF;
  border: 1px solid $borderColor;
  border-radius: 3px;
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: $mainColor;
  cursor: pointer;
  transition: all ease-in-out 0.3s;
  overflow: hidden;
  justify-content: center;
  &.large {
    min-width: 100px;
    padding: 0 12px;
    height: 34px;
    line-height: 34px;
  }
  &.active {
    font-size: 14px;
    background: $mainColor;
    border-radius: 3px;
    color: #fff;
    border-color: $mainColor;
  }
  &.gray {
    color: #7F8294;
    border-color: #E3E5EC;
  }
  &.small {
    min-width: 60px;
    height: 22px;
    line-height: 22px;
    padding: 0 5px;
    font-size: 12px;
    .iconfont {
      font-size: 12px;
      margin-right: 4px;
    }
  }
  img {
    width: 18px;
    margin-right: 6px;
  }
}
</style>