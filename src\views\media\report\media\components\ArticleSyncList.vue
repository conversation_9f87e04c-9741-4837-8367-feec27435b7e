<script setup lang="ts">
import AmountView from "@/views/media/report/media/components/AmountView.vue";

interface Props {
  list: {
    title: string,
    value: string | number,
    icon: string,
    success: number,
    fail: number,
  }[],
  colSettings?: {
    md: number,
    sm: number,
    xs: number,
  }
}
const props = withDefaults(defineProps<Props>(), {
  colSettings: () => ({
    md: 6,
    sm: 12,
    xs: 24,
  })
})
</script>

<template>
<el-row>
  <el-col :md="colSettings.md" :sm="colSettings.sm" :xs="colSettings.xs" v-for="(item, index) in list" :key="index">
    <div class="w100 article-sync-item">
      <div class="icon">
        <img :src="item.icon" alt="">
      </div>
      <div class="amount-item large flex-between">
        <span class="name">{{ item.title }}</span>
        <span class="number">{{ item.value }}</span>
      </div>
      <amount-view :success="item.success" :fail="item.fail" />
    </div>
  </el-col>
</el-row>
</template>

<style scoped lang="scss">
.article-sync-item{
  padding: 12px 20px;
  .icon img{
    width: 48px;
    height: 48px;
  }
  .amount-item{
    padding-bottom: 15px;
    border-bottom: 1px solid #eff0f4;
    margin-bottom: 16px;
    .name{
      font-size: 14px;
      color: #222;
    }
    .number{
      font-size: 20px;
      color: #333;
      font-weight: bold;
    }
  }
}
</style>
