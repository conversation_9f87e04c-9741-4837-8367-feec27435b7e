<template>
  <div class="account-add">
    <div v-if="showAccountList" class="lists flex flex-wrap">
      <div class="item flex-between" v-for="(item, index) in accountTypeList" :key="index" @click="addAccount(index)">
        <div class="icon-text flex">
          <img :src="item.icon" alt="" class="icon">
          <span>{{ item.title }}</span>
        </div>
        <i class="iconfont icon-tianjia"></i>
      </div>
    </div>
    <edit-dialog ref="editDialogRef" v-model:dialogShow="dialogShow" v-bind="formConfig" @success="addAccountSuccess">
      <template #content-bottom>
        <a class="wx-tips" :href="tutorial.url" target="_blank"><i class="iconfont icon-shuoming"></i> {{ tutorial.text }}</a>
      </template>
    </edit-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { useDialog } from '@/hooks/useDialog';
import { openWindow } from '@/utils'
import { addAccountApi } from '@/api/media/account'

const { EditDialog, editDialogRef, dialogShow, openDialog } = useDialog()

const props = defineProps({
  accountTypeList: {
    type: Array as any,
    default: () => []
  },
  showAccountList: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['success'])

const formConfig = ref({
  title: '配置信息',
  width: '500px',
  formData: {},
  formConfig: {
    align: 'left',
    formList: [],
    submit: addAccountApi
  }
})

const formObj: any = {
  '公众号': {
    list: [
      { label: '账号名称', prop: 'title', type: 'input', required: true, placeholder: '请输入账号名称' },
      { label: 'AppId', prop: 'appid', type: 'input', required: true, placeholder: '请输入AppID' },
      { label: 'AppSecret', prop: 'appsecret', type: 'input', required: true, placeholder: '请输入AppSecret' },
    ],
    // 教程地址
    tutorial: {
      text: '如何查找公众号对应信息？查看教程',
      url: 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/********/建站通1.1微信公众号配置文档.pdf'
    }
  },
  '百家号': {
    list: [
      { label: '账号名称', prop: 'title', type: 'input', required: true, placeholder: '请输入账号名称' },
      { label: 'app_id', prop: 'appid', type: 'input', required: true, placeholder: '请输入app_id' },
      { label: 'app_token', prop: 'appsecret', type: 'input', required: true, placeholder: '请输入app_token' },
    ],
    tutorial: {
      text: '如何查找百家号对应信息？查看教程',
      url: 'https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/image/********/百家号配置文档.pdf'
    }
  }
}

const tutorial: any = ref({})
const addAccount = (index: number) => {
  const nowItem = props.accountTypeList[index];
  if (nowItem.auth_url) {
    openWindow(nowItem.auth_url);
  } else {
    formConfig.value.title = `${nowItem.title}配置信息`;
    formConfig.value.formConfig.formList = JSON.parse(JSON.stringify(formObj[nowItem.title].list));
    tutorial.value = formObj[nowItem.title].tutorial;
    openDialog({ types: nowItem.title });
  }
}

const addAccountSuccess = (data: any) => {
  ElMessage.success('添加成功')
  emit('success')
}

defineExpose({ addAccount })
</script>

<style scoped lang="scss">
.wx-tips {
  display: block;
  font-size: 12px;
  color: #2859FF;
  line-height: 32px;
  &:hover {
    color: #2859FF;
  }
  .iconfont {
    font-size: 12px;
  }
}

.account-add {
  .lists {
    padding: 10px 14px;

    .item {
      width: 176px;
      height: 80px;
      background: #FFFFFF;
      box-shadow: 0px 6px 12px 0px rgba(216, 224, 234, 0.2);
      margin-right: 6px;
      margin-bottom: 6px;
      padding: 0 16px;
      box-sizing: border-box;
      cursor: pointer;

      span {
        font-size: 14px;
        color: #696C7D;
        margin-left: 8px;

        .iconfont {
          color: #999FBE;
          font-size: 12px;
        }
      }
    }
  }
}
</style>