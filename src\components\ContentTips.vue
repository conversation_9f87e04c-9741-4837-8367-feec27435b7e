<template>
  <div v-if="showTip" class="media-top-tips flex-between">
    <p>{{ tips }}</p>
    <i class="iconfont icon-guanbi" @click="closeTopTips"></i>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const props = defineProps({
  tips: {
    type: String,
    default: '温馨提示：如果您在1个小时之内未操作页面，系统将退出登录，请及时保存内容'
  }
})

const showTip = ref(true)

const closeTopTips = () => {
  showTip.value = false
}

</script>

<style scoped lang="scss">
.media-top-tips {
  line-height: 32px;
  padding: 0 12px;
  background: rgba(40, 89, 255, .1);
  font-size: 12px;
  color: #2859ff;
  border-radius: 0px;
  .iconfont {
    font-size: 12px;
    cursor: pointer;
  }
}
</style>