<script setup lang="ts">
interface Props {
  data: { icon: string, title: string, value: string | number, unit?: string }[];
  colSettings?: {
    md?: number,
    sm?: number,
    xs?: number
  };
  classType?: 'class-1' | 'default';
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ([]),
  colSettings: () => ({
    md: 7, sm: 6, xs: 4
  }),
  classType: 'default'
})
</script>

<template>
  <el-row>
    <el-col :md="colSettings.md" :sm="colSettings.sm" :xs="colSettings.xs" v-for="(item, index) in data" :key="index">
      <div v-if="classType === 'default'" class="item flex-column flex-c-center">
        <div class="i-title flex">
          <img :src="item.icon" alt="">
          <span>{{ item.title || '--' }}</span>
        </div>
        <p class="number-unit">
          <span class="number">{{ item.value }}</span>
          <span v-if="item.unit" class="unit">{{ item.unit }}</span>
        </p>
      </div>
      <div class="item flex-c-center class-1" v-else-if="classType === 'class-1'">
        <div class="i-title flex">
          <img :src="item.icon" alt="">
        </div>
        <div>
          <span class="name">{{ item.title || '--' }}</span>
          <p class="number-unit">
            <span class="number">{{ item.value }}</span>
            <span v-if="item.unit" class="unit">{{ item.unit }}</span>
          </p>
        </div>
      </div>
    </el-col>
  </el-row>
</template>

<style scoped lang="scss">
.el-col-md-7 {
  width: calc(100% / 7);
  flex: 0 0 calc(100% / 7);
}
.el-col-md-5 {
  width: calc(100% / 5);
  flex: 0 0 calc(100% / 5);
}
.i-title {
  font-size: 14px;
  color: #333;
}
.item {
  width: 100%;
  height: 145px;
  position: relative;
  .i-title {
    img {
      margin-right: 7px;
    }
  }
  .number-unit {
    margin-top: 20px;
    .number {
      font-size: 30px;
      font-family: Bahnschrift;
      font-weight: 400;
      color: #333333;
    }
  }
  &::after {
    content: '';
    width: 1px;
    height: 40px;
    background-color: #EAEBED;
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    margin: auto;
  }
  &.class-1{
    .i-title {
      img {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        margin-right: 14px;
      }
      .name{
        font-size: 14px;
        color: #222;
      }
    }
    .number-unit{
      margin-top: 3px;
      .number{
        font-weight: bold;
        font-size: 24px;
        color: #333;
        margin-right: 4px;
      }
      .unit{
        font-size: 12px;
        color: #999;
      }
    }
  }
}
:deep(.el-col:last-of-type){
 .item::after {
    display: none;
  }
}
</style>
