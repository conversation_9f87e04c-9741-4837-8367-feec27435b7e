import http from '@/utils/request'

// 站点列表
export const getTemplateApi = (params?: any) => {
  return http.request({
    url: '/MyTemplate/index',
    method: 'get',
    params
  })
}

// 获取站点列表
export const getSiteListApi = (params?: any) => {
  return http.request({
    url: '/MyTemplate/temp_list',
    method: 'get',
    params
  })
}

// 复制页面
export const copyPageApi = (params?: any) => {
  return http.request({
    url: '/jwp/copy',
    method: 'get',
    params
  })
}

// 删除页面
export const deletePageApi = (params?: any) => {
  return http.request({
    url: '/jwp/remove',
    method: 'get',
    params
  })
}

// 修改页面名称
export const changePageNameApi = (params?: any) => {
  return http.request({
    url: '/jwp/temp_edit',
    method: 'post',
    data: params
  })
}

// 新增页面
export const addPageApi = (params?: any) => {
  return http.request({
    url: '/jwp/add',
    method: 'post',
    data: params
  })
}
