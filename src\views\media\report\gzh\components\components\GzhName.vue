<script setup lang="ts">
import {useGzhInfo} from "@/hooks/useGzhInfo";
import ChangeList from "@/components/ChangeList.vue";
import {ref, watch} from "vue";
import {changeGzhApi, getGzhListApi} from "@/api/report/gzh";

const props = defineProps({
  showList: {
    type: Boolean,
    default: true,
  }
})
const emits = defineEmits(['setGzhInfo']);

/*公众号数据*/
const {gzhInfo, getGzhInfo} = useGzhInfo();
getGzhInfo((res) => {
  emits('setGzhInfo', res);
});

const listLoading = ref(false);
const gzhList = ref([]);
const listPage = ref({
  page: 1,
  size: 10,
});
const listTotalPage = ref(1);
const getGzhList = async () => {
  listLoading.value = true;
  try{
    const res: any = await getGzhListApi({catid: '公众号', ...listPage.value});
    gzhList.value = res.data.map((v: any) => ({label: v.title, selected: false, id: v.id}));
    listTotalPage.value = res.last_page;
  }catch (e){
    console.log(e);
  }finally {
    listLoading.value = false;
  }
};
watch(() => props.showList, (val) => {
  if(!val) return;
  getGzhList();
}, {immediate: true});
const loadMore = () => {
  if(listPage.value.page >= listTotalPage.value) return;
  listPage.value.page++;
  getGzhList();
};

const change = async (val: any) => {
  await changeGzhApi({id: val.id});
  await getGzhInfo();
};
</script>

<template>
  <div class="flex gzh-name">
    <p>公众号名称：<span>{{ gzhInfo.gzhtitle || '--' }}</span></p>
    <change-list v-if="showList" title="切换公众号" :list="gzhList" @load-more="loadMore" @change="change" />
  </div>
</template>

<style scoped lang="scss">
.gzh-name {
  p {
    font-size: 18px;
    font-family: PingFang;
    font-weight: bold;
    color: #222222;
    span {
      color: #3583FB;
    }
  }
}
</style>
