import http from '@/utils/request'

// 获取咨询列表
export const consultListApi = (params?: any, config: any = {}) => {
  return http.request({
    url: '/ContentList/consultlist',
    ...config,
    method: 'get',
    params
  })
}

// 删除咨询
export const consultDeleteApi = (params?: any, config: any = {}) => {
  return http.request({
    url: 'ContentList/zxdel',
    ...config,
    method: 'post',
    data: params
  })
}

// 获取信息列表
export const infoListApi = (params?: any, config: any = {}) => {
  return http.request({
    url: 'ContentList/messagelist',
    ...config,
    method: 'get',
    params
  })
}

// 留言已读
export const infoReadApi = (params?: any, config: any = {}) => {
  return http.request({
    url: 'ContentList/lyread',
    ...config,
    method: 'post',
    data: params
  })
}

// 删除留言
export const infoDeleteApi = (params?: any, config: any = {}) => {
  return http.request({
    url: 'ContentList/lydel',
    ...config,
    method: 'post',
    data: params
  })
}

// 保存商桥代码
export const saveCodeApi = (params?: any, config: any = {}) => {
  return http.request({
    url: 'SystemInfo/create',
    ...config,
    method: 'post',
    data: params
  })
}

// 获取商桥代码
export const getCodeApi = (params?: any, config: any = {}) => {
  return http.request({
    url: 'SystemInfo/getInfo',
    ...config,
    method: 'get',
    params
  })
}
