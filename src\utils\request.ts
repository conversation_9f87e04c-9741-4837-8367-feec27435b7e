// axios 封装 ts
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
import resetMessage from './resetMessage'; // 重置message
import NProgress from "./nprogress";
import { getToken } from './auth'
import store from '@/store';

interface CustomAxiosRequestConfig extends AxiosRequestConfig {
  allResult?: boolean; // 添加自定义属性
  hideToast?: boolean; // 隐藏toast
}

// 判断是否开发环境
const isDev = process.env.NODE_ENV === 'development';

const { VUE_APP_PROXY_DOMAIN, VUE_APP_PROXY_DOMAIN_REAL } = process.env;

const service = axios.create({
  baseURL: isDev ? VUE_APP_PROXY_DOMAIN : VUE_APP_PROXY_DOMAIN_REAL,
  timeout: 0
});

service.interceptors.request.use(
  (config: any) => {
    const siteId = 255, companyId = 2;
    // const siteId = 112055, companyId = 2;
    // console.log(store, 'store')
    // 开启进度条动画
    NProgress.start();
    const token = getToken();
    if (token) config.headers['Authorization'] = 'Bearer ' + token;
    if (isDev) {
      // 追加site_id到请求参数
      if (config.method === 'post') {
        // 处理data为formData
        if (config.data instanceof FormData) {
          config.data.append('site_id', siteId);
          config.data.append('company_id', companyId);
        } else {
          config.data = { ...config.data, site_id: siteId, company_id: companyId }
          // console.log(config.data);
        }
      }
      else if (config.method === 'get') {
        config.params = { ...config.params, site_id: siteId, company_id: companyId }
      }
    }
    else if (store && store.getters.siteId) config.headers['siteId'] = store.getters.siteId;
    if (config.method === 'post') {
      config.headers['Content-Type'] = 'application/x-www-form-urlencoded';
    }
    return config;
  }
)

service.interceptors.response.use(
  (response: AxiosResponse) => {
    // 关闭进度条动画
    NProgress.done();
    const res = response.data;
    const config = response.config as CustomAxiosRequestConfig;
    switch (res.code) {
      case 200:
      case '200':
      case 1:
        return config.allResult ? res : res.data;
      case 401:
        ElMessage.error(res.message || res.msg || 'Error');
        window.location.href = res.data || res.url;
        return Promise.reject(res.message || res.msg || 'Error');
      default:
        if (!config.hideToast) resetMessage({message: res.message || res.msg || 'Error', type: 'error'});
        return Promise.reject(new Error(res.message || res.msg || 'Error'));
    }
  }, (error: any) => {
    const config = error.config as CustomAxiosRequestConfig;
    // 关闭进度条动画
    NProgress.done();
    if (!config.hideToast) ElMessage.error(error.message || error.msg || 'Error');
    return Promise.reject(error);
  }
)

export default service;
