<template>
  <div class="h100 flex-y">
    <page-menu v-if="navList.length" :list="navList" @click-item="navItemClick" :activeIndex="activeIndex" />
    <div class="flex-1">
      <component ref="currentComponentRef" :is="currentComponent" @changeNav="changeNav" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineAsyncComponent, computed, watch } from 'vue'

const ListView = defineAsyncComponent(() => import('./components/List.vue'))
const AIVue = defineAsyncComponent(() => import('./components/ai.vue'))

const currentComponentRef = ref(null)
const currentComponent = computed(() => {
  return navList.value[activeIndex.value].component
})

const navList = ref([
  { title: '营销热点日历', key: 'list', component: ListView },
  { title: 'AI营销', key: 'ai', component: AIVue, hidden: true },
])
const activeIndex = ref(0)

const navItemClick = (item: any, index: number, data: any) => {
  activeIndex.value = index
}

const rowData = ref({})
const changeNav = (data:any) => {
  const { key, val } = data
  if (val) rowData.value = val
  navList.value.forEach((item:any, index:number) => {
    if (item.key === key) {
      activeIndex.value = index
    }
  })
}

watch(currentComponentRef, (val:any) => {
  if (val) {
    if (val.sendMsg) val.sendMsg(rowData.value)
  }
})

</script>

<style scoped lang="scss">

</style>