<script setup lang="ts">
interface Props {
  title: string,
  list: { name: string, value: string | number }[]
}

const props = defineProps<Props>()
</script>

<template>
  <div style="text-align: left;">
    <p style="font-size: 18px; font-weight: bold">{{ title }}</p>
    <div style="margin-top: 20px;">
      <template v-for="(item, index) in list" :key="index">
        <p style="font-size: 16px;padding-bottom: 20px;" v-if="'' + item.value">{{ item.name }}：{{ item.value }}</p>
      </template>
    </div>
  </div>
</template>

<style scoped lang="scss">

</style>
