/**
 * 日期格式转换 Y-m-d h:i:s
 * @param {String} time
 * @param {String} format
 */
export const timeStamp2String = (time: number, format: string) => {
  const dateTime = new Date()
  dateTime.setTime(time)
  const year = dateTime.getFullYear()
  const month = dateTime.getMonth() + 1 < 10 ? '0' + (dateTime.getMonth() + 1) : dateTime.getMonth() + 1
  const date = dateTime.getDate() < 10 ? '0' + dateTime.getDate() : dateTime.getDate()
  const hour = dateTime.getHours() < 10 ? '0' + dateTime.getHours() : dateTime.getHours()
  const minute = dateTime.getMinutes() < 10 ? '0' + dateTime.getMinutes() : dateTime.getMinutes()
  const second = dateTime.getSeconds() < 10 ? '0' + dateTime.getSeconds() : dateTime.getSeconds()

  // 返回字符串格式
  let dateInfo = ''
  const yIndex = format.search('Y')
  const mIndex = format.search('m')
  const dIndex = format.search('d')
  const hIndex = format.search('h')
  const iIndex = format.search('i')
  const sIndex = format.search('s')
  dateInfo += `${str(year, yIndex)}`
  dateInfo += `${str(month, mIndex)}`
  dateInfo += `${str(date, dIndex)}`
  dateInfo += `${str(hour, hIndex)}`
  dateInfo += `${str(minute, iIndex)}`
  dateInfo += `${str(second, sIndex)}`
  return dateInfo
  function str(number: any, index: number) {
    if (index > -1) return `${number}${format.slice(index + 1, index + 2)}`
    else return ''
  }
}

//当前时段
/**
 * 0:00-6:00--凌晨
*  6:00-11:00--上午
*  11:00-13:00-- 中午
*  13:00-18:00--下午
*  18:00-22:00--晚上
*  22:00-23:59--深夜
*/
export function todayTime() {
  const dateTime:any = new Date()
  const time = timeStamp2String(dateTime, 'h:i')
  let message = '';
  if (time >= '00:00' && time < '06:00') {
    message = '凌晨';
  } else if (time >= '06:00' && time < '11:00') {
    message = '上午';
  } else if (time >= '11:00' && time < '13:00') {
    message = '中午';
  } else if (time >= '13:00' && time < '18:00') {
    message = '下午';
  } else if (time >= '18:00' && time < '22:00') {
    message = '晚上';
  } else {
    message = '深夜';
  }
  return message
}

/**
 * 同步 async 错误捕获
 * @param {promise} 请求
 */
export function awaitHandle(promise: Promise<any>) {
  return promise.then(res => [null, res || "成功"]).catch(err => [err, null]);
}

// 获取当前日期的 年 月 日 周几
export const getDateObj = (time = "") => {
  const nowDate = time ? new Date(time) : new Date(); //当前日期
  const year = nowDate.getFullYear(),
    month = nowDate.getMonth() + 1,
    date = nowDate.getDate(),
    week = nowDate.getDay();
  const days = new Date(year, month, 0).getDate(); //本月天数
  const beforeDays = new Date(year, month - 1, 0).getDate(); //上月天数
  const firstDay = nowDate; //本月第一天的日期
  firstDay.setDate(1);
  const firstDayWeek = firstDay.getDay(); //本月第一天星期几
  return {
    year,
    month,
    date,
    week,
    days,
    beforeDays,
    firstDayWeek
  };
};

// 打开新窗口
let me = null;
export const openWindow = (url: string) => {
  //要打开的网页地址
  const iTop = (window.screen.height - 30 - 800) / 2; //获得窗口的垂直位置;
  const iLeft = (window.screen.width - 10 - 1300) / 2; //获得窗口的水平位置;
  const features =
    "height=800, width=1300, top=" +
    iTop +
    ",left=" +
    iLeft +
    ", margin=auto ,toolbar=no, menubar=no,scrollbars=no,resizable=no, location=no, status=no"; //设置新窗口的特性

  me = window.open(url, "newW", features); //打开新窗口
};

// 将字节转为合适的单位，保留两位小数
export const formatByteSize = (size: number) => {
  if (size < 1024) {
    return size + "B";
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + "KB";
  } else if (size < 1024 * 1024 * 1024) {
    return (size / 1024 / 1024).toFixed(2) + "MB";
  } else {
    return (size / 1024 / 1024 / 1024).toFixed(2) + "GB";
  }
}

// 将base64转为file
export const base64ToFile = (base64: string = '', fileName: string = '') => {
  let arr = base64.split(',')
  let mime = arr[0].match(/:(.*?);/)?.[1] || ''
  let bstr = atob(arr[1])
  let n = bstr.length
  let u8arr = new Uint8Array(n)
  while(n--){
    u8arr[n] = bstr.charCodeAt(n)
  }
  return new File([u8arr], fileName, { type: mime })
}

// 判断图片比例 是否竖版
export const isVerticalImage = (num: number) => {
  return num <= 1
}

// 将blob转为file
// export const blobToFile = (blob: Blob, fileName: string) => {
//   return new File([blob], fileName, { type: blob.type })
// }

// 自动换算文件大小
export const autoFileSize = (bytes: number, decimals: number = 2) => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));
  console.log(i);

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

// 计算两个数的最大公约数
export const gcd: any = (a: number, b: number) => {
  // 四舍五入
  a = Math.round(a);
  b = Math.round(b);
  while (b !== 0) {
    let temp = b;
    b = a % b;
    a = temp;
  }
  return a;
}

// 计算两个数的最小公倍数
export const lcm: any = (a: number, b: number) => {
  return (a * b) / gcd(a, b);
}

// 根据裁剪模式 计算图片的宽高以及 x y 坐标
export const calcImgSize = (width: number, height: number, proportion: number, mode: number) => {
  // mode 1裁剪模式 2平铺模式
  // proportion 宽高比
  let newWidth = width, newHeight = width / proportion;
  let x = 0, y = 0;
  if (mode == 2) y = (newHeight - height) / 2
  else y = (height - newHeight) / 2
  if (y < 0) {
    newHeight = height;
    newWidth = height * proportion;
    if (mode == 2) x = (newWidth - width) / 2
    else x = (width - newWidth) / 2
    y = 0
  }
  // 结果 四舍五入
  newWidth = Math.round(newWidth);
  newHeight = Math.round(newHeight);
  x = Math.round(x);
  y = Math.round(y);
  return {
    newWidth,
    newHeight,
    x,
    y
  }
}