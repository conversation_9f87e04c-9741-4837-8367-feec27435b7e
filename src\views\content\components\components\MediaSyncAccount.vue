<template>
  <!-- 弹窗选择账号 -->
  <edit-dialog ref="editDialogRef" v-model:dialogShow="dialogShow" v-bind="formConfig" v-model:formData="formData" @sure="submit">

  </edit-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { ElMessage } from 'element-plus'
import { useDialog } from '@/hooks/useDialog';

const { EditDialog, editDialogRef, dialogShow, dialogData, openDialog, closeDialog } = useDialog();

const emit = defineEmits(['sure'])

const formConfig = ref({
  title: '选择账号',
  width: '500px',
  sureText: '同步',
  formConfig: {
    labelWidth: 'auto',
    formList: [
      { label: '账号', prop: 'account', type: 'select', placeholder: '请选择账号', options: [], valueKey: 'id', labelKey: 'title' },
    ]
  }
})

const formData = ref({
  account: ''
})

watch(dialogShow, (newVal) => {
  if (newVal) {
    formConfig.value.formConfig.formList[0].options = dialogData.value.list
  }
})

const submit = () => {
  if (!formData.value.account) {
    ElMessage.error('请选择账号')
    return
  }
  emit('sure', {
    account: formData.value.account,
    ...dialogData.value
  })
}

defineExpose({ openDialog, closeDialog })
</script>

<style scoped lang="scss">

</style>