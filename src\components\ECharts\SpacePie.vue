<template>
  <div>

  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from "vue";
import * as echarts from "echarts";

const props = defineProps({
  data: {
    type: Object,
    default: {}
  },
  width: {
    type: String,
    default: '180px'
  },
  height: {
    type: String,
    default: '208px'
  }
})

// 初始化空间占比图表
const allSpace = ref(null);
const spaceInit = (data: any) => {
  nextTick(() => {
    var dom = allSpace.value;
    var myChart = echarts.init(dom);
    var option: any;

    // console.log('饼图', data);

    var base_bfb = data.used_percent || 0;
    var buck_bfb = data.bucket_percent || 0;

    if (base_bfb != 0) base_bfb = base_bfb.split("%")[0];
    if (buck_bfb != 0) buck_bfb = buck_bfb.split("%")[0];

    base_bfb = Number(base_bfb);
    buck_bfb = Number(buck_bfb);

    var count = data.count || "0MB";
    var getflname = ["网站空间", "素材库", "剩余"];
    var getflvalue = [base_bfb, buck_bfb, 100];
    // var getflvalue = [40, 50, 100];

    // 圆环值
    var data1 = [];
    for (var i = 0; i < getflname.length; i++) {
      data1.push({ name: getflname[i], value: getflvalue[i] });
    }

    // 圆环颜色
    var color = [
      {
        type: "linear",
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 0,
            color: "#3765FF" // 0% 处的颜色
          },
          {
            offset: 1,
            color: "#3765FF" // 100% 处的颜色
          }
        ]
      },
      {
        type: "linear",
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 0,
            color: "#B3A3FF" // 0% 处的颜色
          },
          {
            offset: 1,
            color: "#B3A3FF" // 100% 处的颜色
          }
        ]
      },
      {
        type: "linear",
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 0,
            color: "#E6ECF5" // 0% 处的颜色
          },
          {
            offset: 1,
            color: "#E6ECF5" // 100% 处的颜色
          }
        ]
      }
    ];

    var num = 0;
    const seriesOption2 = data1.map((item, index) => {
      // num += item.value;
      num = item.value;
      const a = {
        type: "bar",
        data: [, , , num],
        coordinateSystem: "polar",
        z: 9999 - index,
        name: "网站空间使用",
        roundCap: true,
        color: color[index],
        barGap: "-100%",
        itemStyle: {
          // normal: {
          //   shadowBlur: 5,
          //   shadowColor: color[index]
          // },
          // emphasis: {
          //   color: color[index]
          // }
        },
        emphasis: {
          itemStyle: {
            color: color[index]
          }
        }
      };
      return a;
    });

    option = {
      color: color,
      title: [
        {
          text: "总容量",
          textStyle: {
            "font-size": 16,
            "font-family": "PingFang SC",
            "font-weight": "bold",
            color: "#1C2B4B",
            "line-height": 56
          },
          top: "40%",
          left: "center"
        },
        {
          text: count,
          textStyle: {
            "font-size": 16,
            "font-family": "PingFang SC",
            "font-weight": "bold",
            color: "#1C2B4B",
            "line-height": 56
          },
          top: "50%",
          left: "center"
        }
      ],
      tooltip: {
        show: false
      },
      legend: {
        show: false
      },
      angleAxis: {
        axisLine: {
          show: false
        },
        axisLabel: {
          show: false
        },
        splitLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        min: 0,
        max: 100,
        boundaryGap: ["0", "100"],
        startAngle: 90
      },
      radiusAxis: {
        type: "category",
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          show: false
        },
        data: ["a", "b", "c", "d"],
        z: 10
      },
      polar: {
        radius: ["20%", "90%"]
      },
      toolbox: {
        show: false
      },
      series: seriesOption2
    };

    myChart.setOption(option);
  });
}

watch(
  () => props.data,
  () => {
    spaceInit(props.data)
  },
  { immediate: true }
)
</script>

<style scoped lang="scss">

</style>