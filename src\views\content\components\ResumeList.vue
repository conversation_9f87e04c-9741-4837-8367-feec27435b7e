<template>
  <div class="list-wrap h100">
    <data-list ref="dataListRef" :tableConfig="resumeDataConfig" @clickBtn="dataAction" :actionList="actionList" :actionDefault="false"
      v-model:multipleSelection="resumeDataConfig.multipleSelection">
      <template #top-right>
        <jzt-button name="返回招聘列表" @click="backList"></jzt-button>
      </template>
      <template #content="{ row }">
        {{ resumeInfo(row.content) }}
      </template>
      <template #action="{ row }">
        <div class="table-action flex">
          <div class="item" @click="detailAction(row)">
            <i class="iconfont icon-xiangqing"></i>
            <span> 详情</span>
          </div>
          <div class="item" @click="deleteAction(row.id)">
            <i class="iconfont icon-shanchu1"></i>
            <span> 删除</span>
          </div>
        </div>
      </template>
    </data-list>

    <!--详情-->
    <edit-dialog ref="editDialogRef" :dialog-show="dialogShow" title="简历详情" :custom-form="true" sure-text="关闭" cancel-text="" @close="closeDialog" @sure="closeDialog">
      <div style="max-height: 500px; overflow: auto">
        <detail-dialog :dialogData="dialogData" :column="1"/>
      </div>
    </edit-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus';
import { useContent } from '../hooks';
import DetailDialog from "@/components/DetailDialog.vue";
import {useDialog} from "@/hooks/useDialog";

const props = defineProps({
  cateType: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['actionClick'])

const { resumeDataConfig,
  DataList, dataListRef, deleteAction,
} = useContent(props.cateType)

const dataAction = ({ key, type }: any) => {
  let selectList = resumeDataConfig.value.multipleSelection
  const ids = selectList.map((item: any) => item.id).join(',')

  switch (key) {
    case 'batch-export':
      break;
    case 'all-export':
      break;
    case 'delete':
      if (!selectList.length) return ElMessage.warning('请选择要操作的数据')
      deleteAction(ids)
      break;
  }
}

const actionList = ref([
  { name: '批量导出', icon: 'icon-piliangdaochu', key: 'batch-export', type: 'action' },
  { name: '全部导出', icon: 'icon-piliangdaochu', key: 'all-export', type: 'action' },
  { name: '删除', icon: 'icon-shanchu1', key: 'delete', type: 'action' }
])

// 返回招聘列表
const backList = () => {
  emit('actionClick', 'list')
}

// 设置当前列表的招聘信息
const setResumeData = (resumeData: any) => {
  if (resumeData) resumeDataConfig.value.params = { id: resumeData.id }
}

const { EditDialog, dialogShow, dialogData, openDialog, closeDialog } = useDialog()

// 查看详情
const detailAction = (row: any) => {
  const data = [];
  for (let contentKey in row.content) {
    data.push({
      label: contentKey,
      value: row.content[contentKey],
      type: 'text'
    })
  }
  openDialog(data)
}

// 处理简历详情
const resumeContent = (key: string, content: any) => {
  if(content[key]) {
    return key + '：' + content[key] + ' ';
  }
}

// 计算简历信息显示
const resumeInfo = (content: any) => {
  let info = ''
  info += resumeContent('姓名', content)
  info += resumeContent('性别', content)
  info += resumeContent('联系电话', content)
  info += resumeContent('应聘职位', content)
  return info
}


defineExpose({ setResumeData })

</script>

<style scoped lang="scss">

</style>
