<template>
  <div class="jzt-open-box" v-if="showAlter">
    <div class="jzt-open-shade" @click="showAlter = false;"></div>
    <div class="jzt-open-main market-popup">
      <div class="jzt-open-close" @click="showAlter = false;"><i class="iconfont icon-guanbi"></i></div>
      <div class="show-content">
        <h3 class="title"><span>小游戏营销</span> 新功能上线啦！</h3>
        <p class="tips">小游戏营销正式上线，数字瞬时记忆、拼手速、像素小鸟、斗地主等多种类型任你选择，欢迎大家前来体验试用哦~</p>
      </div>
      <div class="btn-box flex">
        <jzt-button class="active" @click="linkBtn" name="免费试用7天"></jzt-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import store from '@/store'
import { ElMessage } from 'element-plus'

const showAlter = ref(false)

const isAgent = computed(() => store.getters.isAgent)
const isUse = computed(() => store.getters.siteInfo.is_use)

const linkBtn = () => {
  // 跳转链接
  ElMessage.info('功能迁移中，敬请期待~')
}

const checkPopupStatus = () => {
  let today = new Date().toISOString().split('T')[0];
  let popupShownDate = localStorage.getItem('popupShownDate');

  if (popupShownDate !== today) {
    // 不存在标记或标记不是今天的日期，显示弹窗
    displayPopup();
    // 设置标记为今天的日期
    localStorage.setItem('popupShownDate', today);
  }
}
// 显示弹窗的逻辑
const displayPopup = () => {
  // 实现你的弹窗逻辑，例如显示一个模态框或浮动窗口
  // console.log('显示弹窗');
  showAlter.value = true
}

watch(isUse, (newVal) => {
  if(!isAgent.value && isUse.value == 1) {
    // 调用函数检查弹窗状态
    checkPopupStatus();
  }
}, { immediate: true })

</script>

<style scoped lang="scss">
$scale: 0.8;
.jzt-open-box {
  .market-popup {
    background: url('@/assets/images/media/icon01.png') no-repeat center;
    background-size: contain;
    width: calc(811px * $scale) !important;
    height: calc(617px * $scale) !important;
    .jzt-open-close {
      top: calc(95px * $scale);
      .icon-guanbi {
        color: #fff;
      }
    }
    .show-content {
      margin-top: calc(320px * $scale);
      .title {
        font-size: calc(30px * $scale);
        font-weight: 500;
        color: #222222;
        text-align: center;
        margin-bottom: calc(28px * $scale);
        span {
          color: #2D5FFB;
        }
      }
      .tips {
        font-size: calc(18px * $scale);
        font-weight: 500;
        color: #111111;
        margin: 0 calc(46px * $scale);
        line-height: 1.8;
      }
    }
    .btn-box {
      .material-upload {
        &.active {
          margin-right: 0;
          width: calc(240px * $scale);
          height: calc(50px * $scale);
          line-height: calc(50px * $scale);
          border-radius: calc(6px * $scale);
          font-size: calc(20px * $scale);
        }
      }
    }
  }
}
</style>
