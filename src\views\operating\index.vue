<script setup lang="ts">
import {computed, defineAsyncComponent, markRaw, ref} from "vue";

const navList = markRaw([
  { title: '平台列表', key: 'list', component: defineAsyncComponent(() => import('@/views/operating/components/List.vue')) },
])

const activeIndex = ref(0)

const navItemClick = (item: any, index: number) => {
  activeIndex.value = index
}

const currentComponent = computed(() => {
  return navList[activeIndex.value].component
})
</script>

<template>
  <div class="h100 flex-y">
    <page-menu v-if="navList.length" :list="navList" @click-item="navItemClick" :activeIndex="activeIndex" />
    <div class="flex-1">
      <component ref="currentComponentRef" :is="currentComponent" />
    </div>
  </div>
</template>

<style scoped lang="scss">

</style>
