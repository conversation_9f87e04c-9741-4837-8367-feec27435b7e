<template>
  <div class="info-add flex-y w100 h100">
    <content-tips class="mb10"></content-tips>
    <content-tips class="mb10" tips="您可添加外部新闻链接，发布至【云经理-企业资讯】中进行展示"></content-tips>
    <div class="flex-1 content-box h100 flex-y">
      <el-form ref="formRef" class="flex-1" style="overflow: auto;" :model="formData" :rules="rules" label-width="100px">
        <div class="flex-x">
          <el-form-item label="缩略图：" prop="img">
            <upload-image v-model:imageIntro="formData.img" width="210px" height="130px" :uploadBtn="true"></upload-image>
          </el-form-item>
          <div class="flex-1 ml20">
            <el-form-item label="发布类型：" prop="type">
              <el-select v-model="formData.type" placeholder="请选择发布类型">
                <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <template v-if="formData.type == 1">
              <el-form-item label="新闻来源：" prop="source">
                <el-select v-model="formData.source" placeholder="请选择新闻来源">
                  <el-option v-for="item in sourceList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
                <el-input v-if="formData.source == '自定义'" class="mt10" v-model="formData.sourceText" placeholder="请输入自定义新闻来源" maxlength="5"></el-input>
              </el-form-item>
              <el-form-item label="新闻外链：" prop="exterior_url">
                <el-input v-model="formData.exterior_url" placeholder="请输入新闻外链"></el-input>
              </el-form-item>
            </template>
            <el-form-item label="新闻标题：" prop="title">
              <el-input v-model="formData.title" placeholder="请输入新闻标题"></el-input>
            </el-form-item>
          </div>
        </div>
        <template v-if="formData.type == 1">
          <el-form-item label="新闻简介：" prop="content">
            <el-input v-model="formData.content" type="textarea" :rows="3" placeholder="请输入新闻简介"></el-input>
          </el-form-item>
        </template>
        <template v-if="formData.type == 2">
          <el-form-item class="flex-y mb10" label="新闻详情：" prop="detail">
            <tinymce-editor :height="500" v-model:content="formData.detail" />
          </el-form-item>
        </template>
      </el-form>
      <div class="btn-box flex-end mt10">
        <jzt-button class="mr10" name="取消" @click="cancel"></jzt-button>
        <jzt-button class="mr10" name="保存" @click="saveInfo"></jzt-button>
        <jzt-button class="active" name="发布" icon="icon-fabu" @click="sendInfo"></jzt-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineAsyncComponent } from 'vue'
import { ElLoading, ElMessage } from 'element-plus'
import { addNewsApi, publishNewsApi } from '@/api/media'

const ContentTips = defineAsyncComponent(() => import('@/components/ContentTips.vue'))
const UploadImage = defineAsyncComponent(() => import('@/components/Uploads/uploadImg.vue'))
const TinymceEditor = defineAsyncComponent(() => import('@/components/Global/TinymceEditor.vue'))

const emit = defineEmits(['changeNav', 'success'])

const typeList = ref([
  { label: '媒体报道', value: 1 },
  { label: '企业新闻', value: 2 }
])
const sourceList = ref([
  { label: '腾讯', value: '腾讯' },
  { label: '搜狐', value: '搜狐' },
  { label: '新浪', value: '新浪' },
  { label: '网易', value: '网易' },
  { label: '今日头条', value: '今日头条' },
  { label: '自定义', value: '自定义' }
])

const formData:any = ref({
  img: '',
  type: 1,
  source: '',
  sourceText: '',
  exterior_url: '',
  title: '',
  content: ''
})

const rules = ref({
  img: [{ required: true, message: '请上传缩略图', trigger: 'blur' }],
  type: [{ required: true, message: '请选择发布类型', trigger: 'blur' }],
  source: [{ required: (rule: any) => formData.value.type == 1, message: '请选择新闻来源', trigger: 'blur' }],
  sourceCustom: [{ required: (rule: any) => formData.value.source == '自定义' && formData.value.type == 1, message: '请输入自定义新闻来源', trigger: 'blur' },
    { max: 5, message: '自定义新闻来源最多5个字符', trigger: 'blur' }
  ],
  exterior_url: [{ required: (rule: any) => formData.value.type == 1, message: '请输入新闻外链', trigger: 'blur' },
    { pattern: /^(http|https):\/\//, message: '请输入正确的链接格式', trigger: 'blur' }
  ],
  title: [{ required: true, message: '请输入新闻标题', trigger: 'blur' }],
  content: [{ required: (rule: any) => formData.value.type == 1, message: '请输入新闻简介', trigger: 'blur' }],
  detail: [{ required: (rule: any) => formData.value.type == 2, message: '请输入新闻详情', trigger: 'blur' }]
})

const cancel = () => {
  emit('changeNav', { key: 'list'})
}

// 保存
const formRef = ref()
const saveInfo = (type: string = 'save') => {
  return new Promise(async (resolve, reject) => {
    await formRef.value.validate()
    let loading = ElLoading.service({
      lock: true,
      text: '保存中……',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    let params:any = {}
    if (formData.value.id) params.id = formData.value.id
    if (formData.value.type == 2) {
      params = {
        img: formData.value.img,
        type: formData.value.type,
        title: formData.value.title,
        detail: formData.value.detail
      }
    } else {
      params = {
        img: formData.value.img,
        type: formData.value.type,
        exterior_url: formData.value.exterior_url,
        title: formData.value.title,
        content: formData.value.content
      }
      if (formData.value.source == '自定义') params.source = formData.value.sourceText
      else params.source = formData.value.source
    }
    addNewsApi(params).then((res) => {
      ElMessage.success('保存成功')
      if (type == 'save') emit('changeNav', { key: 'list'}), emit('success')
      resolve(res)
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      loading.close()
    })
  })
}

// 发布
const sendInfo = async () => {
  const id = await saveInfo('send')
  let loading = ElLoading.service({
    lock: true,
    text: '发布中……',
    background: 'rgba(0, 0, 0, 0.7)'
  })
  publishNewsApi({ id }).then((res) => {
    ElMessage.success('发布成功')
    emit('changeNav', { key: 'list' })
    emit('success')
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    loading.close()
  })
}

// 设置页面值
const setFormData = (data: any) => {
  const { id, title, img, type, source, exterior_url, content, detail } = data
  if (id) {
    if (type == 2) formData.value = { id, title, img, type, detail }
    else {
      if (sourceList.value.includes(source)) formData.value = { id, title, img, type, source, exterior_url, content }
      else formData.value = { id, title, img, type, source: '自定义', sourceText: source, exterior_url, content }
    }
  } else formData.value = { type: 1} 
}

defineExpose({ setFormData })

</script>

<style scoped lang="scss">

</style>