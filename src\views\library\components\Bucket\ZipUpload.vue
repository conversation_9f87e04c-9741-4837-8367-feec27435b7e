<template>
  <edit-dialog ref="editDialogRef" v-model:dialogShow="dialogShow" v-bind="formConfig" @sure="submitForm">
    <div class="file-wrap flex">
      <el-input v-model="fileName" class="flex-1" placeholder="压缩包的后缀必须是.zip" />
      <span class="btn btn-primary radius upload-btn">
        <i class="iconfont icon-wenjianjia1"></i>
      </span>
      <input ref="fileInputRef" type="file" accept=".zip" class="file-input" @change="fileChange" />
    </div>
  </edit-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import { useDialog } from '@/hooks/useDialog'
import { checkSpaceFileApi, getSynologyOssConfigApi, parseZipApi, uploadZipApi } from '@/api/upload'

const { EditDialog, editDialogRef, dialogShow, dialogData, openDialog, closeDialog } = useDialog()

const emit = defineEmits(['success'])

const props = defineProps({
  folder: {
    type: Number,
    default: 0
  }
})

const formConfig = ref({
  title: '压缩包上传',
  width: '500px',
  sureText: '上传',
  customForm: true
})

const fileType = ref(3)  // 1 图片 2 视频 3 其他
const fileName = ref('')
const fileInputRef = ref()

const currentFile = ref()

const fileChange = () => {
  // 获取文件路径
  const filePath = fileInputRef.value.value
  fileName.value = filePath
  const fileList = fileInputRef.value.files
  if (fileList.length) {
    const file = fileList[0]
    // 判断文件后缀是不是zip
    if (!file.name.endsWith('.zip')) {
      ElMessage.error('压缩包的后缀必须是.zip')
      return
    }
    currentFile.value = file
  }
}

const formLoading = ref()
const submitForm = () => {
  if (!currentFile.value) {
    ElMessage.error('请选择文件')
    return
  }
  formLoading.value = ElLoading.service({
    lock: true,
    text: '上传中...',
    background: 'rgba(0, 0, 0, 0.7)'
  })
  checkSpaceCount(currentFile.value)
}

const checkSpaceCount = (file: any) => {
  let formData = new FormData()
  formData.append('file', file)
  checkSpaceFileApi(formData).then((res) => {
    console.log(res, 'res')
    getSynologyOssConfig()
  }).catch((err) => {
    console.log(err, 'err')
    fileInputRef.value.value = ''
    formLoading.value.close()
  })
}

// 获取群晖oss配置
const getSynologyOssConfig = () => {
  getSynologyOssConfigApi({type: fileType.value}).then((res) => {
    console.log(res, 'res')
    parseZip(res)
  }).catch((err) => {
    console.log(err, 'err')
    fileInputRef.value.value = ''
    formLoading.value.close()
  })
}

// 解析zip
const parseZip = (res: any) => {
  let formData = new FormData()
  var string = currentFile.value.type;
  var stringResult = string.split('/');
  formData.append('file', currentFile.value);
  formData.append('type', res.type);
  formData.append('accessid', res.accessid);
  formData.append('dir', res.dir);
  formData.append('host', res.host);
  formData.append('signature', res.signature);
  formData.append('file_name', res.file_name + '.' + stringResult[1]);
  parseZipApi(formData).then((res) => {
    console.log(res, 'res')
    uploadZip(res)
  }).catch((err) => {
    console.log(err, 'err')
    fileInputRef.value.value = ''
    formLoading.value.close()
  })
}

// 上传压缩包
const uploadZip = (data: any) => {
  console.log(props.folder, 'props.folder')
  let params = {
    data: data.data,
    size: data.size,
    type: fileType.value,
    bucket_type_id: props.folder
  }
  uploadZipApi(params).then((res) => {
    console.log(res, 'res')
    ElMessage.success('上传成功')
    formLoading.value.close()
    closeDialog()
    emit('success')
  }).catch((err) => {
    console.log(err, 'err')
    fileInputRef.value.value = ''
    formLoading.value.close()
  })
}

defineExpose({ openDialog })
</script>

<style scoped lang="scss">
.file-wrap {
  position: relative;
  overflow: hidden;
  .upload-btn {
    width: 40px;
    height: 34px;
    background: #FFFFFF;
    border: 1px solid #A0B6FF;
    border-radius: 3px;
    text-align: center;
    padding: 0;
    line-height: 34px !important;
    margin-left: 10px;
    margin-right: 0;
    color: var(--el-color-primary);
    i {
      font-size: 20px;
    }
  }
  .file-input {
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
  }
}
</style>