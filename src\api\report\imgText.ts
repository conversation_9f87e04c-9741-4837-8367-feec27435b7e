
// 图文列表
import http from "@/utils/request";

export const getImageTextListApi = (params?: any) => {
  return http.request({
    url: '/StatisticsApi/statisticsone',
    // url: 'http://localhost:8888/jztImgAndTextList',
    method: 'get',
    params
  })
}

// 图文详情
export const getImageTextDetailApi = (params?: any) => {
  return http.request({
    url: '/StatisticsApi/getzhibiao',
    // url: 'http://localhost:8888/jztImgAndTextList/detail',
    method: 'get',
    params
  })
}

// 查看评论
export const getCommentApi = (params?: any) => {
  return http.request({
    url: '/StatisticsApi/statisticsthree',
    // url: 'http://localhost:8888/jztImgAndTextList/comment',
    method: 'get',
    params
  })
}

// 回复评论
export const replyCommentApi = (params?: any) => {
  return http.request({
    url: '/StatisticsApi/hfpinlun',
    method: 'post',
    data: params
  })
}

// 删除评论
export const deleteCommentApi = (params?: any) => {
  return http.request({
    url: '/StatisticsApi/scpinlun',
    method: 'post',
    params
  })
}
