<template>
  <div class="h100 flex-y">
    <page-menu v-if="navList.length" :list="navList" @click-item="navItemClick" :activeIndex="activeIndex" />
    <div class="flex-1">
      <component ref="currentComponentRef" :is="currentComponent" @change-nav="changeNav" :params="params" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import {computed, defineAsyncComponent, markRaw, ref} from "vue";

const navList = markRaw([
  { title: '全部分析', key: 'overview', component: defineAsyncComponent(() => import('./components/Overview.vue')) },
  { title: '图文列表', key: 'list', component: defineAsyncComponent(() => import('./components/List.vue')) },
  { title: '单篇分析', key: 'single', component: defineAsyncComponent(() => import('./components/Single.vue')), hidden: true },
  { title: '查看评论', key: 'review', component: defineAsyncComponent(() => import('./components/Review.vue')), hidden: true },
])

const activeIndex = ref(0)

const navItemClick = (item: any, index: number) => {
  activeIndex.value = index
}

const currentComponent = computed(() => {
  return navList[activeIndex.value].component
})

const params = ref()
const changeNav = (key: number, data?: any) => {
  activeIndex.value = key
  if(!data) return;
  params.value = data
}
</script>

<style lang="scss" scoped>

</style>
