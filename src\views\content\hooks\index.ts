import { ref, defineAsyncComponent, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus'
import store from '@/store'
import { contentListApi, contentDeleteApi, contentStatusApi, contentTransferApi } from '@/api/content';
import { cateApiUrl, listConfigCreateForm, listConfigCate, resumeListConfig } from '@/utils/contentType';
import { getAccountListApi } from '@/api/media/account';

export const useContent = (type: string = 'article') => {

  const DataList = defineAsyncComponent(() => import('@/components/Global/DataList.vue'))
  const dataListRef = ref()

  const LimitWord = defineAsyncComponent(() => import('@/components/LimitWord.vue'))
  const limitWordRef = ref()

  const TransferView = defineAsyncComponent(() => import('@/views/content/components/components/TransferView.vue'))
  const transferViewRef = ref()

  const typeApi = cateApiUrl(type).type
  const listApi = cateApiUrl(type).list

  const listAddForm = listConfigCreateForm(type)
  const listCateKey = listConfigCate(type)
  const resumeDataConfig = ref(resumeListConfig(type))

  const tableData = ref<any>([]);
  const tableLoading = ref<boolean>(false);
  const tableTotal = ref<number>(0);

  const getList = (params: any) => {
    tableLoading.value = true;
    contentListApi(listApi, params).then((res: any) => {
      tableData.value = res.data;
      tableTotal.value = res.total;
    })
    .finally( () => {
      tableLoading.value = false;
    });
  }

  // 刷新列表
  const refreshList = () => {
    dataListRef.value.refresh()
  }

  // 删除
  const deleteLoading = ref(false)
  const deleteAction = async (ids: string) => {
    if (deleteLoading.value) return ElMessage.error('请勿重复操作!')
    await ElMessageBox.confirm('确定要删除吗?', '提示', { type: 'warning' })
    try {
      deleteLoading.value = true
      const result = await contentDeleteApi(listApi, {id: ids})
      ElMessage.success('删除成功')
      refreshList()
    } catch (error) {
      console.log(error)
    } finally {
      deleteLoading.value = false
    }
  }

  // 转移到某个分类
  const moveAction = async (ids: string, typeid: string) => {
    if (deleteLoading.value) return ElMessage.error('请勿重复操作!')
    await ElMessageBox.confirm('确定要转移到该分类吗?', '提示', { type: 'warning' })
    try {
      deleteLoading.value = true
      const result = await contentTransferApi(listApi, {id: ids, catid: typeid})
      ElMessage.success('转移成功')
      refreshList()
    } catch (error) {
      console.log(error)
    } finally {
      deleteLoading.value = false
    }
  }

  // 内容列表 上下线
  const statusAction = async (row: any) => {
    if (deleteLoading.value) return ElMessage.error('请勿重复操作!')
    try {
      deleteLoading.value = true
      const { id, state: status } = row
      const state = status == 1 ? 2 : 1
      const result = await contentStatusApi(listApi, {id, state})
      ElMessage.success('操作成功')
      refreshList()
    } catch (error) {
      console.log(error)
    } finally {
      deleteLoading.value = false
    }
  }

  /* 媒体同步 同步公众号、百家号 */
  const siteInfo = computed(() => store.getters.siteInfo)
  // 获取账号列表
  const configList = ref<any>([])
  const getConfigList = async (type: number = 2) => {
    return new Promise((resolve, reject) => {
      getAccountListApi({ type: type, site_id: siteInfo.value.id }).then(res => {
        configList.value = res
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  }

  return {
    listApi, typeApi,
    tableData, tableLoading, tableTotal, getList,
    DataList, dataListRef, refreshList, deleteAction, moveAction,
    LimitWord, limitWordRef,
    TransferView, transferViewRef,
    statusAction,
    listAddForm, listCateKey, resumeDataConfig,
    configList, getConfigList
  }

}