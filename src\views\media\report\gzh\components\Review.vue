<script setup lang="ts">
import GzhName from "./components/GzhName.vue";
import JztButton from "@/components/Global/JztButton.vue";
import CommentList from '@/views/media/report/gzh/components/components/CommentList.vue';
import {ref, watch} from "vue";
import {getCommentApi} from "@/api/report/imgText";

const emits = defineEmits(['changeNav']);

/*返回图文列表*/
const toList = () => {
  emits('changeNav', 1);
};

/*公众号信息*/
const gzhLoading = ref(true);
const gzhInfo:any = ref({});
const setGzhInfo = (data: any) => {
  gzhInfo.value = data;
  gzhLoading.value = false;
}

/* 获取评论 */
const loading = ref(false);
const commentList: any = ref([]);
const getCommentList = async (params: {msgid: string, titless: string, datesss: string}) => {
  loading.value = true;
  try{
    const res: any = await getCommentApi(params);
    if(res.list!=''){
      commentList.value = res.list.map((item: any) => {
        item.reply && (item.reply.replyContent = item.reply.replyContent ? item.reply.replyContent.replace(/<[^>]+>/g, '') : '');
        return item
      });
    }
  }catch (e) {
    console.log(e)
  }finally {
    loading.value = false;
  }
}
watch(() => gzhInfo.value, (val) => {
  if(!val.listss || !val.listss.msgid) return;
  const params = {
    msgid: val.listss.msgid,
    titless: val.twtitle,
    datesss: val.ref_date
  }
  getCommentList(params);
}, {immediate: true, deep: true});
</script>

<template>
  <div class="wrap h100">
    <div class="content-box" v-loading="gzhLoading">
      <div class="gzh-list flex-between flex-align-center">
        <gzh-name :show-list="false" @set-gzh-info="setGzhInfo" />
        <div class="btns">
          <jzt-button name="返回图文列表" @click="toList" />
        </div>
      </div>
      <div class="content-list mt-20">
        <div class="t-box">
          <p class="title">{{ gzhInfo.twtitle }}</p>
          <p class="t-time" id="msgdate">发布时间：{{ gzhInfo.ref_date }}</p>
        </div>
      </div>
      <div class="comment-box mt-20" v-loading="loading">
        <div v-if="commentList.length">
          <comment-list :data="commentList" :msg-id="gzhInfo.listss.msgid" @refresh="getCommentList" />
        </div>
        <el-empty v-else description="暂无评论" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
@import "src/views/media/report/gzh/style/common";
</style>
