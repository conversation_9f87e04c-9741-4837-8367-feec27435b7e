<script setup lang="ts">
import {useDialog} from "@/hooks/useDialog";
import {computed, markRaw, ref, watch} from "vue";
import UploadImg from "@/components/Uploads/uploadImg.vue";
import {addGameApi, updateGameInfoApi} from "@/api/game";
import {ElMessage} from "element-plus";

interface Props {
  dialogShow: boolean,
  title: string,
  data: any,
}
const props = defineProps<Props>();

const emits = defineEmits(['update:dialogShow', 'success']);

const show = computed({
  get() {
    return props.dialogShow
  },
  set(val: any) {
    emits('update:dialogShow', val)
  }
})
const {EditDialog, editDialogRef} = useDialog();

const form = ref({
  title: '',
  company_name: '',
  phone: '',
  domain: '',
  url: '',
  share_title: '',
  share_desc: '',
  share_img: '',
  status: 1
});

watch(() => props.data, (val: any) => {
  if (val) {
    form.value = {...form.value, ...val}
  }
}, {deep: true, immediate: true})

const rules = markRaw({
  title: [{
    required: true,
    message: '请输入游戏标题',
    trigger: 'blur'
  }],
  company_name: [{
    required: true,
    message: '请输入公司名称',
    trigger: 'blur'
  }],
})

const formRef: any = ref(null);
const close = () => {
  if (!formRef) return
  formRef.value.resetFields();
  show.value = false;
}

const loading = ref(false)
/**
 * 提交
 */
const submit = () => {
  if (!formRef) return
  formRef.value.validate(async (valid: any) => {
    if (valid) {
      const apiMap: any = {
        save: addGameApi,
        edit: updateGameInfoApi
      }
      loading.value = true;
      const data = JSON.parse(JSON.stringify(form.value))
      data.phone = data.phone.replace(/[^\d.-]/g, '');
      try{
        await apiMap[props?.data?.id? 'edit' : 'save'](data)
        ElMessage.success('操作成功')
        close();
        emits('success')
      }finally {
        loading.value = false;
      }
    }
  })
}
</script>

<template>
  <edit-dialog ref="editDialogRef" v-model:dialogShow="show" :title="title" :custom-form="true" :show-bottom-btn="false"
               @close="close" @sure="close" width="600px">
    <el-form :model="form" ref="formRef" label-width="100px" :rules="rules" v-if="show">
      <el-form-item label="游戏标题" prop="title">
        <el-input v-model="form.title" placeholder="请输入游戏标题（最多15个字）" :maxlength="15"/>
      </el-form-item>
      <el-form-item label="公司名称" prop="company_name">
        <el-input v-model="form.company_name" placeholder="请输入公司名称"/>
      </el-form-item>
      <el-form-item label="联系电话" prop="phone">
        <el-input v-model="form.phone" placeholder="请输入联系电话"/>
      </el-form-item>
      <el-form-item label="官网地址" prop="domain">
        <el-input v-model="form.domain" placeholder="请输入官网地址"/>
      </el-form-item>
      <el-form-item label="公众号地址" prop="url">
        <el-input v-model="form.url" placeholder="请输入公众号地址"/>
        <a style="color: #2859FF; margin-top: 10px; display: block;" href="https://oss.lcweb01.cn/jzt/0971a11acea011ea9d6bfa163ea50a57/file/20230627/c22c3eb1d29867ce3045457369b4b399.pdf" target="_blank">【查看公众号地址获取教程】</a>
      </el-form-item>
      <el-form-item label="分享标题" prop="share_title">
        <el-input v-model="form.share_title" placeholder="请输入分享标题（最多15个字）" :maxlength="15"/>
      </el-form-item>
      <el-form-item label="分享描述" prop="share_desc">
        <el-input v-model="form.share_desc" placeholder="请输入分享描述（最多30个字）" :maxlength="30"/>
      </el-form-item>
      <el-form-item label="分享图标" prop="share_img">
        <upload-img :uploadBtn="true" width="140px" height="140px" from="content" v-model:imageIntro="form.share_img" :max-size="300 / 1024" position="right"/>
        <p class="tips">分享图标请上传300k以内的图片，否则设置的分享图标会不显示</p>
      </el-form-item>
      <el-form-item label="是否开启" prop="status">
        <el-switch v-model="form.status" :active-value="1" :inactive-value="0" active-text="开启" inactive-text="关闭" inline-prompt/>
      </el-form-item>
      <el-form-item label-width="0">
        <div class="flex-c-center flex-1" style="margin-top: 20px;">
          <el-button type="primary" class="btn" @click="submit" :loading="loading">保存</el-button>
          <el-button class="btn" @click="close">取消</el-button>
        </div>
      </el-form-item>
    </el-form>
</edit-dialog>
</template>

<style scoped lang="scss">
.tips {
  margin-top: 10px;
  color: red;
}
.btn{
  width: 110px;
  height: 36px;
  &:not(.el-button--primary){
    border: 1px solid #A0B6FF;
    color: #2859FF;
  }
}
</style>
