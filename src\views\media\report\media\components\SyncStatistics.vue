<script setup lang="ts">
import AmountView from "@/views/media/report/media/components/AmountView.vue";
import ArticleSyncList from "@/views/media/report/media/components/ArticleSyncList.vue";
import JztButton from "@/components/Global/JztButton.vue";
import DataTable from "@/components/Global/DataTable.vue";
import {computed} from "vue";
import {getMediaArticleListApi} from "@/api/report/media";
import {useRouter} from "vue-router";

interface Props {
  name: string,
  title: string,
  viewInfo: {
    count: number,
    success: number,
    fail: number,
  },
  articleList: {
    title: string,
    value: number | string,
    icon: string,
    success: number,
    fail: number,
  }[],
  type: 'content' | 'video',
  icon: string,
  colSettings?: {
    md: number,
    sm: number,
    xs: number,
  }
}
const props = withDefaults(defineProps<Props>(), {
  colSettings: () => ({
    md: 6,
    sm: 12,
    xs: 24,
  })
})

const tableConfig = computed(() => {
  const config = {
    api: getMediaArticleListApi,
    columns: [
      {prop: 'title', label: '标题', type: ''},
      {prop: 'info', label: '发布状态', width: 200, type: ''},
      {prop: 'create_time', label: '创建时间', width: 200, type: ''},
    ],
    params: {
      type: props.type
    }
  }
  if(props.type === 'video'){
    config.columns.unshift({prop: 'cover', label: '封面', width: 100, type: 'slot'})
  }
  return config;
})

const router = useRouter();
const toContent = () => {
  router.push({
    path: props.type === 'content' ? '/content/article' : '/media/video'
  })
}

// 列表图处理
const  handleCover = (item: any) => {
  return item.cover ? item.cover : require('@/assets/images/system/no-img-1.png');
}
</script>

<template>
  <div class="article-sync mt-10 white-bg">
    <div class="title">{{ name }}</div>
    <el-row :gutter="20" style="margin-top: 30px;">
      <el-col :md="4" :sm="4" :xs="24">
        <div class="item flex-column flex-c-center">
          <div class="icon">
            <img :src="icon" alt="">
          </div>
          <p class="name mt-10">{{ title }}</p>
          <p class="number">{{ viewInfo.count }}</p>
          <amount-view :success="viewInfo.success" :fail="viewInfo.fail"/>
        </div>
      </el-col>
      <el-col :md="20" :sm="20" :xs="24">
        <div class="article-list w100 h100">
          <article-sync-list :list="articleList" :col-settings="colSettings"/>
        </div>
      </el-col>
    </el-row>
    <div class="article mt-20">
      <data-table :show-multiple="false" :show-page="false" :show-index="false" :api="tableConfig.api"
                  :columns="tableConfig.columns" :params="tableConfig.params">
        <template #cover="{row}">
          <el-image
              style="width: 70px;height: 40px;border-radius: 8px;"
              :src="handleCover(row)" fit="cover"
              :preview-src-list="[handleCover(row)]">
          </el-image>
        </template>
      </data-table>
      <div class="flex-c-center mt-20">
        <jzt-button @click="toContent">
          <i class="iconfont icon-chaxun" style="margin-right: 5px;" />
          查看更多
        </jzt-button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.white-bg {
  background: #fff;
  margin-bottom: 10px;
  padding: 20px;
  box-sizing: border-box;
}
.mt-10{
  margin-top: 10px;
}
.mt-20 {
  margin-top: 20px;
}
.name {
  font-size: 14px;
  color: #222;
  margin-top: 10px;
  margin-bottom: 8px;
}
.number {
  font-weight: bold;
  font-size: 24px;
  color: #333;
}
.article-list {
  overflow-x: auto;
  background: #f6f8fb;
}
</style>
