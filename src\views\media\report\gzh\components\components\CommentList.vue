<script lang="ts" setup>
import CommentItem from "./CommentItem.vue";

interface CommentItem {
  content: string;
  time: string;
  author: string;
  [key: string]: any;
}

defineProps<{
  data: CommentItem[];
  msgId: string;
}>();

const emits = defineEmits<{
  (event: 'reply'): void;
}>();

const reply = () => {
//   刷新列表
  emits('reply');
};
</script>

<template>
  <div class="comment-list">
    <comment-item v-for="(item, index) in data" :key="index" :msg-id="msgId" :item="item" @reply="reply" />
  </div>
</template>

<style scoped lang="scss">
.comment-list {
  padding: 0 20px 56px;
  .mt-20 {
    margin-top: 20px;
  }
}
</style>
