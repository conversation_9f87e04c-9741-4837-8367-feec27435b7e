<template>
  <div class="h100 flex-y">
    <page-menu v-if="navList.length" :list="navList" @click-item="navItemClick" :activeIndex="activeIndex" />
    <div class="flex-1">
      <component ref="currentComponentRef" :is="currentComponent" @change-nav="changeNav" :data="data" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import {computed, defineAsyncComponent, ref, shallowRef} from "vue";

const navList = shallowRef([
  { title: '小游戏运营', key: 'list', component: defineAsyncComponent(() => import('@/views/market/game/components/List.vue')) },
  { title: '小游戏', key: 'game', component: defineAsyncComponent(() => import('@/views/market/game/components/Game.vue')), hidden: true },
  { title: '表单设置', key: 'formSettings', component: defineAsyncComponent(() => import('@/views/market/game/components/FormSettings.vue')), hidden: true },
  { title: '表单数据', key: 'formData', component: defineAsyncComponent(() => import('@/views/market/game/components/FormData.vue')), hidden: true },
])

const activeIndex = ref(0)

const navItemClick = (item: any, index: number) => {
  activeIndex.value = index
}

const currentComponent = computed(() => {
  return navList.value[activeIndex.value].component
})

const data = ref({});
const changeNav = (index: number, row?: any) => {
  activeIndex.value = index;
  data.value && (data.value = row)
}
</script>

<style lang="scss" scoped>

</style>
