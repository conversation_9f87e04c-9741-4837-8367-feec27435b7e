<template>
  <div class="bucket-wrap h100 flex-y">
    <div class="bucket-action flex-between p10">
      <div class="btn-list flex-1 flex">
        <template v-for="item in actionBtn" :key="item.key">
          <jzt-button v-if="(isFree && item.isFree) || !isFree" @click="actionBtnClick(item)"
            :name="item.name" :icon="item.icon" :class="[item.class, 'mr10', 'flex-shrink']" :disabled="item.isFree && !isFree"
          />
        </template>
      </div>
      <div class="search-box flex ml30">
        <el-input class="width200" v-model="searchValue" @blur="paramsTitleChange" @keyup.enter="paramsTitleChange" clearable placeholder="请输入素材名搜索" />
        <div class="refresh-btn ml5 flex-c-center flex-shrink" @click="refresh">
          <i class="iconfont icon-shuaxin" />
        </div>
      </div>
    </div>
    <bucket-list-view ref="bucketListViewRef" class="flex-1" @folderChange="folderChange" @closeCate="closeCate" />
    <!-- 单个上传 -->
    <single-upload ref="singleUploadRef" :folder="currentFolder.id" @success="refresh" />
    <!-- 批量上传 -->
    <batch-upload ref="batchUploadRef" :folder="currentFolder.id" @success="refresh" />
    <!-- 压缩包上传 -->
    <zip-upload ref="zipUploadRef" :folder="currentFolder.id" @success="refresh" />
    <!-- 智能获取 -->
    <get-material ref="getMaterialRef" :type="1" :classid="currentFolder.id" />
    <!-- 全部分类 -->
    <all-cate ref="allCateRef" @enterFolder="enterFolder" :type="cateType" @deleteCate="deleteCate" @transfer="transfer" @refresh="refresh" />
  </div>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus';
import { ref, computed, defineAsyncComponent } from 'vue'
import { useStore } from 'vuex'

const store = useStore()
const siteInfo = computed(() => store.getters.siteInfo)
const isFree = computed(() => store.getters.isFree)

const emit = defineEmits(['changeNav'])

const BucketListView = defineAsyncComponent(() => import('./Bucket/BucketList.vue'))
const SingleUpload = defineAsyncComponent(() => import('./Bucket/SingleUpload.vue'))
const BatchUpload = defineAsyncComponent(() => import('./Bucket/BatchUpload.vue'))
const ZipUpload = defineAsyncComponent(() => import('./Bucket/ZipUpload.vue'))
const GetMaterial = defineAsyncComponent(() => import('./Bucket/GetMaterial.vue'))
const AllCate = defineAsyncComponent(() => import('./Bucket/AllCate.vue'))

const singleUploadRef = ref()
const batchUploadRef = ref()
const zipUploadRef = ref()
const getMaterialRef = ref()
const allCateRef = ref()

const actionBtn = ref([
  { name: '上传', key: 'upload', icon: 'icon-shangchuan2', class: 'active', group: 1, isFree: 1 },
  { name: '新建分类', key: 'add-cate', icon: 'icon-xinjianwenjianjia', group: 1, isFree: 1 },
  { name: '批量添加图片', key: 'batch-upload', icon: 'icon-piliangdaochu', group: 1, isFree: 1 },
  { name: '压缩包上传', key: 'zip-upload', icon: 'icon-file-zip', group: 1, isFree: 0 },
  { name: '共享云经理', key: 'synchronous', icon: 'icon-tongbu2x', group: 1, isFree: 0 },
  { name: '智能获取', key: 'smart-access', icon: 'icon-wenjian', group: 1, isFree: 0 },
  { name: '下载', key: 'download', icon: 'icon-xiazai', group: 2, isFree: 1 },
  { name: '转移', key: 'transfer', icon: 'icon-Transfer', group: 2, isFree: 0 },
  { name: '删除', key: 'deleteAll', icon: 'icon-shanchu1', group: 2, isFree: 1 },
  { name: '全部分类', key: 'allCategoryList', icon: 'icon-sucai', group: 2, isFree: 1 }
])

const searchValue = ref('')
const cateType = ref('cate')

const bucketListViewRef = ref()
const paramsTitleChange = () => {
  bucketListViewRef.value.paramsTitleChange(searchValue.value)
}

const actionBtnClick = async (item: any) => {
  // console.log(item, 'actionBtnClick')
  switch (item.key) {
    case 'upload':
      singleUploadRef.value.uploadClick()
      break;
    case 'add-cate': 
      bucketListViewRef.value.addBucketCate()
      break;
    case 'batch-upload':
      batchUploadRef.value.openDialog()
      break;
    case 'zip-upload':
      zipUploadRef.value.openDialog(currentFolder.value)
      break;
    case 'synchronous':
      emit('changeNav', { key: 'share-manager' })
      break;
    case 'smart-access':
      getMaterialRef.value.openDialog(currentFolder.value)
      break;
    case 'download':
      bucketListViewRef.value.download()
      break;
    case 'transfer':
      let list = bucketListViewRef.value.checkedTableData
      console.log(list, 'list')
      if (!list.length) return ElMessage.warning('请先选择要转移的素材或分类')
      cateType.value = 'transfer'
      allCateRef.value.openDialog(list)
      break;
    case 'deleteAll':
      bucketListViewRef.value.deleteBucket()
      break;
    case 'allCategoryList':
      cateType.value = 'cate'
      allCateRef.value.openDialog()
      break;
  }
}

const refresh = () => {
  bucketListViewRef.value.refresh()
}

const currentFolder = ref({id: 0})
const folderChange = (folder: any) => {
  currentFolder.value = folder
}

const enterFolder = (folderArr: any) => {
  bucketListViewRef.value.toSecondFiles(folderArr)
}

// 删除分类
const deleteCate = (data: any) => {
  bucketListViewRef.value.deleteItem(data)
}

// 转移
const transfer = (data: any) => {
  bucketListViewRef.value.transfer(data)
}

const closeCate = () => {
  allCateRef.value.closeDialog()
}
</script>

<style lang="scss" scope>
.bucket-wrap {
  .bucket-action {
    align-items: flex-start;
    .btn-list {
      overflow-x: auto;
    }
    .search-box {
      .refresh-btn {
        cursor: pointer;
        width: 34px;
        height: 34px;
        background: #FFFFFF;
        border: 1px solid #E4E6EB;
        border-radius: 17px;
        color: #878EA5;
      }
    }
  }
}
</style>