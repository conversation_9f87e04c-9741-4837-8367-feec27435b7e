import http from '@/utils/request'

// 数据统计
export function getMediaStatisticsApi(params?: any) {
  return http.request({
    url: '/mediaData/onecount',
    method: 'get',
    params
  })
}

// 账号绑定数
export function getMediaBindApi(params?: any) {
  return http.request({
    url: '/mediaData/authcount',
    method: 'get',
    params
  })
}

// 文章绑定数
export function getMediaArticleApi(params?: any) {
  return http.request({
    url: '/mediaData/contentcount',
    method: 'get',
    params
  })
}

// 文章列表
export function getMediaArticleListApi(params?: any) {
  return http.request({
    url: '/mediaData/sendlog',
    method: 'get',
    params
  })
}

// 视频绑定数
export function getMediaVideoApi(params?: any) {
  return http.request({
    url: '/mediaData/videocount',
    method: 'get',
    params
  })
}
