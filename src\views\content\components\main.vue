<template>
  <div class="h100 flex-y">
    <page-menu v-if="navList.length" :list="navList" @click-item="navItemClick" :activeIndex="activeIndex" />
    <div class="flex-1">
      <component v-if="needRefresh" ref="currentComponentRef" :is="currentComponent" :cateType="cateType" :cateName="cateName" @actionClick="actionClick" @success="refresh" />
      <keep-alive v-else>
        <component ref="currentComponentRef" :is="currentComponent" :cateType="cateType" :cateName="cateName" @actionClick="actionClick" @success="refresh" />
      </keep-alive>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {ref, computed, watch, nextTick, provide} from 'vue'
import { useContent } from '@/hooks/useContent'

const props = defineProps({
  cateType: {
    type: String,
    default: ''
  }
})

const currentComponentRef = ref()

const { navList, components, cateName } = useContent(props.cateType)

const emit = defineEmits(['click-item'])

const activeIndex = ref(0)
const activeKey = ref('')

const navItemClick = (item: any, index: number, data: any) => {
  activeIndex.value = index
  activeKey.value = item.key
  emit('click-item', item)
}

const currentComponent = computed(() => {
  let key = navList.value[activeIndex.value] && navList.value[activeIndex.value].key
  return components[key]
})

const curPage = ref(1);
const currentData:any = ref(null)
const holdListCurPage = ref(false)
const listParams = ref({})
const actionClick = (action: string, data: any = null) => {
  console.log(action, data, 'action')
  const holdPage = ['copy']
  holdListCurPage.value = holdPage.includes(action)
  if (data) {
    data = JSON.parse(JSON.stringify(data))
    if (action === 'copy') {
      currentData.value = data
      delete data.id
      action = 'add'
    }
    else if (action == 'add') currentData.value = data
    else if (action == 'resume-list') currentData.value = data
    else if (action == 'we-chat-sync') currentData.value = data
    else if (action == 'list'){
      if(!data.holdCurPage){
        curPage.value = 1;
        listParams.value = {
          catid: 0,
          title: ''
        }
      }
    }
    needRefresh.value = true
  }
  navList.value.forEach((item: any, index: number) => {
    if (item.key === action) navItemClick(item, index, data)
  })
}

// 刷新数据
const needRefresh = ref(false)
const refresh = () => {
  needRefresh.value = true
}

watch(() => currentComponentRef.value, (val) => {
  nextTick(() => {
    if (currentData.value && val) {
      if (val.setData) {
        if (activeKey.value != 'we-chat-sync') {
          console.log(currentData.value.id, 'currentData.value.id');
          if (currentData.value.id) navList.value[activeIndex.value].title = '编辑'
          else if (currentData.value.title) navList.value[activeIndex.value].title = '复制'
          else if(!currentData.value.id) navList.value[activeIndex.value].title = '添加'
        }
        val.setData(currentData.value)
      }
      if (val.setResumeData) {
        val.setResumeData(currentData.value)
      }
    }
    needRefresh.value = false
  })
})

watch(() => needRefresh.value, (val) => {
  console.log(val, 'needRefresh.value')
}, { immediate: true })

provide('curPage', curPage)
provide('holdListCurPage', holdListCurPage)
provide('listParams', listParams)
</script>

<style lang="scss" scoped>

</style>
