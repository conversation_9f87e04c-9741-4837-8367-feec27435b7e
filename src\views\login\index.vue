<template>
  <div class="select-none">
    <img src="@/assets/images/login/bg.png" class="wave" />
    <div class="login-container">
      <div class="img">
        <img src="@/assets/images/login/illustration.svg" />
      </div>
      <div class="login-box">
        <div class="login-form">
          <img src="@/assets/images/logo.png" alt="" style="margin-bottom: 20px;">
          <el-form ref="ruleFormRef" :model="ruleForm" :rules="loginRules" size="large">
            <el-form-item prop="username">
              <el-input v-model="ruleForm.username" clearable placeholder="请输入用户名" :prefix-icon="User"  size="large" />
            </el-form-item>
            <el-form-item prop="password">
              <el-input v-model="ruleForm.password" clearable show-password placeholder="请输入密码" :prefix-icon="Lock"  size="large" />
            </el-form-item>
            <el-button class="w100" size="large" type="primary" :loading="loading" @click="onLogin(ruleFormRef)">登录</el-button>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { User, Lock } from "@element-plus/icons-vue";

const ruleFormRef = ref();
const ruleForm = reactive({
  username: "admin",
  password: "admin123"
});

const loginRules = {
  username: [{ required: true, message: "请输入用户名", trigger: "blur" }],
  password: [{ required: true, message: "请输入密码", trigger: "blur" }]
}

const loading = ref(false);

const onLogin = async (formEl: any) => {
  if (!formEl) return;
  await formEl.validate((valid: boolean, fields: any) => {
    if (valid) {
      loading.value = true;
      // TODO: 登录逻辑
    } else {
      console.log("error submit!", fields);
      return false;
    }
  });
}

</script>

<style scoped>
@import url("@/assets/css/login.css");
</style>

<style lang="scss" scoped>

</style>
