import http from '@/utils/request'

// 获取营销日期列表
export const getListApi = () => {
  return http.request({
    url: '/inspiration/activity_list',
    method: 'get'
  })
}

// 获取节日类型
export const getFestivalTypeApi = () => {
  return http.request({
    url: '/inspiration/festival_type',
    method: 'get'
  })
}

// 获取节日列表
export const getFestivalListApi = (params:any) => {
  return http.request({
    url: '/inspiration/calendar',
    method: 'get',
    params
  })
}

// 获取AI聊天记录
export const getAiChatApi = (params:any) => {
  return http.request({
    url: '/chatgpt/search_list',
    method: 'get',
    params
  })
}

// ai创作
export const addAiApi = (data:any) => {
  return http.request({
    url: '/chatgpt/wenxin',
    method: 'post',
    data
  })
}

// 添加自定义营销日期
export const addCustomApi = (data:any) => {
  return http.request({
    url: '/inspiration/add_activity',
    method: 'post',
    data
  })
}

