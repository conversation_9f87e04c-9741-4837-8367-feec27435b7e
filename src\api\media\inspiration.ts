import http from '@/utils/request'

// 获取灵感创意列表
export const getListApi = (params?: any) => {
  return http.request({
    url: '/inspiration/list',
    method: 'get',
    params
  })
}

// 收藏灵感创意
export const collectApi = (id:string) => {
  return http.request({
    url: '/inspiration/collection',
    method: 'post',
    data: { id }
  })
}

// 删除灵感创意
export const deleteApi = (id:string) => {
  return http.request({
    url: '/inspiration/cancel_collection',
    method: 'post',
    data: { id }
  })
}

// 添加灵感创意
export const addApi = (data:any) => {
  return http.request({
    url: '/inspiration/create_inspiration',
    method: 'post',
    data
  })
}
