<template>
  <div class="h100">
    <data-list :tableConfig="tableConfig" @clickBtn="dataAction" ref="listRef">
      <template #title="{ row }">
        <i v-if="row.icon" :class="['iconfont', row.icon]" style="margin-right: 10px"></i>
        <span>{{ row.title }}</span>
      </template>
      <template #action="{ row }">
        <div class="table-action flex">
          <div class="item" @click="edit(row)">
            <i class="iconfont icon-bianji"></i> <span>编辑</span>
          </div>
          <div class="item" @click="del(row)">
            <i class="iconfont icon-shanchu1"></i>
            <span>删除</span>
          </div>
        </div>
      </template>
    </data-list>
    <!-- 添加、修改 -->
    <edit-dialog
      ref="editDialogRef"
      v-model:dialogShow="dialogShow"
      v-bind="formConfig"
      v-model:formData="formConfig.formData"
      @success="updateMenuList"></edit-dialog>
  </div>
</template>

<script setup lang="ts">
import { useStore } from 'vuex'
import { ref, defineAsyncComponent, watch } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus';
import {editMenuApi, delMenuApi, getMenuApi} from '@/api/app'

const store = useStore()

// 定义组件
const DataList = defineAsyncComponent(() => import('@/components/Global/DataList.vue'))
const EditDialog = defineAsyncComponent(() => import('@/components/EditDialog/index.vue'))

// 定义表格配置
const tableConfig = ref({
  // api: getMenuApi,
  params: {
    type: 'all'
  },
  tableData: [],
  showPage: false,
  columns: [
    { label: '图标/名称', prop: 'title', type: 'slot' },
    { label: '路径', prop: 'path', },
    { label: '状态', prop: 'status', width: 100, align: 'center', type: 'tag', values: { 0: { text: '禁用', type: 'danger' }, 1: { text: '启用', type: 'success' } } },
    { label: 'AI版菜单', prop: 'yxb', width: 100, align: 'center', type: 'tag', values: { 0: { text: '否', type: 'danger' }, 1: { text: '是', type: 'success' } } },
    { label: '是否可跳转', prop: 'is_link', width: 100, align: 'center', type: 'tag', values: { 0: { text: '否', type: 'danger' }, 1: { text: '是', type: 'success' } } },
    { label: '创建时间', prop: 'create_time', width: 200 },
    { label: '操作', prop: 'action', type: 'slot', align: 'center' }
  ],
  delApi: delMenuApi
})

const listRef = ref<any>(null)

const dataAction = (action: any) => {
  const { key } = action
  switch (key) {
    case 'add':
      editDialogRef.value.open({})
      formConfig.value.title = '添加菜单'
      break
    case 'del':
      // del(e.row)
      break
  }
}

const getMenuList = async () => {
  const result: any = await getMenuApi({ type: 'all' })
  tableConfig.value.tableData = result
}
getMenuList()

// 编辑
const edit = (row: any) => {
  const rowData = JSON.parse(JSON.stringify(row))
  formConfig.value.title = '编辑菜单'
  delete rowData.children
  editDialogRef.value.open(rowData)
}

// 删除
const del = async (row: any) => {
  // 询问
  const res = await ElMessageBox.confirm('确定删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
  if (res === 'confirm') {
    const result = await delMenuApi({ id: row.id })
    ElMessage.success('删除成功')
    await getMenuList()
  }
}

// 表单配置
const editDialogRef:any = ref(null)
const dialogShow = ref(false)
const formConfig = ref({
  title: '添加菜单',
  width: '600px',
  formData: {},
  formConfig: {
    formList: [
      { label: '父级菜单', prop: 'pid', type: 'cascader', options: [{ title: '顶级菜单', id: 0 }], labelKey: 'title', valueKey: 'id' },
      { label: '中文名称', prop: 'title', type: 'input', required: true },
      { label: '英文名称', prop: 'name', type: 'input', required: true },
      { label: '图标代码', prop: 'icon', type: 'input' },
      { label: '显示URL', prop: 'path', type: 'input', required: true },
      { label: '跳转URL', prop: 'redirect', type: 'input', tips: '如果需要跳转站外链接此处请填以http://或者https://开头' },
      { label: '组件路径', prop: 'component', type: 'input', required: true },
      { label: '状态开关', prop: 'status', type: 'switch', value: 1 },
      { label: 'AI版菜单', prop: 'yxb', type: 'switch', value: 0 },
      { label: '是否可跳转', prop: 'is_link', type: 'switch', value: 0, tips: '&nbsp;&nbsp;设置AI版菜单是否可进入页面' },
      { label: '排序', prop: 'rank', type: 'number', value: 0 },
    ],
    submit: editMenuApi
  }
})

// 更新菜单
const updateMenuList = async (form: any) => {
  await getMenuList()
  await store.dispatch('app/getMenuList')
}

watch(() => tableConfig.value.tableData, (newVal) => {
  if(newVal){
    formConfig.value.formConfig.formList[0].options = [{ title: '顶级菜单', id: 0 }, ...newVal]
  }
}, { immediate: true, deep: true })

</script>

<style scoped lang="scss">

</style>
