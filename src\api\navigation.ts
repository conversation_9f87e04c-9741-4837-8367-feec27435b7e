import http from '@/utils/request'

const baseUrl = process.env.VUE_APP_PROXY_DOMAIN_REAL3

// 获取站点导航列表
export const getNavigationListApi = () => {
  return http.request({
    baseURL: baseUrl,
    url: 'menus/lst',
    method: 'get'
  })
}

// 获取导航列表 左侧使用
export const getNavigationLeftListApi = () => {
  return http.request({
    baseURL: baseUrl,
    url: 'menus/leftlst',
    method: 'get'
  })
}

// 获取导航详情
export const getNavigationDetailApi = (id: number) => {
  return http.request({
    baseURL: baseUrl,
    url: 'menus/info',
    method: 'get',
    params: { id }
  })
}

// 获取页面所用到的插件
export const getPagePluginsApi = (params: any) => {
  return http.request({
    baseURL: baseUrl,
    url: 'jwp/lst',
    method: 'get',
    params
  })
}

// 获取单页（信息管理）数据列表  text_id 多个id逗号隔开
export const getPageDataListApi = (params: any) => {
  return http.request({
    baseURL: baseUrl,
    url: 'text/lst',
    method: 'get',
    params
  })
}

// 获取单页（信息管理）数据详情
export const getPageDataDetailApi = (id: number) => {
  return http.request({
    baseURL: baseUrl,
    url: 'text/info',
    method: 'get',
    params: { text_id: id }
  })
}

// 编辑导航
export const editNavApi = (data: any) => {
  return http.request({
    baseURL: baseUrl,
    url: 'menus/edit',
    method: 'post',
    data
  })
}

// 一键设置
export const handleSetApi = (data: any) => {
  return http.request({
    baseURL: baseUrl,
    url: 'menus/all_add',
    method: 'post',
    data
  })
}

// 重新发布
export const reloadWebApi = (data?: any) => {
  return http.request({
    baseURL: baseUrl,
    url: 'site/reloadweb',
    method: 'post',
    data
  })
}
