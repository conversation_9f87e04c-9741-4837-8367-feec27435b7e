<template>
  <div class="container-main flex-y h100">
    <div v-if="showTop" class="contents-top flex">
      <div class="btn-box flex">
        <jzt-button v-for="(item, index) in btnList" :name="item.name" :icon="item.icon"
          :style="{marginRight: index === btnList.length - 1 ? '0' : '10px'}" @click="clickBtn(item)"></jzt-button>
      </div>
      <div class="top-right flex">
        <slot name="top-right"></slot>
      </div>
    </div>
    <div class="table-box flex-1 h100">
      <data-table ref="dataTableRef" v-bind="tableConfig" @changeMultiple="changeMultiple" @changeMaxNumber="changeMaxNumber" @changeCurrent="changeCurrent">
        <template v-for="(item, index) in slotList" :key="index" #[item.prop]="{row}">
          <slot :name="item.prop" :row="row"></slot>
        </template>
      </data-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, defineAsyncComponent, watch } from 'vue';

const DataTable = defineAsyncComponent(() => import('./DataTable.vue'))

const props = defineProps({
  actionDefault: {
    type: Boolean,
    default: true
  },
  actionList: {
    type: Array,
    default: () => []
  },
  tableConfig: {
    type: Object,
    default: () => ({})
  },
  multipleSelection: {
    type: Array,
    default: () => []
  },
  maxNumber: {
    type: Number,
    default: 0
  },
  showTop: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['clickBtn', 'update:multipleSelection', 'update:maxNumber', 'changeCurrent'])

const btnDefault = [
  { name: '添加', icon: 'icon-tianjia', key: 'add', type: 'page' },
  { name: '删除', icon: 'icon-shanchu1', key: 'delete', type: 'action' }
]
const btnList: any = computed(() => {
  if (props.actionDefault) {
    return [...btnDefault, ...props.actionList]
  } else return props.actionList
})

const clickBtn = (item: any) => {
  emit('clickBtn', item)
}

const slotList = computed(() => {
  return props.tableConfig.columns.filter((item: any) => item.type === 'slot')
})

const changeMultiple = (val: any) => {
  emit('update:multipleSelection', val)
}

// 单选
const changeCurrent = (val: any) => {
  emit('changeCurrent', val)
}

const changeMaxNumber = (val: any) => {
  console.log(val, 'maxNumber')
  emit('update:maxNumber', val)
}

// 刷新数据列表
const dataTableRef = ref()
const refresh = () => {
  dataTableRef.value.refresh()
}

defineExpose({ refresh, dataTableRef })

</script>

<style scoped lang="scss">
.container-main {
  .contents-top {
    width: 100%;
    justify-content: space-between;
    padding: 10px 22px 0;
    box-sizing: border-box;
  }
}
</style>
