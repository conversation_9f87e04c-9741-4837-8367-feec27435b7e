<script setup lang="ts">
import {ref} from "vue";
import {operationListApi} from "@/api/operation";
import DataTable from "@/components/Global/DataTable.vue";

const tableConfig = ref({
  api: operationListApi,
  columns: [
    {label: '名称', prop: 'title'},
    {label: '链接', prop: 'link'},
    {label: '账号', prop: 'username'},
    {label: '密码', prop: 'password', type: 'slot'},
    {label: '操作', prop: 'action', type: 'slot', align: 'center', width: 280}
  ],
  multipleSelection: []
})

const interview = (row: any) => {
  if(!row.link) return
  if(row.link.indexOf('http') === -1 || row.link.indexOf('https') === -1) {
    row.link = 'http://' + row.link
  }
  window.open(row.link)
}
</script>

<template>
  <div class="consult h100">
    <data-table :show-page="false" :api="tableConfig.api" :columns="tableConfig.columns">
      <template #password="{row}">
        <div class="password">
          <el-input v-model="row.password" type="password" class="password" readonly />
        </div>
      </template>
      <template #action="{row}">
        <div class="table-action flex">
          <div class="item" @click="interview(row)">
            <i class="iconfont icon-lianjie-"></i> <span>访问</span>
          </div>
        </div>
      </template>
    </data-table>
  </div>
</template>

<style scoped lang="scss">
.consult{
  padding-top: 10px;
  .password{
    :deep(.el-input__wrapper){
      box-shadow: none;
      background: transparent;
    }
  }
}
</style>
