<template>
  <div class="list-wrap h100">
    <data-list ref="dataListRef" :tableConfig="tableConfig" @clickBtn="dataAction" :actionList="actionList" v-model:multipleSelection="tableConfig.multipleSelection"
        v-model:maxNumber="tableConfig.maxNumber">
      <template #top-right>
        <search-view v-model:search-data="tableConfig.params" :cateType="cateType" :apiType="typeApi"></search-view>
      </template>
      <template #ordering="{ row }">
        <div style="cursor:  move;">
          <i class="iconfont icon-tuodongweizhi"></i>
          <span>{{ row.ordering }}</span>
        </div>
      </template>
      <template #categories="{ row }">
        <span >{{ row[listCateKey] && row[listCateKey].title || '未分组' }}</span>
      </template>
      <template #media_sync="{ row }">
        <media-sync ref="mediaSyncRef" :row="row" @seletAccount="selectAccount" @syncWx="syncWx" />
      </template>
      <template #image_intro="{ row }">
        <el-image style="width: 100%;height: 40px;border-radius: 4px;" fit="cover" :src="row.image_intro || require('@/assets/images/system/no-img.png')"
        :preview-teleported="true" :preview-src-list="[row.image_intro || require('@/assets/images/system/no-img.png')]" />
      </template>
      <template #action="{ row }">
        <div class="table-action flex">
          <div v-if="cateType == 'recruitment'" class="item" @click="read(row)">
            <i class="iconfont icon-jianli1"></i> <span>查看简历</span>
          </div>
          <div class="item" @click="edit(row)">
            <i class="iconfont icon-bianji"></i> <span>编辑</span>
          </div>
          <div v-if="cateType == 'goods'" class="item" @click="copy(row)">
            <i class="iconfont icon-piliangtianjia"></i> <span>复制</span>
          </div>
          <div class="item" @click="statusAction(row)">
            <i :class="['iconfont', { 'icon-xiaxian': row.state }, { 'icon-shangxian': !row.state }]"></i>
            <span>{{ row.state ? ' 下线' : ' 上线' }}</span>
          </div>
          <div class="item" @click="deleteAction(row.id)">
            <i class="iconfont icon-shanchu1"></i>
            <span>删除</span>
          </div>
        </div>
      </template>
    </data-list>
    <!-- 转移 -->
    <transfer-view ref="transferViewRef" @success="refreshList" :apiType="typeApi" :apiList="listApi"></transfer-view>
    <!-- 文章列表 同步百家号 选择账号 -->
    <media-sync-account v-if="cateType == 'content'" ref="mediaSyncAccountRef" @sure="toAsync" />
  </div>
</template>

<script setup lang="ts">
import {defineAsyncComponent, inject, ref, watch} from 'vue'
import { contentListApi } from '@/api/content';
import { ElMessage } from 'element-plus';
import { useContent } from '../hooks';

// 定义组件
const MediaSync = defineAsyncComponent(() => import('./components/MediaSync.vue'))
const MediaSyncAccount = defineAsyncComponent(() => import('./components/MediaSyncAccount.vue'))
const SearchView = defineAsyncComponent(() => import('./components/SearchView.vue'))

const mediaSyncRef = ref()
const mediaSyncAccountRef = ref()

const props = defineProps({
  cateType: {
    type: String,
    default: ''
  },
})

const { listApi, typeApi, listCateKey,
  DataList, dataListRef, refreshList, deleteAction, TransferView, transferViewRef, statusAction
} = useContent(props.cateType)

const listParams = inject<{[key: string]: any, value: {[key: string]: any}}>('listParams', {value: {catid: 0, title: ''}})
const tableConfig = ref({
  api: contentListApi,
  apiType: listApi,
  cateType: props.cateType,
  showIndex: false,
  allResult: true,
  columns: <any>[
    { label: '分类', prop: 'categories', type: 'slot', width: 200 },
    { label: '标题', prop: 'title', edit: true },
    { label: '添加时间', prop: 'created', width: 200, sortable: 'custom' },
    { label: '状态', prop: 'state', width: 100, align: 'center', type: 'state' },
    { label: '操作', prop: 'action', type: 'slot', align: 'center' }
  ],
  multipleSelection: [],
  params: <any>listParams.value,
  maxNumber: 1,
  dragSort: true,
})

const actionList:any = ref([])

const emit = defineEmits(['actionClick'])
const dataAction = ({ key, type }: any) => {
  if (type == 'page') {
    let params = null
    if (key == 'add') params = { maxNumber: tableConfig.value.maxNumber }
    emit('actionClick', key, params)
  }
  else if (type == 'action') {
    console.log(key, tableConfig.value.multipleSelection)
    let selectList = tableConfig.value.multipleSelection
    const ids = selectList.map((item: any) => item.id).join(',')
    switch (key) {
      case 'transfer':
      case 'delete':
        if (!selectList.length) return ElMessage.warning('请选择要操作的数据')
        if (key == 'delete') deleteAction(ids)
        else transferViewRef.value.openDialog(ids)
        break;
      case 'library-add':
        emit('actionClick', key)
        break;
    }
  }
}

watch(() => props.cateType, () => {
  switch (props.cateType) {
    case 'content':
      actionList.value = [
        { name: '批量转移', icon: 'icon-piliangzhuanyi', key: 'transfer', type: 'action' }
      ]
      tableConfig.value.columns.splice(0, 0, { label: '排序', prop: 'ordering', type: 'slot', sortable: 'custom', width: 100, edit: true })
      tableConfig.value.columns.splice(tableConfig.value.columns.length - 1, 0, { label: '媒体同步', prop: 'media_sync', type: 'slot', align: 'center', width: 200 })
      break;
    case 'goods':
    case 'product':
      tableConfig.value.columns.splice(0, 0, { label: '排序', prop: 'ordering', type: 'slot', sortable: 'custom', width: 100, edit: true })
      tableConfig.value.columns.splice(1, 0, { label: '封面', prop: 'image_intro', type: 'slot', width: 94 })
      actionList.value = [
        { name: '素材库批量添加', icon: 'icon-piliangtianjia', key: 'library-add', type: 'page' }
      ]
      if (props.cateType == 'goods') actionList.value.push({ name: '批量转移', icon: 'icon-piliangzhuanyi', key: 'transfer', type: 'action' })
      break;
    case 'carousel':
      break;
    case 'text':
      tableConfig.value.params.catid = ''
      break;
    case 'image':
      tableConfig.value.columns.splice(1, 0, { label: '封面', prop: 'image_intro', type: 'slot', width: 94 })
      tableConfig.value.columns.splice(0, 0, { label: '排序', prop: 'ordering', type: 'slot', sortable: 'custom', width: 100, edit: true })
      actionList.value = [
        { name: '素材库批量添加', icon: 'icon-piliangtianjia', key: 'library-add', type: 'page' }
      ]
      break;
  }
}, { immediate: true })

// 查看简历
const read = (row: any) => {
  emit('actionClick','resume-list', row)
}

// 编辑
const edit = (row: any) => {
  // console.log(row)
  emit('actionClick', 'add', row)
}

// 复制
const copy = (row: any) => {
  emit('actionClick', 'copy', row)
}

// 选择百家号账号
const selectAccount = (row: any) => {
  console.log(row)
  console.log(mediaSyncAccountRef.value)
  mediaSyncAccountRef.value && mediaSyncAccountRef.value.openDialog(row)
}

// 同步 百家号
const toAsync = (row: any) => {
  const { account, id } = row
  mediaSyncRef.value && mediaSyncRef.value.layer_alert(account, id)
}

// 同步 微信公众号
const syncWx = (row: any) => {
  emit('actionClick', 'we-chat-sync', row)
}

defineExpose({ refreshList, dataListRef })
</script>

<style scoped lang="scss">

</style>
