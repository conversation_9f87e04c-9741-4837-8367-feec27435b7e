<template>
  <div v-loading="loading" class="h100 flex-y">
    <template v-if="loaded">
      <template v-if="pagePlugins.length">
        <el-tabs v-if="pagePlugins.length > 1" v-model="activeTab" class="demo-tabs" @tab-click="changeTab">
          <el-tab-pane v-for="item in pagePlugins" :key="item.name" :label="item.label" :name="item.name">
          </el-tab-pane>
        </el-tabs>
        <div class="flex-1">
          <component ref="currentPagePluginRef" :is="currentPagePlugin.component" :cateType="currentPagePlugin.key" :showAddType="currentPagePlugin.showAddType" :id="route.query.id"></component>
        </div>
      </template>
      <div v-else class="flex-1 flex-column pb100">
        <el-empty :description="type == 1 ? '该页面没有从客户端获取数据，请去高级页面修改' : '该导航未做任何跳转设置'" />
        <el-button v-if="type == 1" type="primary" @click="goEdit">去高级页面修改</el-button>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, defineAsyncComponent, watch, computed, shallowRef, markRaw } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { usePreview } from '@/views/website/hooks'
import { getPagePluginsApi, getPageDataDetailApi, getNavigationDetailApi } from '@/api/navigation'

const route = useRoute()
const router = useRouter()

const type = computed(() => Number(route.query.type))

const { openEdit } = usePreview()

const activeTab = ref('single')

const SinglePage = defineAsyncComponent(() => import('@/views/content/text.vue'))
const ArticlePage = defineAsyncComponent(() => import('@/views/content/article.vue'))
const ProductPage = defineAsyncComponent(() => import('@/views/content/goods.vue'))
const SinglePageEdit = defineAsyncComponent(() => import('@/views/content/components/infoAdd.vue'))
const EditNav = defineAsyncComponent(() => import('@/views/page/components/EditNav.vue'))

const pagePlugins = ref<any[]>([])
const currentPagePlugin = computed(() => pagePlugins.value.find(item => item.name === activeTab.value))

const currentPagePluginRef = shallowRef<any>()

const tabList = ref([
  { label: '信息管理', name: 'single', component: markRaw(SinglePage), key: 'text', showAddType: true },
  { label: '文章管理', name: 'article', component: markRaw(ArticlePage), key: 'content', showAddType: true },
  { label: '图文管理', name: 'product', component: markRaw(ProductPage), key: 'goods', showAddType: true },
  { label: '导航管理', name: 'nav', component: markRaw(EditNav), key: 'nav', showAddType: false }
])

// 切换tab
const changeTab = (tab: any) => {
  console.log(tab, 'tab')
  const { name } = tab.props
  activeTab.value = name
  if (name == 'single' && pageDataId.value) {
    getPageData(pageDataId.value)
  }
}

const loaded = ref(false)
const loading = ref(false)
const pageDataId = ref<number>(0)
const getPagePlugins = () => {
  const { jwp_id } = route.query
  loading.value = true
  getPagePluginsApi({ jwp_id }).then((res: any) => {
    loaded.value = true
    // 遍历对象
    for (const item in res) {
      let plugin: any = tabList.value.find(v => v.key === item)
      if (plugin) {
        if (item == 'text' && res[item].length == 1) {
          plugin.component = markRaw(SinglePageEdit)
          pageDataId.value = res[item][0]
          getPageData(res[item][0])
        }
        pagePlugins.value.push(plugin)
      }
    }
    if (pagePlugins.value.length) activeTab.value = pagePlugins.value[0].name
    else {
      getNavDetail(Number(route.query.id))
    }
  }).catch(err => {
    console.log(err, 'err')
    getNavDetail(Number(route.query.id))
  }).finally(() => {
    loading.value = false
  })
}

// 获取 导航详情数据
const navInfo = ref<any>({})
const getNavDetail = (id: number) => {
  getNavigationDetailApi(id).then((res: any) => {
    navInfo.value = res
  })
}

// 去高级页面修改
const goEdit = () => {
  openEdit(navInfo.value)
}

// 获取 信息管理、单页数据
const getPageData = (id: number) => {
  getPageDataDetailApi(id).then((res: any) => {
    console.log(res, 'res')
    currentPagePluginRef.value.setData(res)
  })
}

// 监听路有变化
watch(() => route.query, () => {
  const { jwp_id, id, type, title }:any = route.query
  if (jwp_id && jwp_id != '0' && type == 1) {
    loaded.value = false
    loading.value = true
    pagePlugins.value = []
    pageDataId.value = 0
    getPagePlugins()
  } else if (type == 2 && id) {
    loaded.value = true
    pagePlugins.value = []
    let plugin:any = tabList.value.find(v => v.key == 'nav')
    pagePlugins.value.push(plugin)
    console.log(pagePlugins.value, 'plugin')
    activeTab.value = 'nav'
  } else if (type == 3) {
    loaded.value = true
    pagePlugins.value = []
  }
  if (title) document.title = title
}, { immediate: true, deep: true })

</script>

<style scoped lang="scss">
.demo-tabs {
  --el-tabs-header-height: 50px;
  background-color: #fff;
  :deep(.el-tabs__header) {
    margin-bottom: 0;
    .el-tabs__nav-wrap {
      padding: 0 10px;
      &::after {
        background-color: #E3E5EC;
        height: 1px;
      }
    }
  }
}
</style>