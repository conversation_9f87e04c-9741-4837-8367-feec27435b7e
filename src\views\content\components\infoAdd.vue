<template>
  <div class="info-add flex-y h100">
    <content-tips class="mb10"></content-tips>
    <div class="content-box flex-1 flex-y h100">
      <div class="flex-1" style="overflow-y: auto">
        <el-form ref="ruleFormRef" :model="formData" :rules="rules" :inline="true"
          :label-width="labelAuto ? 'auto': '100px'" class="flex-1 demo-ruleForm demo-form-inline">
          <div class="top-info-box flex">
            <div v-if="hasCover" class="left-img-box">
              <label class="el-form-item__label">封面图：</label>
              <upload-image :uploadBtn="true" height="130px" v-model:imageIntro="formData.image_intro" />
            </div>
            <div v-if="cateType == 'product'" class="mr20">
              <label class="el-form-item__label">二维码：</label>
              <upload-image :uploadBtn="true" width="130px" height="130px" localBtnText="本地" libraryBtnText="素材库" v-model:imageIntro="formData.qrcode" />
            </div>
            <div class="right-item-box">
              <div v-if="cateType == 'carousel'" class="flex banner-wrap">
                <el-form-item class="banner-item" label="轮播图：" prop="image_intro">
                  <upload-image class="w100" :uploadBtn="true" height="220px" v-model:imageIntro="formData.image_intro" />
                </el-form-item>
                <el-form-item class="banner-item" label="内嵌图：" prop="tp2">
                  <upload-image class="w100" :uploadBtn="true" height="220px" v-model:imageIntro="formData.tp2" />
                </el-form-item>
              </div>
              <el-form-item :label="`${cateName}分类：`" prop="catid">
                <type-select ref="typeSelectRef" class="width200 mr10" v-model:value="formData.catid" :apiType="typeApi" :cateType="cateType" v-model:maxNumber="tableConfig.maxNumber"
                  :isEdit="formData.id ? true : false" optionType="content-add" @change="typeChange"></type-select>
                <jzt-button name="添加分类" icon="icon-tianjia" @click="addTypeBtn"></jzt-button>
              </el-form-item>
              <el-form-item :label="`${cateName}排序：`">
                <el-col :span="6" prop="ordering" style="margin-right: 5px">
                  <el-input-number v-model="formData.ordering" controls-position="right" :min="0" size=""></el-input-number>
                </el-col>
              </el-form-item>
              <el-form-item :label="`${cateName}状态：`">
                <el-radio-group v-model="formData.state">
                  <el-radio :value="1">上线</el-radio>
                  <el-radio :value="2">暂不上线</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item v-if="cateType != 'carousel'" :label="`${cateName}标题：`" prop="title" style="width: 60%">
                <el-col :span="24">
                  <el-input v-model="formData.title" :placeholder="`请输入${cateName}标题`"/>
                </el-col>
              </el-form-item>
              <el-form-item :label="`添加时间：`" prop="created">
                <el-date-picker v-model="formData.created" prefix-icon="Calendar" type="datetime"
                  placeholder="请选择日期"
                  value-format="YYYY-MM-DD HH:mm:ss"/>
              </el-form-item>
              <el-form-item v-if="cateType == 'activity'" :label="`活动时间：`" prop="actime">
                <el-date-picker v-model="formData.actime" prefix-icon="Calendar" type="date"
                  placeholder="请选择日期"
                  value-format="YYYY-MM-DD"/>
              </el-form-item>
              <template v-if="cateType == 'product'">
                <el-form-item :label="`商品原价：`" prop="original_price">
                  <el-input v-model="formData.original_price" :placeholder="`0.00`"/>
                </el-form-item>
                <el-form-item :label="`商品售价：`" prop="sale_price">
                  <el-input v-model="formData.sale_price" :placeholder="`0.00`"/>
                </el-form-item>
                <el-form-item :label="`咨询电话：`" prop="tel">
                  <el-input v-model="formData.tel" :placeholder="`请输入咨询电话`"/>
                </el-form-item>
              </template>
              <el-form-item v-if="showIntro" :label="`${cateName}简介：`" prop="introtext" style="width: 100%; margin-right: 0">
                <el-col :span="24">
                  <el-input v-model="formData.introtext" :placeholder="`请输入${cateName}简介`" :rows="4" type="textarea"
                    resize="none"/>
                </el-col>
              </el-form-item>
              <template v-if="cateType == 'goods'">
                <el-col v-for="(item, index) in goodsKeys" :key="index" :span="24">
                  <el-form-item class="w100" :label="item.value + '：'" :prop="item.key">
                    <el-input class="w100" v-model="item.content" :placeholder="`请输入${item.value}`"/>
                  </el-form-item>
                </el-col>
              </template>
              <template v-if="cateType == 'carousel'">
                <el-col :span="24">
                  <el-form-item class="w100" :label="`标题：`" prop="title">
                    <el-input v-model="formData.title" :placeholder="`请输入标题`"/>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item class="w100" :label="`${cateName}标题：`" prop="banner_title">
                    <el-input v-model="formData.banner_title" :placeholder="`请输入${cateName}标题`"/>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item class="w100" :label="`外部链接：`" prop="link">
                    <el-input v-model="formData.link" :placeholder="`请输入外部链接`"/>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item class="w100" :label="`视频地址：`" prop="video_link">
                    <el-input v-model="formData.video_link" :placeholder="`请输入视频地址`"/>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item class="w100" :label="`副标题：`" prop="second_title">
                    <el-input v-model="formData.second_title" :placeholder="`请输入副标题`" :rows="4" type="textarea"
                      resize="none"/>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item class="w100" :label="`${cateName}内容：`" prop="introtext">
                    <el-input v-model="formData.introtext" :placeholder="`请输入${cateName}内容`" :rows="4" type="textarea"
                      resize="none"/>
                  </el-form-item>
                </el-col>
              </template>
              <el-form-item v-for="(item, index) in listAddForm" :key="index" :label="`${item.label}：`" :prop="item.key">
                <el-input :class="`width${item.width}`" v-model="formData[item.key]" :placeholder="item.placeholder"/>
              </el-form-item>
            </div>
          </div>
          <div v-if="cateType == 'activity'" class="info-more-main">
            <el-form-item v-for="(item, index) in activityKeys" :key="index" :label="item.label + '：'" :prop="item.key">
              <el-input :class="`width${item.width}`" v-if="item.type == 'input'" v-model="formData[item.key]" :placeholder="item.placeholder"/>
              <el-date-picker v-if="item.type == 'datetime'" v-model="formData[item.key]" prefix-icon="Calendar" type="datetimerange"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"/>
            </el-form-item>
          </div>
          <div v-if="hasSpecial" class="info-more-main">
            <div class="info-more-line flex">
              <div class="i-m-line"></div>
              <jzt-button @click="isShowMore = !isShowMore" name="特殊插件设置" icon="icon-settings">
                <i :class="['iconfont', 'icon-arrow-down', { open: isShowMore }]"></i>
              </jzt-button>
            </div>
            <div class="info-more-box mt10" v-show="isShowMore">
              <el-row :gutter="20">
                <template v-if="cateType == 'content'">
                  <el-col :span="24">
                    <el-form-item class="w100" label="可跳转的外部链接：" prop="href_url">
                      <el-input class="w100" v-model="formData.href_url" :placeholder="`请输入可跳转的外部链接`"/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item class="w100" label="标签一：" prop="introtext2">
                      <el-input v-model="formData.introtext2" :placeholder="`请输入标签一`"/>
                    </el-form-item>
                  </el-col>
                </template>
                <template v-if="cateType == 'goods'">
                  <el-col :span="24">
                    <el-form-item class="w100" label="可跳转的外部链接：" prop="href_url">
                      <el-input class="w100" v-model="formData.href_url" :placeholder="`请输入可跳转的外部链接`"/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <div class="info-add-part01 w100 flex mb20">
                      <!--二维码-->
                      <div class="info-add-part01-left">
                        <div class="s-title">二维码：</div>
                        <upload-image :uploadBtn="true" width="130px" position="right" height="130px" v-model:imageIntro="formData.tp2" />
                      </div>
                      <!--多图上传-->
                      <div class="info-add-part01-right flex-1 ml20">
                        <div class="s-title">多图上传：</div>
                        <div class="w100" style="overflow-x: auto;">
                          <upload-image :uploadBtn="true" :multiple="true" width="130px" position="right" height="130px" v-model:imageIntro="formData.tp3" />
                        </div>
                      </div>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item class="w100" label="标签一：" prop="label1">
                      <el-input v-model="formData.label1" :placeholder="`请输入标签一`"/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item class="w100" label="标签二：" prop="label2">
                      <el-input v-model="formData.label2" :placeholder="`请输入标签二`"/>
                    </el-form-item>
                  </el-col>
                </template>
              </el-row>
              <div class="info-line"></div>
            </div>
          </div>
          <div v-else-if="showEditor" class="info-line"></div>
        </el-form>
        <!-- style="width: 48%;overflow-y: auto;"  ml20 h100-->
        <div v-if="showEditor" class="editor-wrap" >
          <label class="el-form-item__label">{{ `${cateName}详情：` }}</label>
          <!--{{formData.fulltext}}-->
          <tinymce-editor v-model:content="formData.fulltext" :imgArr="[]" />
        </div>
      </div>
      <div class="bot-btn-box flex mt20" style="justify-content: flex-end">
        <div class="btn-box flex">
          <jzt-button size="large" style="margin-right: 10px" name="取消" @click="cancel" />
          <jzt-button size="large" style="margin-right: 10px" class="active" name="保存" :click-time="2000" @click="save(false)" />
          <jzt-button v-if="showContinue" size="large" style="" class="active" name="保存并继续添加" :click-time="2000" @click="save(true)" />
        </div>
      </div>
    </div>
    <limit-word ref="limitWordRef" :cate-type="cateType" @continue="limitContinue" />
    <!-- 添加修改弹窗 -->
    <edit-type ref="editTypeRef" :cateType="cateType" :apiType="typeApi" :maxOrder="tableConfig.maxNumber" @success="refreshList"></edit-type>
  </div>
</template>

<script setup lang="ts">
import {ref, defineAsyncComponent, watch, computed, inject} from 'vue'
import { useContent } from '../hooks';
import { ElMessage } from 'element-plus';
import { useLimitWord } from '@/hooks/useLimitWord'
import { goodsKeysApi, contentAddApi, contentEditApi } from '@/api/content'

const { LimitWord, limitWordRef } = useLimitWord()

const props = defineProps({
  cateType: {
    type: String,
    default: ''
  },
  cateName: {
    type: String,
    default: ''
  }
})

const { listApi, typeApi, listAddForm, listCateKey } = useContent(props.cateType)

const emit = defineEmits(['actionClick', 'success'])

const ContentTips = defineAsyncComponent(() => import('@/components/ContentTips.vue'))
const UploadImage = defineAsyncComponent(() => import('@/components/Uploads/uploadImg.vue'))
const TinymceEditor = defineAsyncComponent(() => import('@/components/Global/TinymceEditor.vue'))
const TypeSelect = defineAsyncComponent(() => import('./components/TypeSelect.vue'))
const EditType = defineAsyncComponent(() => import('./components/EditType.vue'))

const defaultFormData = {
  state: 1,
  ordering: 0
}

const formData:any = ref(JSON.parse(JSON.stringify(defaultFormData)))
const rules = computed(() => {
  let obj:any = {
    title: [
      { required: true, message: '请输入标题', trigger: 'blur' }
    ]
  }
  switch (props.cateType) {
    case 'carousel':
      obj.image_intro = [
        { required: true, message: '请添加轮播图', trigger: 'blur' }
      ]
      break;
  }
  return obj
})

// 添加分类 点击
const editTypeRef = ref()
const tableConfig = ref({
  maxNumber: 0
})
const addTypeBtn = () => {
  editTypeRef.value && editTypeRef.value.openDialog()
}

const typeSelectRef = ref()
const refreshList = () => {
  // emit('success')
  typeSelectRef.value && typeSelectRef.value.refresh()
}

// 是否显示封面
const hasCover = computed(() => {
  switch (props.cateType) {
    case 'content':
    case 'goods':
    case 'product':
    case 'activity':
    case 'advertisement':
    case 'image':
      return true
    default:
      return false
  }
})
// 是否显示富文本
const showEditor = computed(() => {
  switch (props.cateType) {
    case 'carousel':
    case 'image':
      return false
    default:
      return true
  }
})

// 是否显示简介
const showIntro = computed(() => {
  switch (props.cateType) {
    case 'content':
    case 'goods':
    case 'product':
    case 'activity':
    case 'advertisement':
    case 'image':
      return true
    default:
      return false
  }
})

// 是否显示保存并继续添加
const showContinue = computed(() => {
  switch (props.cateType) {
    case 'content':
    case 'goods':
    case 'product':
      return true
    default:
      return false
  }
})

// 表单标签宽度是否自动
const labelAuto = computed(() => {
  switch (props.cateType) {
    case 'carousel':
      return false
    default:
      return true
  }
})

/* 特殊插件设置 */
const hasSpecial = computed(() => props.cateType == 'goods' || props.cateType == 'content')
const isShowMore = ref(false)

const setData = (data: any) => {
  console.log(data, 'setData')
  let { id, catid, title, introtext, ordering, state, created, fulltext, image_intro, images } = data
  if (id || title) {
    if (!image_intro) {
      try {
        image_intro = images && JSON.parse(images).image_intro
      } catch (error) {
        image_intro = images
      }
    }
    formData.value = { id, catid, title, introtext, ordering, state: state == 1 ? 1 : 2, created, fulltext, image_intro }
    let paramsKey:any = {
      'content': [
        {dataKey: 'href_url', formKey: 'href_url'},
        {dataKey: 'introtext2', formKey: 'introtext2'}
      ],
      'goods': [
        {dataKey: 'href_url', formKey: 'href_url'},
        {dataKey: 'label1', formKey: 'label1'},
        {dataKey: 'label2', formKey: 'label2'},
        {dataKey: 'qrcode', formKey: 'tp2'},
        {dataKey: 'many_img', formKey: 'tp3'},
        {dataKey: 'other', formKey: 'other'}
      ],
      'carousel': [
        {dataKey: 'banner_title', formKey: 'banner_title'},
        {dataKey: 'link', formKey: 'link'},
        {dataKey: 'video_link', formKey: 'video_link'},
        {dataKey:'second_title', formKey:'second_title'},
        {dataKey: 'qrcode', formKey: 'tp2'}
      ],
      'product': [
        {dataKey: 'original_price', formKey: 'original_price'},
        {dataKey:'sale_price', formKey:'sale_price'},
        {dataKey: 'tel', formKey: 'tel'},
        {dataKey: 'qrcode', formKey: 'qrcode'}
      ],
      'activity': [
        {dataKey: 'actime', formKey: 'actime'},
      ]
    }
    if (props.cateType == 'activity') {
      currentType.value = data.activityfl
      activityKeys.value.forEach((item:any) => {
        if (item.key == 'active_time') formData.value[item.key] = [data.pkstart, data.pkstop]
        else formData.value[item.key] = data[item.key]
      })
    }
    paramsKey[props.cateType] && paramsKey[props.cateType].forEach((item: any) => {
      // 把所有的;替换成,
      if (item.dataKey == 'many_img') formData.value[item.formKey] = data[item.dataKey].replace(/;/g, ',')
      else formData.value[item.formKey] = data[item.dataKey]
    })
    console.log(formData.value,'formData.value')
    listAddForm.length && listAddForm.forEach((item:any) => {
      if (data[item.key]) formData.value[item.key] = data[item.key]
    })
  }
  else {
    formData.value.ordering = data.maxNumber
    listAddForm.length && listAddForm.forEach((item:any) => {
      if(item.default) formData.value[item.key] = item.default
    })
  }
  // 分类处理
  let cate = data[listCateKey]
  if (!cate) formData.value.catid = 0
}

// 监听分类选择
const currentType:any = ref(null)
const typeChange = (val: any) => {
  console.log(val, 'type-change')
  currentType.value = val
}

/* 产品特殊处理 */
const goodsKeys:any = ref([])
const getGoodsKeys = async () => {
  try {
    const res:any = await goodsKeysApi()
    goodsKeys.value = res
    if (formData.value.other) {
      const other = JSON.parse(formData.value.other)
      goodsKeys.value.forEach((item:any) => {
        item.content = other[item.key]
      })
    }
  } catch (error) {
    console.log(error)
  }
}

// 取消
const cancel = () => {
  emit('actionClick', 'list')
}

const activityKeys = computed(() => {
  if (!currentType.value) return []
  let arr:any = [], { id, parent_id } = currentType.value
  if (id == 2 || parent_id == 2) {
    arr = [
      { label: '比赛时间', key: 'active_time', type: 'datetime', placeholder: '请选择比赛时间' },
      { label: '活动地点', key: 'address', type: 'input', placeholder: '请输入活动地点', width: '300' },
      { label: '主办单位', key: 'organizer', type: 'input', placeholder: '请输入主办单位', width: '300' },
      { label: '承办单位', key: 'undertaker', type: 'input', placeholder: '请输入承办单位', width: '300' }
    ]
  }
  if (id == 3 || parent_id == 3) {
    arr = [
      { label: '活动排名', key: 'ranking', type: 'input', placeholder: '活动排名', width: '100' },
      { label: '培训次数', key: 'pxrnumber', type: 'input', placeholder: '培训次数', width: '100' },
      { label: '培训人数', key: 'number', type: 'input', placeholder: '培训人数', width: '100' },
      { label: '星级', key: 'xingji', type: 'input', placeholder: '星级', width: '100' },
      { label: '联系人', key: 'lxrname', type: 'input', placeholder: '联系人', width: '300' },
      { label: '联系电话', key: 'lxrtel', type: 'input', placeholder: '联系电话', width: '300' },
      { label: '详细地址', key: 'xxaddress', type: 'input', placeholder: '详细地址', width: '300' },
    ]
  }
  return arr
})


// 保存
const ruleFormRef = ref()
const submitLoading = ref(false)
const saveContinue = ref(false)

const save = async (isContinue = false) => {
  if (submitLoading.value) return ElMessage.warning('请勿重复提交')
  await ruleFormRef.value.validate()
  saveContinue.value = isContinue
  await limitWordRef.value && await limitWordRef.value.checkLimit(formData.value)
}

const limitContinue = async (res: any) => {
  formData.value = res
  saveApi(saveContinue.value)
}
const holdListCurPage = inject<{[key: string]: any, value: boolean}>('holdListCurPage', {value: false})
const saveApi = async (isContinue = false) => {
  let api = contentAddApi
  if (formData.value && formData.value.id) api = contentEditApi
  try {
    submitLoading.value = true
    let params:any = JSON.parse(JSON.stringify(formData.value))
    if (params.catid == 0) delete params.catid
    switch (props.cateType) {
      case 'goods':
        params.other = goodsKeys.value
        break;
      case 'activity':
        if (!params.actime) return ElMessage.warning('请选择活动时间')
        if (activityKeys.value.find((item: any) => item.key == 'active_time')) {
          if (params.active_time) {
            params.pkstart = params.active_time[0]
            params.pkstop = params.active_time[1]
            delete params.active_time
          }
        }
        break;
    }
    const res = await api(listApi, params)
    ElMessage.success('操作成功')
    if (isContinue) resetData()
    else emit('success'), emit('actionClick', 'list', { holdCurPage: (formData.value && formData.value.id) || holdListCurPage.value })
  } catch (error) {
    console.log(error)
  } finally {
    submitLoading.value = false
  }
}

const resetData = () => {
  formData.value = {...JSON.parse(JSON.stringify(defaultFormData)), ordering: formData.value.ordering}
  if (props.cateType == 'goods') {
    goodsKeys.value.forEach((item: any) => {
      item.content = ''
    })
  }
}

watch(() => props.cateType, (val) => {
  if (val == 'goods') getGoodsKeys()
}, { immediate: true })

defineExpose({ setData })
</script>

<style scoped lang="scss">
.info-add {
  .demo-ruleForm {
    overflow-y: auto;
  }
  .top-info-box {
    align-items: flex-start;

    .left-img-box {
      width: 210px;
      margin-right: 30px;
    }

    .right-item-box {
      flex: 1;
      .banner-wrap {
        justify-content: space-between;
        .banner-item {
          width: calc(100% / 2 - 20px);
          flex-direction: column;
          :deep(.el-form-item__label) {
            width: auto !important;
            justify-content: flex-start;
          }
        }
      }
    }
  }
  :deep(.el-form) {
    .el-form-item__label-wrap {
      margin-left: 0 !important;
    }
  }
  .info-more-line {
    justify-content: space-between;

    .i-m-line {
      width: calc(100% - 166px);
      height: 1px;
      background: #e3e5ec;
    }

    .icon-arrow-down {
      font-size: 10px;
      transform: rotateZ(90deg);
      transition: all ease-in-out 0.3s;
      margin-left: 5px;

      &.open {
        transform: rotateY(0deg);
      }
    }
  }
  .info-add-part01 {
    .s-title {
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 500;
      color: var(--el-text-color-regular);
      margin-bottom: 8px;
    }
    .info-add-part01-left {
      width: 250px;
      flex-shrink: 0;
    }
  }

  .info-line {
    width: 100%;
    height: 1px;
    background: #e3e5ec;
    margin: 10px 0;
  }
}
</style>
