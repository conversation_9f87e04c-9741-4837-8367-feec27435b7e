import { defineAsyncComponent, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

export const useDataList = () => {
  const DataList = defineAsyncComponent(() => import('@/components/Global/DataList.vue'))
  const dataListRef = ref()

  // 获取数据列表
  const listLoading = ref(false)
  const getList = (api: any, params: any) => {
    return new Promise((resolve, reject) => {
      listLoading.value = true
      api(params).then((res: any) => {
        resolve(res)
      }).finally(() => {
        listLoading.value = false
      })
    })
  }

  // 删除
  const loading = ref(false)
  const deleteAction = (ids: string | number | number[], api: any, params: any = {}) => {
    return new Promise((resolve, reject) => {
      if (loading.value) return
      ElMessageBox.confirm('确定要删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        loading.value = true
        api({ id: ids, ...params }).then((res: any) => {
          ElMessage.success('删除成功')
          resolve(res)
          if (dataListRef.value) dataListRef.value.refresh()
        }).finally(() => {
          loading.value = false
        })
      })
    })
  }

  // 刷新
  const refresh = () => {
    dataListRef.value.refresh()
  }

  // 处理封面 
  const handleCover = (row: any) => {
    return row.cover || row.img || require('@/assets/images/system/no-img-1.png')
  }

  return { DataList, dataListRef, deleteAction, listLoading, getList, refresh, handleCover }
}