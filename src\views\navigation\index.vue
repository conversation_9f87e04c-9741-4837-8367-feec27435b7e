<template>
  <div class="h100 wrap">
    <data-list ref="dataListRef" :tableConfig="tableConfig" :show-top="false">
      <template #status="{row}">
        <el-switch v-if="row && Object.keys(row).length > 0" :model-value="row.status" 
          :active-value="1" :inactive-value="2" inline-prompt active-text="开启" inactive-text="关闭" @click="changeStatus(row)" />
      </template>
      <template #title="{row}">
        <div class="title" v-html="row.title" />
      </template>
      <template #action="{row}">
        <div class="table-action">
          <div class="item" @click="edit(row)">
            <i class="iconfont icon-bianji"></i> <span>编辑</span>
          </div>
        </div>
      </template>
    </data-list>
    <!-- 编辑导航 -->
    <edit-nav ref="editNavRef" @success="refresh" />
  </div>
</template>

<script setup lang="ts">
import { defineAsyncComponent, ref } from "vue";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import { useDataList } from "@/hooks/useDataList";
import { getNavigationListApi, editNavApi } from "@/api/navigation";

const { DataList, dataListRef, refresh } = useDataList()

const EditNav = defineAsyncComponent(() => import('@/views/navigation/components/EditNav.vue'))
const editNavRef = ref()

const tableConfig = ref({
  api: getNavigationListApi,
  tableData: [],
  showMultiple: false,
  showPage: false,
  columns: [
    { label: '导航名称', prop: 'title', type: 'slot' },
    { label: '页面名称', prop: 'jwp_title' },
    { label: '显示', prop: 'status', type: 'slot', width: 120, align: 'center', activeValue: 1, inactiveValue: 0, activeText: '开启', inactiveText: '关闭' },
    { label: '操作', prop: 'action', type: 'slot', width: 150, align: 'center' }
  ]
})

const changeStatus = (row: any) => {
  let params = { id: row.id, status: row.status }
  if (row.status == 1) params.status = 2
  else params.status = 1
  let loading = ElLoading.service({ text: '更改中，请稍后' })
  editNavApi(params).then(res => {
    ElMessage.success('修改成功')
    refresh()
  }).catch(err => {
    
  }).finally(() => {
    loading.close()
  })
}

// 编辑
const edit = (row: any) => {
  editNavRef.value.openDialog(row)
}

</script>

<style scoped lang="scss">
.wrap {
  padding: 10px 0;
}
.title {
  display: inline-block;
}
</style>
