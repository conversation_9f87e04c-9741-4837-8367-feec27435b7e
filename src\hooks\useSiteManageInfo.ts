import {ref} from "vue";
import {getSiteFaviconApi} from "@/api/siteManage";

export const useSiteManageInfo = () => {
  const loading = ref(false)

  // 获取favicon
  const getFavicon = async (showLoading: boolean = true, callback?: (res: any) => void) => {
    showLoading && (loading.value = true)
    try{
      const res: any = await getSiteFaviconApi()
      callback && callback(res)
    }catch (e) {
      console.log(e);
    }finally {
      showLoading && (loading.value = false)
    }
  }

  return {
    loading,
    getFavicon
  }
}

export default useSiteManageInfo

