<template>
  <div class="h100 flex-y">
    <page-menu v-if="navList.length" :list="navList" @click-item="navItemClick" :activeIndex="activeIndex" />
    <div class="flex-1 mt10">
      <component  ref="currentComponentRef" :is="currentComponent" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineAsyncComponent, computed } from 'vue'

const ListView = defineAsyncComponent(() => import('./components/List.vue'))

const currentComponentRef = ref(null)
const currentComponent = computed(() => {
  return navList.value[activeIndex.value].component
})

const navList = ref([
  { title: '灵感创意', key: 'list', component: ListView }
])
const activeIndex = ref(0)

const navItemClick = (item: any, index: number, data: any) => {
  activeIndex.value = index
}
</script>

<style scoped lang="scss">

</style>