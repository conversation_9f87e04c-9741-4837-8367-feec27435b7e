<template>
  <edit-dialog ref="editDialogRef" v-model:dialogShow="dialogShow" v-bind="formConfig">
    <div v-loading="loading" class="qr-code flex-column">
      <qrcode-vue v-if="qrCodeUrl"  :value="qrCodeUrl" :size="200" />
      <div v-else class="width200 height200"></div>
      <p class="qr-tips">请使用抖音APP扫描上方二维码</p>
    </div>
  </edit-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useDialog } from '@/hooks/useDialog'
import QrcodeVue from "qrcode.vue"
import { shareDouApi } from '@/api/media'

const { EditDialog, editDialogRef, dialogShow, dialogData, openDialog } = useDialog()

const formConfig = ref({
  title: '分享抖音',
  width: '400px',
  customForm: true,
  showBottomBtn: false
})

// 获取分享抖音二维码链接
const qrCodeUrl = ref('')
const loading = ref(false)
const getShareDouUrl = () => {
  loading.value = true
  shareDouApi({log_id: dialogData.value}).then((res: any) => {
    qrCodeUrl.value = res
  }).finally(() => {
    loading.value = false
  })
}

watch(dialogShow, (val) => {
  if (val && dialogData.value) qrCodeUrl.value = '', getShareDouUrl()
})

defineExpose({ openDialog })
</script>

<style scoped lang="scss">
.qr-tips {
  text-align: center;
  font-size: 14px;
  color: #999;
  line-height: 42px;
}
</style>