<template>
  <div class="jzt-open-box" v-if="dialogShow">
    <div class="jzt-open-shade" @click="cancel('shade')"></div>
    <div class="jzt-open-main" :style="`width: ${width};height: ${height};`">
      <div class="jzt-open-close" @click="cancel('close')"><i class="iconfont icon-guanbi"></i>
      </div>
      <div class="jzt-open-title">{{ title }}</div>
      <div class="jzt-open-content">
        <template v-if="customForm">
          <slot></slot>
        </template>
        <template v-else>
          <edit-form ref="editFormRef" v-bind="formConfig" v-model:data="formData" />
          <slot name="content-bottom"></slot>
        </template>
        <div class="btn-box flex" v-if="showBottomBtn">
          <jzt-button v-if="sureText" size="large" class="active" :name="sureText" @click="sure" :reClick="true" :canClick="!isPosting"
            :style="cancelText ? 'margin-right: 10px !important;' : ''"></jzt-button>
          <jzt-button v-if="cancelText" size="large" :name="cancelText" @click="cancel('cancel')"></jzt-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus';
import { ref, watch, defineAsyncComponent } from 'vue'

const EditForm = defineAsyncComponent(() => import('./EditForm.vue'))

const props = defineProps({
  dialogShow: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '提示'
  },
  width: {
    type: String,
    default: '30%'
  },
  height: {
    type: String,
    default: 'max-content'
  },
  customForm: {
    type: Boolean,
    default: false
  },
  formConfig: {
    type: Object,
    default: () => {}
  },
  sureText: {
    type: String,
    default: '确定'
  },
  cancelText: {
    type: String,
    default: '取消'
  },
  isCancelHide: {
    type: Boolean,
    default: true
  },
  isCloseHide: {
    type: Boolean,
    default: true
  },
  showBottomBtn: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:dialogShow', 'update:formData', 'sure', 'cancel', 'close', 'shade', 'success'])

const editFormRef:any = ref(null)
const formData = ref({})
watch(
  () => formData.value,
  (val) => {
    emit('update:formData', val)
  },
  { deep: true }
)

// 打开弹窗
const open = (row?: any) => {
  console.log(row, '打开弹窗');
  if (row) formData.value = row
  else formData.value = {}
  emit('update:dialogShow', true)
}

// 关闭弹窗
const close = () => {
  emit('update:dialogShow', false)
}

// 点击确定
const isPosting = ref(false)
const sure = async () => {
  if (props.customForm || !props.formConfig.submit) emit('sure')
  else {
    await editFormRef.value.submitForm()
    if (!props.formConfig.submit) return
    isPosting.value = true
    try {
      const result = await props.formConfig.submit(formData.value)
      close()
      emit('update:formData', {})
      emit('success', formData.value)
    } catch (error) {
      console.log(error)
    }
    finally {
      isPosting.value = false
    }
  }
}

// 点击取消
const cancel = (type: any) => {
  if ((type == 'cancel' && props.isCancelHide) || (type == 'close' && props.isCloseHide)) close()
  emit(type)
}

defineExpose({ open, close })
</script>

<style scoped lang="scss">

</style>
