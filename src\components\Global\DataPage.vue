<template>
  <div class="page-box flex">
    <div :class="['page-btn', 'prev', { 'disabled': cPage == 1 }]" @click="prevPage"><i
        class="iconfont icon-zuofanyezuohua"></i></div>
    <div class="page-btn page-input">
      <el-input-number v-model="cPage" :min="1" :max="totalPage" :controls="false" @blur="changePage"
        @keyup.enter="changePage" />
    </div>
    <div class="page-text">/&nbsp;{{ totalPage }}页<span v-if="showTotal">&nbsp;&nbsp;&nbsp;共 {{ total }}
        条</span></div>
    <div :class="['page-btn', 'next', { 'disabled': page == totalPage }]" @click="nextPage"><i
        class="iconfont icon-youfanyeyouhua"></i></div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

const props = defineProps({
  page: {
    type: Number,
    default: 1,
  },
  totalPage: {
    type: Number,
    default: 1,
  },
  total: {
    type: Number,
    default: 0
  },
  showTotal: {
    type: Boolean,
    default: true
  },
})

const emit = defineEmits(['pagination', 'update:page'])

const cPage = ref(props.page)

// 上一页
const prevPage = () => {
  if (cPage.value > 1) {
    emit('update:page', cPage.value - 1)
    emit('pagination')
  }
}

// 改变页数
const changePage = (e: any) => {
  console.log(e.target.value)
  emit('update:page', Number(e.target.value))
  emit('pagination')
}

// 下一页
const nextPage = () => {
  if (cPage.value < props.totalPage) {
    emit('update:page', cPage.value + 1)
    emit('pagination')
  }
}

watch(() => props.page, (newVal, oldVal) => {
  cPage.value = newVal
})

</script>

<style scoped lang="scss">
.page-box {
  height: 62px;
  justify-content: center;

  .page-btn {
    width: 28px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    background: #FFFFFF;
    border: 1px solid #A7AAB8;
    border-radius: 6px;
    cursor: pointer;

    &.disabled {
      border-color: #E3E5EC;
      color: #A7AAB8;
    }

    &.page-input {
      border-color: #A0B6FF;
      color: var(--jzt-color-main);

      :deep(.el-input-number) {
        width: 100%;
        height: 100%;
        text-align: center;
        background: none;
      }

      :deep(.el-input) {
        line-height: inherit;
        height: 100%;
      }

      :deep(.el-input__wrapper) {
        padding: 0;
        width: 100%;
        height: auto;
        text-align: center;
        background: none;
        border: none;
        box-shadow: none;
        color: var(--jzt-color-main);
        margin-top: -2px;
      }

      :deep(.el-input__inner) {
        padding: 0;
        width: 100%;
        height: 26px;
        text-align: center;
        background: none;
        border: none;
        box-shadow: none;
        color: var(--jzt-color-main);
        margin-top: -2px;
      }

      margin: 0 4px 0 10px;
    }
  }

  .page-text {
    font-size: 14px;
    color: #696C7D;
    margin-right: 10px;
  }
}
</style>