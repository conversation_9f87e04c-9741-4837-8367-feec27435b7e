<template>
  <edit-dialog
    ref="editDialogRef"
    v-model:dialogShow="dialogShow"
    v-bind="formConfig"
    @success="refreshList">
  </edit-dialog>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import { useDialog } from '@/hooks/useDialog'
import { addApi } from '@/api/media/inspiration'

const { EditDialog, editDialogRef, dialogShow, dialogData, openDialog } = useDialog()

const emits = defineEmits(['refresh'])

const formConfig = ref({
  title: '添加网址',
  width: '700px',
  formData: {},
  formConfig: {
    formList: [
      { label: '网站网址', prop: 'link', type: 'input', required: true, placeholder: 'http://' },
      { label: '网站名称', prop: 'title', type: 'input', required: true, placeholder: '请输入网站名称' },
      { label: '网站描述', prop: 'description', type: 'textarea', required: false, placeholder: '请输入网站详情说明' },
      { label: '网站图标', prop: 'icon', type: 'uploadImg', required: false, uploadConfig: { uploadBtn: true, width: '130px', height: '130px', position: 'right' } }
    ],
    submit: addApi
  }
})

const refreshList = () => {
  emits('refresh', true)
}

watch(dialogData, (newVal) => {
  console.log(newVal)
  if (newVal.id) {
    formConfig.value.title = '编辑网址'
    formConfig.value.formData = newVal
  } else {
    formConfig.value.title = '添加网址'
  }
})

defineExpose({
  openDialog
})

</script>

<style lang="scss" scoped>
p.tips {
  margin-top: 10px;
  color: red;
  width: 100%;
}
</style>