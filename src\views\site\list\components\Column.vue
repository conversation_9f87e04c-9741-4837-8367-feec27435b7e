<script setup lang="ts">
import DataTable from "@/components/Global/DataTable.vue";
import {changeSiteColumnApi, getSiteColumnApi} from "@/api/siteManage";
import {computed, ref} from "vue";
import {ElMessage, ElMessageBox} from "element-plus";
import {useDialog} from "@/hooks/useDialog";

const tableConfig = ref({
  tableData: [],
  columns: [
    {
      label: '显示',
      prop: 'booleanStatus',
      type: 'slot',
      width: 90,
    },
    {
      label: '名称',
      prop: 'title',
      type: 'slot',
    },
    {
      label: '页面名称',
      prop: 'jwp_title'
    },
    {
      label: '操作',
      prop: 'action',
      type: 'slot',
      width: 110,
    }
  ]
})

const getList = () => {
  getSiteColumnApi().then((res: any) => {
    tableConfig.value.tableData = res.map((v: any) => {
      return {
        ...v,
        booleanStatus: !!v.status
      }
    })
  })
}
getList()

const changeStatus = (row: any) => {
  ElMessageBox.confirm('真的要改变状态吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try{
      await changeSiteColumnApi({id: row.id})
      ElMessage.success('操作成功')
    }catch (e){
      row.status = row.status === 1 ? 0 : 1
      console.log(e);
    }
  }).catch(() => {
    row.status = row.status === 1 ? 0 : 1
  })
}

const { EditDialog, dialogShow, openDialog, closeDialog, editDialogRef } = useDialog()
const hostname = computed(() => {
  // 获取当前网站域名
  return window.location.hostname;
})
const baseUrl = computed(() => {
  return process.env.NODE_ENV !== 'development' ? 'https://jzt_dev_1.china9.cn/newclient/menus/create' : hostname.value === 'zhjzt.china9.cn' ? 'https://zhjzt.china9.cn/newclient/menus/create' : 'https://jzt_dev_1.china9.cn/newclient/menus/create'
})
const editIframeUrl = ref('')
const getEditIframeUrl = (data: {layoutId: string, id: string}) => {
  return baseUrl.value + '?layout_id=' + data.layoutId + '&id=' + data.id
}

const edit = (row: any) => {
  if(!row) return
  const data = {
    layoutId: row.layout_id,
    id: row.id
  }
  editIframeUrl.value = getEditIframeUrl(data)
  openDialog(row)
}
</script>

<template>
  <div class="h100 wrap">
    <data-table :show-multiple="false" :columns="tableConfig.columns" :table-data="tableConfig.tableData" :show-page="false">
      <template #booleanStatus="{row}">
        <el-switch v-if="row && Object.keys(row).length > 0" v-model="row.booleanStatus" inline-prompt active-text="开启" inactive-text="关闭" @change="() => changeStatus(row)" />
      </template>
      <template #title="{row}">
        <div class="title" v-html="row.title" />
      </template>
      <template #action="{row}">
        <div class="table-action">
          <div class="item" @click="edit(row)">
            <i class="iconfont icon-bianji"></i> <span>编辑</span>
          </div>
        </div>
      </template>
    </data-table>

    <edit-dialog ref="editDialogRef" :dialog-show="dialogShow" title="编辑导航" :custom-form="true" sure-text="关闭" cancel-text="" @close="closeDialog" @sure="closeDialog" width="80%" :show-bottom-btn="false">
      <div style="height: 70vh; overflow: auto">
        <iframe :src="editIframeUrl" frameborder="0" width="100%" height="100%" style="height: calc(100% - 4px);"></iframe>
      </div>
    </edit-dialog>
  </div>
</template>

<style scoped lang="scss">
.wrap {
  padding: 10px 0;
}
</style>
