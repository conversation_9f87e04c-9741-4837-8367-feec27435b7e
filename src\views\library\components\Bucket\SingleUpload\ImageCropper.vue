<template>
  <div class="img-wrap flex-1 flex-y">
    <div class="flex-1 h100" ref="cropperWrapRef">
      <vueCropper ref="cropperRef" :img="imgUrl" v-bind="cropperOption" @imgLoad="imgLoad" @realTime="realTime" />
      <!-- <div class="width400 height400">
      </div> -->
    </div>
    <el-form :inline="true" :model="cropConfig" class="bucket-settings mt20">
      <el-form-item class="mb0" label="比例">
        <el-input-number class="input-number-wrap width80" v-model="cropConfig.x" placeholder="1" type="number" :min="1" :controls="false" size="small" /> ：
        <el-input-number class="input-number-wrap width80" v-model="cropConfig.y" placeholder="1" type="number" :min="1" :controls="false" size="small" />
        <!-- 确定 按钮 -->
        <el-button class="ml10" type="primary" @click="changeCropper">确定</el-button>
      </el-form-item>
      <el-form-item class="mb0" label="">
        <el-radio-group v-model="cropConfig.fillMode" @change="changeFillMode">
          <el-radio-button :value="1">切割</el-radio-button>
          <el-radio-button :value="2">平铺</el-radio-button>
        </el-radio-group>
        <el-button type="primary" class="ml10" :icon="RefreshLeft" circle @click="rotateLeft" />
        <el-button type="primary" class="ml10" :icon="RefreshRight" circle @click="rotateRight" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { defineAsyncComponent, ref, watch } from 'vue'
import { RefreshLeft, RefreshRight } from '@element-plus/icons-vue'
import 'vue-cropper/dist/index.css'
import { VueCropper } from "vue-cropper"
import { gcd, calcImgSize } from '@/utils'

const props = defineProps({
  imgUrl: {
    type: String,
    default: ''
  },
  file: {
    type: Object,
    default: () => {}
  }
})

const emit = defineEmits(['changePreview', 'changePreviewFile'])

const cropperRef = ref<InstanceType<typeof VueCropper>>()
const cropperWrapRef = ref()

const cropperOption:any = ref({
  autoCrop: true,
  outputSize: 0.8,
  outputType: 'jpeg',
  fillColor: '#fff',
  full: true,
})
watch(() => props.file, (newVal) => {
  if (newVal) {
    cropperOption.value.outputType = newVal.type.split('/')[1]
  }
}, { immediate: true })

const cropConfig = ref({
  x: 1,
  y: 1,
  fillMode: 1,
  imgRealWidth: 0,
  imgRealHeight: 0,
})
const cropperWrap = ref({
  width: 0,
  height: 0,
})


watch(() => props.imgUrl, (newVal) => {
  if (newVal) {
    const img = new Image()
    img.src = newVal
    img.onload = () => {
      setCropperWrap()
      cropConfig.value.imgRealWidth = img.width
      cropConfig.value.imgRealHeight = img.height
      setCropper({ isInit: true })
    }
  }
}, { immediate: true })

const setCropperWrap = () => {
  const { clientWidth, clientHeight } = cropperWrapRef.value
  cropperWrap.value.width = clientWidth
  cropperWrap.value.height = clientHeight
}

// 设置裁剪比例
const setCropper = (option: any = {}) => {
  // isInit 是否设置裁剪框默认值
  // isSet 根据设置比例裁剪
  let isInit = option.isInit || false,
    isSet = option.isSet || false

  const { fillMode } = cropConfig.value
  const { imgRealWidth, imgRealHeight, x, y } = cropConfig.value
  const { width, height } = cropperWrap.value

  let setWidth = 0, setHeight = 0, proportion = imgRealWidth / imgRealHeight
  if (isSet && x && y) proportion = x / y
  if (imgRealWidth > width || imgRealHeight > height) {
    // 图片尺寸大于容器尺寸
    setWidth = width, setHeight = setWidth * imgRealHeight / imgRealWidth 
    if (setWidth > width || setHeight > height) setHeight = height, setWidth = height * imgRealWidth / imgRealHeight
  } else {
    setWidth = imgRealWidth, setHeight = imgRealHeight
  } 
  console.log(setWidth, setHeight, 'setWidth, setHeight')
  let imgSize = calcImgSize(setWidth, setHeight, proportion, fillMode)
  console.log(imgSize, 'imgSize')
  setCropConfig(imgSize, option)
}

const setCropConfig = (imgSize: any, option: any = {}) => {
  let { newWidth: width, newHeight: height, x, y } = imgSize
  // isInit 是否设置裁剪框默认值
  // isSet 根据设置比例裁剪
  let isInit = option.isInit || false,
    isSet = option.isSet || false
  if (isInit || isSet) {
    cropperOption.value.autoCropWidth = width
    cropperOption.value.autoCropHeight = height
  }
  if (isSet) return
  if (width == height) {
    cropConfig.value.x = 1
    cropConfig.value.y = 1
  } else {
    // 最大公约数
    const maxGcd = gcd(width, height)
    console.log(maxGcd, '最大公约数')
    cropConfig.value.x = Math.round(width) / maxGcd
    cropConfig.value.y = Math.round(height) / maxGcd
  }
}

// 图片实时预览
const realTime = (e: any) => {
  console.log(e)
  const { w, h } = e
  setCropConfig({ newWidth: w, newHeight: h})
  // emit('changePreview', e)
  console.log(cropperRef.value, 'cropperRef.value ')
  cropperRef.value && cropperRef.value.getCropData((data: any) => {
    // console.log(data)
    emit('changePreview', data)
  })
  cropperRef.value && cropperRef.value.getCropBlob((data: any) => {
    console.log(data)
    emit('changePreviewFile', data)
  })
}

// 图片加载成功
const imgLoad = (e: any) => {
  console.log(e)
  if (e == 'success') {
    
  }
}

// 图片旋转
const rotateLeft = () => {
  cropperRef.value && cropperRef.value.rotateLeft()
}
const rotateRight = () => {
  cropperRef.value && cropperRef.value.rotateRight()
}

const changeFillMode = () => {
  setCropper({ isSet: true })
}

// 输入比例点击确定
const changeCropper = () => {
  setCropper({ isSet: true })
}

</script>

<style scoped lang="scss">

</style>