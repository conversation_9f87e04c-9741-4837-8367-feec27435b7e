import { ref, defineAsyncComponent } from 'vue'

export const useDialog = () => {

  const EditDialog = defineAsyncComponent(() => import('@/components/EditDialog/index.vue'))
  const editDialogRef = ref()

  const dialogData = ref()

  const dialogShow = ref(false)

  const openDialog = (row: any) => {
    dialogShow.value = true
    dialogData.value = row
    editDialogRef.value.open(row)
  }

  const closeDialog = () => {
    dialogShow.value = false
  }

  return {
    EditDialog,
    editDialogRef,
    dialogData,
    dialogShow,
    openDialog,
    closeDialog
  }
}