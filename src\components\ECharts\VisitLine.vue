<template>
  <div ref="visitDom" :style="`width: ${width}; height: ${height};`"></div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from "vue";
import * as echarts from "echarts";

const props = defineProps({
  from: {
    type: String,
    default: 'visit'
  },
  data: {
    type: Object,
    default: {}
  },
  width: {
    type: String,
    default: '180px'
  },
  QDColor: {
    type: Array,
    default: ['#0012BF', '#2859FF', '#46C4FF', '#B4A5FF', '#15FFFC']
  },
  height: {
    type: String,
    default: '208px'
  },
  isShowTitle: {
    type: Boolean,
    default: true
  }
})

// 初始化访问量图表
const visitDom = ref(null);
// 初始化访问量
const visitInit = (xLabel: any, dataList: any) => {
  console.log(xLabel, dataList);
  var dom = visitDom.value
  var myChart = echarts.init(dom);
  var option;

  let nameData: any = [];
  let listData: any = [];
  dataList.map((item: any, index: number) => {
    nameData.push({
      name: item.name
    })
    listData.push({
      name: item.name,
      type: 'line',
      symbol: 'circle', // 默认是空心圆（中间是白色的），改成实心圆
      showAllSymbol: true,
      symbolSize: 0,
      smooth: true,
      lineStyle: {
        // normal: {
          width: 2,
          color: props.QDColor[index], // 线条颜色
        // },
        borderColor: 'rgba(0,0,0,.4)',
      },
      itemStyle: {
        color: props.QDColor[index],
        borderColor: '#646ace',
        borderWidth: 1,
      },
      tooltip: {
        show: true,
      },
      areaStyle: {
        //区域填充样式
        // normal: {
          //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            0,
            1,
            [
              {
                offset: 0,
                color: props.QDColor[index] + '80',
              },
              {
                offset: 1,
                color: props.QDColor[index] + '22',
              },
            ],
            false
          ),
          shadowColor: props.QDColor[index] + '33', //阴影颜色
          shadowBlur: 20, //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
        // },
      },
      data: item.list,
    });
  });
  let legend = <any> {
    align: 'left',
    top: '5px',
    type: 'plain',
    textStyle: {
      color: 'rgba(105, 108, 125, 1)',
      fontSize: 16
    },
    // icon: 'rect',
    itemGap: 30,
    itemWidth: 30,
    itemHeight: 4,
    itemStyle: {
      borderWidth: 0,
    },
    icon: 'path://M0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z',
    data: nameData
  }

  if(props.from == 'visit') { legend.show = props.isShowTitle; legend.left = '15%' }
  if(props.from == 'report') { legend.show = true; legend.right = '0%' }

  option = {
    backgroundColor: '#fff',
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'transparent',
      axisPointer: {
        lineStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0,
              color: 'rgba(126,199,255,0)' // 0% 处的颜色

            }, {
              offset: 0.5,
              color: 'rgba(126,199,255,1)' // 100% 处的颜色

            }, {
              offset: 1,
              color: 'rgba(126,199,255,0)' // 100% 处的颜色

            }],
            global: false // 缺省为 false

          }
        }
      },
      formatter: (p: any) => { }
    },
    legend,
    grid: {
      top: '50px',
      left: '50px',
      right: '20px',
      bottom: '20px' // containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        axisLine: {
          //坐标轴轴线相关设置。数学上的x轴
          show: true,
          lineStyle: {
            color: 'transparent'
          }
        },
        axisLabel: {
          //坐标轴刻度标签的相关设置
          // textStyle: {
          color: 'rgba(167, 171, 187, 1)',
          padding: 0,
          fontSize: 14,
          // },
          formatter: (data: any) => {
            return data;
          }
        },
        splitLine: {
          show: false,
          lineStyle: {
            color: '#000'
          }
        },
        axisTick: {
          show: false
        },
        data: xLabel
      }
    ],
    yAxis: [{
      type: 'value',
      name: props.isShowTitle ? '访问统计' : '',
      nameTextStyle: {
        color: 'rgba(28, 43, 75, 1)',
        fontSize: 16,
        padding: 10,
        marginLeft: -10,
        fontWeight: 'bold'
      },
      min: 0,
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(167, 171, 187, 0.4)'
        }
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(167, 171, 187, 1)'
        }
      },
      axisLabel: {
        show: true,
        // textStyle: {
        color: 'rgba(167, 171, 187, 1)',
        padding: 0,
        // },
        formatter: function formatter(value: number) {
          if (value === 0) {
            return value;
          }

          return value;
        }
      },
      axisTick: {
        show: false
      }
    }],
    series: listData
  };

  myChart.setOption(option);
}

watch(
  () => props.data,
  () => {
    // visitInit(props.data)
    let data = props.data
    let dataList
    if (data.look) {
      dataList = [
        { name: '浏览量', list: data.look },
        { name: '访客量', list: data.ip },
        // { name: 'IP数', list: data.ip }
      ]
    }else {
      dataList = data.data
    }
    visitInit(data.date || data.datetime, dataList)
  }
)
</script>

<style scoped lang="scss">

</style>
