<script setup lang="ts">
import {computed, defineAsyncComponent, nextTick, ref} from "vue";
import store from "@/store";
import {addPageApi, changePageNameApi, copyPageApi, deletePageApi, getSiteListApi, getTemplateApi} from "@/api/website";
import {useDialog} from "@/hooks/useDialog";
import JztButton from "@/components/Global/JztButton.vue";
import DataPage from "@/components/Global/DataPage.vue";
import {ElMessage, ElMessageBox, formatter} from "element-plus";

const Navigation = defineAsyncComponent(() => import('@/views/navigation/index.vue'));

const {EditDialog, editDialogRef: navDialogRef, dialogShow: navDialogShow, dialogData: navDialogData, openDialog: openNavDialog, closeDialog: closeNavDialog} = useDialog()
const {editDialogRef, dialogShow: editDialogShow, dialogData: editDialogData, openDialog: openEditDialog, closeDialog: closeEditDialog} = useDialog()

let baseUrl = ref('');
let previewUrl = ref('');
const useDevLinkList = ['localhost', 'jzt_dev_1.china9.cn'];
if (useDevLinkList.includes(window.location.hostname)) {
  baseUrl.value = 'https://jzt_dev_1.china9.cn/';
  previewUrl.value = 'http://jzt_dev_2.china9.cn/';
} else {
  baseUrl.value = 'https://zhjzt.china9.cn/';
  previewUrl.value = 'https://ijzt.china9.cn/';
}

const siteInfo = store.getters.siteInfo;
const company_id = siteInfo.company_id;

/**
 * 获取layout_id和domain
 */
const info = ref({
  layout_id: '',
  site_id: '',
  id: '',
  status: 3,
})
const siteId = computed(() => store.getters.siteId);
const loading = ref(false);
const getLayoutIdAndDomain = async () => {
  loading.value = true;
  try {
    const res: any = await getTemplateApi();
    info.value.layout_id = res.layout_id;
    info.value.site_id = res.site_id || siteId.value;
    info.value.id = res.id;
    info.value.status = res.status || 3;

    await getSiteList(info.value);
  }catch (e) {
    console.log(e);
  } finally {
    loading.value = false;
  }
}
getLayoutIdAndDomain();

const list = ref<{ [key: string]: any }[]>([]);
const total = ref(0);
const page = ref(1);
const lPage = ref(1);
const getSiteList = async (data: any) => {
  try {
    const res = await getSiteListApi({...data, page: page.value}) as any;
    list.value = res.data.map((v: any) => {
      v.editing = false;
      v.oldTitle = v.title;
      return v;
    });
    total.value = res.total;
    page.value = res.current_page;
    lPage.value = res.last_page;
  }catch (e) {
    console.log(e);
  }finally {}
}

/**
 * 设置导航
 */
const setNav = () => {
  openNavDialog({});
}

/**
 * 编辑
 * @param item
 */
const edit = (item: any) => {
  nextTick(() => {
    item.editing = true;
  })
}

/**
 * 保存
 * @param item
 */
const save = async (item: any) => {
  item.editing = false;
  loading.value = true;
  try{
    await changePageNameApi({title: item.title, id: item.id, jwpagefactory_id: item.jwpagefactory_id, layout_id: item.layout_id, site_id: item.site_id})
    await getSiteList(info.value);
  }catch (e){
    item.title = item.oldTitle;
    console.log(e);
  }finally {
    loading.value = false;
  }
}

/**
 * 删除
 * @param item
 */
const del = (item: any) => {
  ElMessageBox.confirm('确定删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    loading.value = true;
    try {
      await deletePageApi({id: item.jwpagefactory_id, layout_id: item.layout_id, site_id: item.site_id});
      ElMessage.success('删除成功');
      await getSiteList(info.value);
    }catch (e) {
      console.log(e);
    }finally {
      loading.value = false;
    }
  }).catch((e) => {
    ElMessage.error(`删除失败：${e}`);
  });
}

/**
 * 复制页面
 * @param item
 */
const copy = async (item: any) => {
  loading.value = true;
  try {
    await copyPageApi({id: item.jwpagefactory_id, layout_id: item.layout_id, site_id: item.site_id});
    ElMessage.success('复制成功');
    await getSiteList(info.value);
  }catch (e) {
    console.log(e);
  }finally {
    loading.value = false;
  }
}

const changePage = (p: number) => {
  console.log(page.value);
  getSiteList(info.value);
}

/**
 * 编辑页面
 */
const createPage = () => {
  openEditDialog({});
}

/**
 * 创建页面
 */
const form = ref({
  title: '',
})
const rules = {
  title: [
    { required: true, message: '请输入页面名称', trigger: 'blur' },
    { min: 1, max: 10, message: '请输入1-10个字符', trigger: 'blur' },
  ],
}
const formRef = ref();
const createPageForm = async () => {
  const res = await (formRef.value as any)?.validate();
  if (!res) return;
  try {
    await addPageApi({title: form.value.title, layout_id: info.value.layout_id, site_id: info.value.site_id});
    ElMessage.success('创建成功');
    await getSiteList(info.value);
    closeEditDialog();
  }catch (e) {
    console.log(e);
  }
}
const cancel = () => {
  form.value.title = '';
  closeEditDialog();
}
</script>

<template>
  <div class="wrap h100" v-loading="loading">
    <div class="btns flex">
      <jzt-button @click="setNav" class="set-nav">设置导航</jzt-button>
    </div>
    <div class="list">
      <div style="overflow: hidden">
        <el-row :gutter="20">
        <el-col :lg="6" :md="8" :sm="12" :xs="1">
          <div class="item new-page flex-column align-center flex-c-center flex" @click="createPage">
            <div class="icon-box"></div>
            <div class="title">添加空白页</div>
          </div>
        </el-col>
        <el-col :lg="6" :md="8" :sm="12" :xs="1" v-for="item in list" :key="item.id">
          <div class="item">
            <div class="img-box flex align-center justify-between" :style="{backgroundImage: `url('${require('@/assets/images/website/page-bg.png')}')`}">
              <div class="flex align-center flex-1"
                   style="width: calc(100% - 82px - 14px);padding: 10px 0;box-sizing: border-box;">
                <div class="icon-box flex align-center flex-c-center">
                  <img src="@/assets/images/website/page-avatar-icon.png" alt="">
                </div>
                <div class="c-info">
                  <div class="company-name flex align-center" :title="item.title">
                    <el-input :id="`item_${item.id}`" :maxlength="10" v-if="item.editing" v-model="item.title" />
                    <div v-else>{{ item.title }}</div>
                    <i class="iconfont icon-bianji1 edit-btn" v-if="!item.editing" @click="edit(item)"></i>
                    <i class="iconfont icon-duihao save-btn" v-else @click="save(item)"></i>
                  </div>
                </div>
              </div>
              <div class="b-info">
                <a :href="item.look_url"
                   target="_blank" class="flex-c-center">
                  <i class="iconfont icon-webduanmobancaozuo_yulan"></i>&nbsp;
                  <span>预览</span>
                </a>
              </div>
            </div>
            <div class="bot-info">
              <div class="time">创建时间：{{ formatter(item.create_time * 1000, 'YYYY-MM-DD hh:mm:ss', 'zh-cn') }}</div>
              <div class="bot-btn flex flex-c-center">
                <a class="read flex align-center flex-c-center"
                   :href="item.edit_url"
                   target="_blank">
                  <img src="@/assets/images/website/u012.png" alt=""><span>编辑</span>
                </a>
                <a class="a-btn" @click="del(item)">删除</a>
                <a class="a-btn" @click="copy(item)">复制</a>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
      </div>
    </div>
    <div v-if="total > 0" class="pagination">
      <data-page v-model:page="page" :total="total" :totalPage="lPage" @pagination="changePage"></data-page>
    </div>

    <edit-dialog ref="navDialogRef" title="设置导航" :dialog-data="navDialogData" :dialog-show="navDialogShow"
                 :show-bottom-btn="false" @close="closeNavDialog" width="86%" :custom-form="true">
      <div style="max-height: 600px;overflow:auto;">
        <Navigation />
      </div>
    </edit-dialog>

    <edit-dialog ref="editDialogRef" title="创建页面" :dialog-data="editDialogData" :dialog-show="editDialogShow"
                 :show-bottom-btn="false" @close="closeEditDialog" :custom-form="true">
      <el-form :model="form" :rules="rules" ref="formRef">
        <el-form-item label="页面名称" prop="title">
          <el-input v-model="form.title" placeholder="请输入页面名称"></el-input>
        </el-form-item>
        <el-form-item label-width="0">
          <div class="flex-c-center flex-1">
            <el-button type="primary" @click="createPageForm" style="width: 120px;">确定</el-button>
            <el-button @click="cancel" style="width: 120px;">取消</el-button>
          </div>
        </el-form-item>
      </el-form>
    </edit-dialog>
  </div>
</template>

<style scoped lang="scss">
.wrap {
  height: 100%;
  box-sizing: border-box;
  padding: 20px 30px;
  position: relative;
  .btns {
    .set-nav {
      width: 94px;
      height: 34px;
      border-radius: 17px;
    }
  }
  .list {
    height: calc(100% - 34px - 62px);
    margin-top: 20px;
    box-sizing: border-box;
    overflow: auto;
    .item {
      margin-bottom: 20px;
      height: 210px;
      cursor: pointer;
      background: rgb(255, 255, 255);
      border-radius: 10px;
      transition: 0.3s ease-in-out;
      overflow: hidden;
      border-width: 1px;
      border-style: solid;
      border-color: rgb(230, 236, 245);
      border-image: initial;
      &:hover {
        box-shadow: rgba(216, 224, 234, 0.6) 0px 6px 12px 0px;
        border-color: transparent;
      }
      .icon-box {
        background-color: #3F79FF;
        box-shadow: 0px 4px 8px 0px rgba(44, 129, 255, 0.18);
        position: relative;
        width: 42px;
        height: 42px;
        border-radius: 50%;
      }
      &.new-page{
        .icon-box {
          &::after,
          &::before {
            content: "";
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            margin: auto;
            background-color: #fff;
            border-radius: 3px;
          }
          &::before {
            width: 3px;
            height: 50%;
          }
          &::after {
            width: 50%;
            height: 3px;
          }
          & + .title {
            font-weight: 800;
            font-size: 18px;
            color: #1C2B4B;
            margin-top: 18px;
          }
        }
      }
      .img-box {
        height: 90px;
        width: 100%;
        box-sizing: border-box;
        padding: 0px 20px;
        background-size: 100% 100%;
        .c-info{
          width: calc(100% - 42px - 14px);
          flex: 1;
          margin-left: 14px;
          .company-name {
            font-size: 18px;
            font-weight: 800;
            color: #2859FF;
            margin-bottom: 0;
            width: 80%;
            &>div{
              margin-right: 10px;
              font-weight: 800;
              font-size: 18px;
              color: #1C2B4B;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              max-width: calc(100% - 20px);
              min-height: 22px;
              &.editing{
                width: 100%;
                max-height: 60px;
                padding: 12px 16px;
                box-sizing: border-box;
                border-bottom: 1px solid #B6B8C5;
                /* 去掉单行文本溢出 */
                text-overflow: hidden;
                white-space: wrap;
                overflow: auto;
              }
            }
            &>i{
              color: #B6B8C5;
            }
          }
        }
        .b-info a{
          width: 80px;
          height: 30px;
          line-height: 30px;
          text-align: center;
          font-size: 14px;
          font-weight: 500;
          color: #0048FF;
          background: #FFFFFF;
          border: 1px solid #A0B6FF;
          border-radius: 15px;
        }
      }
      .bot-info{
        font-size: 14px;
        font-weight: 500;
        color: rgb(89, 106, 142);
        padding: 20px;
        .time {
          margin-bottom: 10px;
        }
        .bot-btn {
          margin-top: 23px;
          .read {
            min-width: 100px;
            height: 36px;
            box-shadow: rgb(212, 226, 253) 0px 4px 8px 0px;
            font-size: 14px;
            font-weight: bold;
            color: rgb(255, 255, 255);
            text-align: center;
            background: linear-gradient(90deg, rgb(40, 89, 255) 0%, rgb(62, 124, 255) 100%);
            border-radius: 18px;
            padding: 0px 14px;
            flex: 1.4;
            span{
              margin-left: 8px;
            }
          }
          .a-btn {
            display: inline-block;
            height: 36px;
            text-align: center;
            line-height: 36px;
            font-size: 14px;
            font-weight: 500;
            color: rgb(0, 72, 255);
            margin-left: 12px;
            background: rgb(255, 255, 255);
            border-width: 1px;
            border-style: solid;
            border-color: rgb(160, 182, 255);
            border-image: initial;
            border-radius: 18px;
            flex: 1 1 0%;
          }
        }
      }
    }
  }
}
</style>
