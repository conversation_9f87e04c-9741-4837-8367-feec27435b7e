import { computed } from 'vue'
import store from '@/store'
import { ElMessage } from 'element-plus';

export const useMedia = () => {

  // 是否AI版
  const isAgent = computed(() => store.getters.isAgent)
  
  // 链接跳转
  const jumpUrl = (url: string) => {
    if (isAgent.value) {
      let str = url.split('?')[1], query: any = []
      if (str) query = str.split('&')
      query.forEach((item: any) => {
        let key = item.split('=')[0], value = item.split('=')[1]
        if (key == 'type') window.localStorage.setItem('media_type', value)
      })
      window.open(url)
    } else {
      ElMessage.error('请联系商务升级AI版使用！')
    }
  }

  return {
    isAgent, jumpUrl
  }
}