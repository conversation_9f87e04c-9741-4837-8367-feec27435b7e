<script setup lang="ts">
import GzhName from "./components/GzhName.vue";
import JztButton from "@/components/Global/JztButton.vue";
import {getImageTextDetailApi} from "@/api/report/imgText";
import {getContentAnalysisApi} from "@/api/report/gzh";
import {computed, markRaw, ref, watch} from "vue";
import StatisticsList from "../../components/StatisticsList.vue";
import BarChart from "./components/BarChart.vue";
import RadioButtonGroup from "./components/RadioButtonGroup.vue";
import LineChart from "./components/LineChart.vue";
import PdfPage from "@/views/media/components/PdfPage.vue";
import ExportText from "./components/ExportText.vue";
import {exportPDF} from "@/utils/htmlToPdf";

const props = defineProps({
  params: {
    type: Object,
    default: () => ({})
  }
})

const emits = defineEmits(['changeNav']);

/*返回图文列表*/
const toList = () => {
  emits('changeNav', 1);
}

/*公众号信息*/
const gzhInfo:any = ref({});
const setGzhInfo = (data: any) => {
  gzhInfo.value = data;
}

/*文章*/
const contentList = ref<{title: string; ref_date: string; [key: string]: any}>({title: '', ref_date: ''});

/*数据统计列表*/
const dataList = ref<{ icon: string, title: string, value: string | number }[]>([
  {title: '总阅读(次)', icon: require('@/assets/images/report/item01.png'), value: 0},
  {title: '总分享(次)', icon: require('@/assets/images/report/item02.png'), value: 0},
  {title: '阅读后关注(人)', icon: require('@/assets/images/report/item03.png'), value: 0},
]);

/*送达转化*/
const deliveryData = ref([
  {name: '公众号消息阅读次数', value: 0},
  {name: '送达人数', value: 0}
]);
// 转化率
const conversionRate = ref(0);

/*分享统计*/
const shareData = ref([
  {name: '其他分享次数', value: 0},
  {name: '朋友圈分享次数', value: 0},
  {name: '公众号分享次数', value: 0},
  {name: '总分享次数', value: 0},
]);

/*获取详情*/
const loading = ref(false);
const date = ref([]);
const getDetail = async () => {
  if(!gzhInfo.value.listss || !gzhInfo.value.listss.msgid) return;
  loading.value = true;
  try{
    const res: any = await getImageTextDetailApi({msgid: gzhInfo.value.listss.msgid});
    contentList.value = res.list;

    dataList.value[0].value = res.list.z_readnum;
    dataList.value[1].value = res.list.z_fxnum;
    dataList.value[2].value = res.list.z_readren;

    deliveryData.value[0].value = res.list.z_readnum;
    deliveryData.value[1].value = res.target_user;
    conversionRate.value = res.zhl;

    shareData.value[0].value = res.qtfx;
    shareData.value[1].value = res.pyqfx;
    shareData.value[2].value = res.gzhfx;
    shareData.value[3].value = res.z_fxnum;

    date.value = res.datelist;
  }catch (e) {
    console.log(e);
  }finally {
    loading.value = false;
  }
};
watch(() => gzhInfo.value, getDetail, {immediate: true, deep: true});

/*barchart颜色*/
const colors: string = '#3583FB';
/*barchart图例*/
const deliveryLegend = computed(() => {
  return {
    show: true,
    data: ['转化率'],
    formatter: (name: string) => {
      return name + '：' + conversionRate.value + '%';
    },
    textStyle: {
      fontSize: 14,
    }
  }
});

/*数据趋势筛选*/
const trendForm = ref({
  contype: 'yd',
  contypetwo: 'qb'
});
const trendOptions = markRaw({
  index: [
    {label: '图文阅读', value: 'yd'},
    {label: '图文分享', value: 'fx'}
  ],
  channel: [
    {label: '全部', value: 'qb'},
    {label: '公众号消息', value: 'gzh'},
    {label: '聊天会话', value: 'lthh'},
    {label: '朋友圈', value: 'pyq'},
    {label: '朋友在看', value: 'pyzk'},
    {label: '看一看精选', value: 'pyzk'},
    {label: '看一看精选', value: 'kyk'},
    {label: '搜一搜', value: 'sys'},
    {label: '公众号主页', value: 'gzhzy'},
    {label: '其他', value: 'qt'},
  ]
});
const changeDataType = () => {
  trendForm.value.contypetwo = 'qb'
};
const trendColors = ['rgba(53, 131, 251, 1)', 'rgba(253, 178, 5, 1)'];
/*内容分析数据*/
interface ContentData {
  date: string[];
  data: any[];
}
const trendData = ref<ContentData>({
  date: [],
  data: []
})
const trendLoading = ref(false);
const getTrend = async () => {
  trendLoading.value = true;
  try{
    const res: any = await getContentAnalysisApi(trendForm.value);
    trendData.value.date = res.datenewlist;
    trendData.value.data = [
      {name: res.nameone, list: res.content_cnum}, {name: res.nametwo, list: res.content_rnum}
    ];
  }catch (e) {
    console.log(e);
  }finally {
    trendLoading.value = false;
  }
}
watch(() => trendForm.value, getTrend, {immediate: true, deep: true});

/*导出*/
const pdfWidth = "calc(841.89px * 1.5)";
const pdfHeight = "calc(595.28px * 1.5)";
const exportPdf = () => {
  setTimeout(() => {
    exportPDF(gzhInfo.value.gzhtitle + '-单篇分析', 'export')
  }, 100)
};

/*导出数据处理*/
const deliveryList = computed<{ name: string, value: string | number }[]>(() => {
  return [
    {name: '时间', value: gzhInfo.value.ttday},
    ...deliveryData.value,
    {name: '送达转换率', value: conversionRate.value + '%'},
  ]
});
const trendList = computed<{ name: string, value: string | number }[]>(() => {
  const dateStr = date.value[0] + '~' + date.value[date.value.length - 1];
  const dataTypeStr = trendOptions.index.find((v: any) => v.value === trendForm.value.contype)?.label || '';
  const channelStr = trendOptions.channel.find((v: any) => v.value === trendForm.value.contype)?.label || '';
  return [
    {name: '时间', value: dateStr},
    {name: '数据类型', value: dataTypeStr},
    {name: '渠道', value: channelStr}
  ]
});
</script>

<template>
  <div class="h100" style="overflow: hidden">
    <div class="wrap h100">
      <div class="content-box">
        <div class="gzh-list flex-between flex-align-center">
          <gzh-name :show-list="false" @set-gzh-info="setGzhInfo" />
          <div class="btns flex">
            <jzt-button name="返回图文列表" @click="toList" />
            <el-button type="primary" @click="exportPdf" class="primary-btn">
              <i class="iconfont icon-daochu" />
              导出
            </el-button>
          </div>
        </div>
        <div class="t-box mt-20 border">
          <p class="t-title">{{ contentList.title }}</p>
          <p class="t-time">{{ contentList.ref_date }}</p>
        </div>
        <div class="statistics mt-20 border">
          <statistics-list :data="dataList" :col-settings="{md: 8, xs: 24}" />
        </div>
        <div class="delivery mt-20 border">
          <div class="p-title">送达转化</div>
          <div class="chart-wrap mt-20" v-loading="loading">
            <bar-chart :colors="colors" width="100%" height="300px" :data="deliveryData" :legend="deliveryLegend" />
          </div>
        </div>
        <div class="share mt-20 border">
          <div class="p-title">分享统计</div>
          <div class="chart-wrap mt-20" v-loading="loading">
            <bar-chart :colors="colors" width="100%" height="500px" :data="shareData" :legend="{show: false, data: ['分享统计']}" />
          </div>
        </div>
        <div class="trend mt-20 border" v-loading="loading">
          <div class="p-title">数据趋势</div>
          <el-form :model="trendForm" label-width="100px" class="px-20 mt-20">
            <el-form-item label="数据指标">
              <radio-button-group v-model:value="trendForm.contype" size="large" @change="changeDataType" :list="trendOptions.index" type="primary" />
            </el-form-item>
            <el-form-item label="渠道">
              <radio-button-group v-model:value="trendForm.contypetwo" size="large" :list="trendOptions.channel" />
            </el-form-item>
          </el-form>
          <div class="trend-chart w100 px-20" v-loading="trendLoading">
            <line-chart :colors="trendColors" width="100%" height="500px" :data="trendData.data" :x-axis-data="trendData.date" />
          </div>
        </div>
      </div>
    </div>
    <div id="export" :style="`background: #fff;width: ${pdfWidth}; margin-top: 200px;`">
      <div
          class="page-1"
          :style="`width: ${pdfWidth};height: ${pdfHeight};display: flex;flex-direction: column;align-items: center;justify-content: center;page-break-after: always;`"
      >
        <h1 class="title" style="font-weight: bold; font-size: 52px;">
          微信公众号运营报告
        </h1>
        <h2 style="font-weight: bold; margin-top: 20px; font-size: 26px;">
          公众号名称：{{ gzhInfo.title }}
        </h2>
      </div>
      <pdf-page>
        <template #textInfo>
          <p style="font-weight: bold;font-size: 26px;padding-bottom: 20px;">{{ contentList.title }}</p>
          <p style="font-size: 18px;padding-bottom: 20px;">{{ contentList.ref_date }}</p>
          <div style="padding: 0 30px">
            <yesterday-list :data="dataList" :col-settings="{md: 8, xs: 24}"/>
          </div>
        </template>
      </pdf-page>
      <pdf-page>
        <template #textInfo>
          <export-text title="送达转化" :list="deliveryList" />
        </template>
        <template #canvas>
          <div style="padding: 0 30px; margin-top: 20px">
            <bar-chart :colors="colors" width="100%" height="300px" :data="deliveryData" :legend="deliveryLegend" />
          </div>
        </template>
      </pdf-page>
      <pdf-page>
        <template #textInfo>
          <export-text title="分享统计" :list="shareData" />
        </template>
        <template #canvas>
          <div style="padding: 0 30px; margin-top: 20px">
            <bar-chart :colors="colors" width="100%" height="300px" :data="shareData" :legend="{show: false, data: ['分享统计']}" />
          </div>
        </template>
      </pdf-page>
      <pdf-page>
        <template #textInfo>
          <export-text title="数据趋势" :list="trendList" />
        </template>
        <template #canvas>
          <div style="padding: 0 30px; margin-top: 20px">
            <line-chart :colors="trendColors" width="100%" height="500px" :data="trendData.data" :x-axis-data="trendData.date" />
          </div>
        </template>
      </pdf-page>
    </div>
  </div>
</template>

<style scoped lang="scss">
@import "src/views/media/report/gzh/style/common";
.wrap{
  .primary-btn{
    height: 34px;
    & + .material-upload{
      margin-left: 10px;
    }
  }
  .material-upload + .primary-btn{
    margin-left: 10px;
  }
}
</style>
