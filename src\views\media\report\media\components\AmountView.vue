<script setup lang="ts">
import {computed} from "vue";

interface Props {
  success: number,
  fail: number,
}
const props = defineProps<Props>()

/*获取成功和失败列表*/
const getSuccessAndFailList = computed(() => {
  return [
    {label: '成功次数', value: props.success ?? '--'},
    {label: '失败次数', value: props.fail ?? '--'},
  ]
})
</script>

<template>
  <div class="amount-view w100">
    <div
        v-for="(item, index) in getSuccessAndFailList"
        :key="index" class="amount-item flex-between">
      <span class="name">{{ item.label }}</span> <span class="number">{{ item.value }}</span>
    </div>
  </div>
</template>

<style scoped lang="scss">
.amount-view{
  .amount-item{
    & + .amount-item{
      margin-top: 8px;
    }
    .name{
      font-size: 12px;
      color: #666;
    }
    .number{
      font-weight: bold;
      font-size: 14px;
      color: #666;
    }
  }
}
</style>
