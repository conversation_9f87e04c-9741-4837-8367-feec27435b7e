<template>
  <div class="home-container flex" id="index-app" v-cloak>
    <!-- 左侧 -->
    <div class="left-box flex-wrap">
      <!-- 常用操作 -->
      <div class="content-box action-container">
        <p class="new-title">常用操作</p>
        <div class="action-box flex-wrap">
          <template v-for="(item, index) in actionList">
            <a
              @click="actionToPath(item.url)"
              class="action-item flex"
              :style="{
                background: item.bg
              }"
            >
              <img :src="require('@/assets/images/index/' + item.icon)" alt="" />
              <div class="info">
                <p class="t-title">{{ item.title }}</p>
                <p class="t-p">{{ item.desc }}</p>
              </div>
            </a>
          </template>
        </div>
      </div>
      <!--网站使用报告-->
      <website-usage-report />
      <!--各端二维码-->
      <q-r-code-view />
    </div>
    <!-- 右侧 -->
    <div class="right-box">
      <!-- 近期重要营销日期推荐 -->
      <template v-if="recommendList.length">
        <div class="content-box market-container">
          <p class="new-title">近期重要营销日期推荐</p>
          <div
            class="market-box"
            :style="'background-image: url(' + recommendList[0].bgimg + ');'"
          >
            <div class="market-main flex">
              <div class="time-info">
                <p class="title">{{ recommendList[0].title }}</p>
                <p class="date">
                  {{ recommendList[0].date }} {{ recommendList[0].Weekday }}
                </p>
              </div>
              <div class="day-box flex">
                <span class="number">{{ recommendList[0].days }}</span>
                <span class="text">天后</span>
              </div>
            </div>
          </div>
        </div>
      </template>
      <!--媒介直达-->
      <media-view />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
// import { getListApi } from '@/api/media/plan'
import useDashboard from '@/views/dashboard/hooks/useDashboard'

const { WebsiteUsageReport, QRCodeView, MediaView } = useDashboard()

// 常用操作 ********控制台改版
const actionList = ref([
  {
    url: "/media/sendArticle",
    title: "发文章",
    desc: "媒体内容快速发布与更新",
    icon: "fnr.png",
    bg: "linear-gradient(90deg, #397AFA, #3EB0FF)"
  },
  {
    url: "/library",
    title: "传素材",
    desc: "全网素材物料一站式管理",
    icon: "csc.png",
    bg: "linear-gradient(90deg, #FFA11B, #FFB864)"
  },
  {
    url: "/media/account",
    title: "管帐号",
    desc: "媒体平台账号管理及内容发布",
    icon: "gzh.png",
    bg: "linear-gradient(90deg, #6599F7, #7FC9FF)"
  },
  {
    url: "/media/plan",
    title: "宣传计划",
    desc: "营销热点提前掌握规划",
    icon: "xcjh.png",
    bg: "linear-gradient(90deg, #C698FF, #8659F8)"
  },
  {
    url: "/report/website",
    title: "看数据",
    desc: "全域媒体营销分析报告",
    icon: "ksj.png",
    bg: "linear-gradient(90deg, #22A1E0, #21C6D9)"
  },
  {
    url: "/media/inspiration",
    title: "找灵感",
    desc: "快速发现与捕捉营销创意",
    icon: "zlg.png",
    bg: "linear-gradient(90deg, #0FC7A2, #59E7A8)"
  }
]);

// 获取控制台数据
const initDashboard = () => {
  getRecommendList()
};

// 获取推荐列表
const recommendList:any = ref([])
const getRecommendList = async () => {
  // const res:any = await getListApi()
  // recommendList.value = res
}

// 常用操作跳转
const actionToPath = (url: string) => {
//   if (url == '/content') {

//   } else router.push(url)
}

onMounted(() => {
  initDashboard();
});

</script>

<style lang="scss" scoped>
@import "../style/index.scss";
// 文字超出一行..
.one-line {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.home-container {
  align-items: flex-start;
  // 左侧
  .left-box {
    flex: 1;
    .action-container {
      width: 100%;
      .action-box {
        .action-item {
          width: 390px;
          padding: 20px;
          box-sizing: border-box;
          background: linear-gradient(90deg, #397afa, #3eb0ff);
          border-radius: 10px;
          width: calc(100% / 3 - (20px * 2) / 3);
          margin-right: 20px;
          margin-top: 20px;
          &:nth-child(3n) {
            margin-right: 0;
          }
          img {
            width: 56px;
            height: 56px;
            object-fit: scale-down;
            margin-right: 18px;
          }
          .info {
            flex: 1;
            overflow: hidden;
            .t-title {
              font-weight: 800;
              font-size: 20px;
              color: #ffffff;
              margin-bottom: 6px;
            }
            .t-p {
              font-weight: 500;
              font-size: 14px;
              color: #ffffff;
            }
          }
        }
      }
    }
    .report-container {
      width: 34%;
      height: 370px;
    }
    .review-container {
      width: calc(66% - 20px);
      margin-left: 20px;
      height: 370px;
    }
  }
  .right-box {
    width: 275px;
    margin-left: 20px;
    .market-container {
      .market-box {
        height: 130px;
        border-radius: 10px;
        overflow: hidden;
        background-size: cover;
        margin-top: 12px;
        .market-main {
          background-color: rgba(0, 0, 0, 0.5);
          padding: 0 20px;
          height: 100%;
          box-sizing: border-box;
          justify-content: space-between;
          .time-info {
            width: 64%;
            .title {
              font-size: 20px;
              color: #ffffff;
              position: relative;
              padding-left: 10px;
              @extend .one-line;
              &::before {
                content: "";
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
                width: 4px;
                height: 16px;
                background: #ffffff;
              }
            }
            .date {
              font-size: 14px;
              color: #ffffff;
            }
          }
          .day-box {
            width: 66px;
            height: 60px;
            background: rgba(248, 250, 255, 0.9);
            border-radius: 10px;
            border: 1px solid #2859ff;
            justify-content: center;
            flex-shrink: 0;
            margin-left: 5px;
            .number {
              font-weight: 800;
              font-size: 30px;
              color: #2859ff;
            }
            .text {
              width: 12px;
              font-weight: 500;
              font-size: 12px;
              color: #2859ff;
              line-height: 14px;
              margin-left: 2px;
            }
          }
        }
      }
    }
  }
}
</style>
