import http from '@/utils/request'

// 平台类型
export const accountTypeApi = (params: any) => {
  return http({
    url: '/NewMedia/mediaType',
    method: 'get',
    params
  })
}

// 账号列表
export const accountListApi = (params: any) => {
  return http({
    url: '/NewMedia/accountList',
    method: 'get',
    params
  })
}

// 删除账号
export const deleteAccountApi = (data: any) => {
  return http({
    url: '/NewMedia/authDel',
    method: 'post',
    data
  })
}

// 添加账号
export const addAccountApi = (data: any) => {
  return http({
    url: '/NewMedia/mediaDeploy',
    method: 'post',
    data
  })
}

// 获取账号授权信息
export const getAuthInfoApi = (params: any) => {
  return http({
    url: '/NewMedia/dyUserinfo',
    method: 'get',
    params
  })
}

// 获取账号作品列表
export const getWorkListApi = (params: any) => {
  return http({
    url: '/NewMedia/itemList',
    method: 'get',
    params
  })
}

// 删除作品
export const deleteWorkApi = (data: any) => {
  return http({
    url: '/NewMedia/itemDel',
    method: 'post',
    data
  })
}

// 获取评论列表
export const getCommentListApi = (data: any) => {
  return http({
    url: '/MediaInfo/commentList',
    method: 'post',
    data
  })
}

// 获取粉丝列表
export const getFansListApi = (data: any) => {
  return http({
    url: '/MediaInfo/fansList',
    method: 'post',
    data
  })
}

// 获取关注列表
export const getFollowListApi = (data: any) => {
  return http({
    url: '/MediaInfo/followingList',
    method: 'post',
    data
  })
}

// 获取账号列表
export const accountListsApi = (params: any) => {
  return http({
    url: '/NewMedia/accountLists',
    method: 'get',
    params
  })
}

// 文章列表 获取账号
export const getAccountListApi = (params: any) => {
  return http({
    url: '/MediaConfig/selConfig',
    method: 'get',
    params
  })
}

// 文章 同步至百家号
export const syncToBaiJiaApi = (data: any) => {
  return http({
    url: '/BaijiahaoContent/copy',
    method: 'post',
    data
  })
}