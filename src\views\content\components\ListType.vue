<template>
  <div class="type-wrap h100">
    <data-list ref="dataListRef" :tableConfig="tableConfig" @clickBtn="dataAction" v-model:multipleSelection="multipleSelection"
      v-model:maxNumber="tableConfig.maxNumber">
      <template #title="{ row }">
        <span v-html="row.title"></span>
      </template>
      <template #action="{ row }">
        <div class="table-action flex">
          <div class="item" @click="edit(row)">
            <i class="iconfont icon-bianji"></i> <span>编辑</span>
          </div>
          <div class="item" @click="del(row)">
            <i class="iconfont icon-shanchu1"></i>
            <span>删除</span>
          </div>
        </div>
      </template>
    </data-list>
    <!-- 添加修改弹窗 -->
    <edit-type ref="editTypeRef" :cateType="cateType" :apiType="cateApiUrl(props.cateType).type" :maxOrder="tableConfig.maxNumber" @success="refreshList"></edit-type>
  </div>
</template>

<script setup lang="ts">
import { ref, defineAsyncComponent, watch } from 'vue'
import { contentTypeApi, contentDeleteTypeApi } from '@/api/content'
import { cateApiUrl } from '@/utils/contentType';
import { ElMessage, ElMessageBox } from 'element-plus';

// 定义组件
const DataList = defineAsyncComponent(() => import('@/components/Global/DataList.vue'))
const EditType = defineAsyncComponent(() => import('./components/EditType.vue'))
const editTypeRef = ref()

const props = defineProps({
  cateType: {
    type: String,
    default: ''
  },
  cateName: {
    type: String,
    default: ''
  }
})

const typeApi = cateApiUrl(props.cateType).type

const tableConfig = ref({
  api: contentTypeApi,
  apiType: cateApiUrl(props.cateType).type,
  allResult: true,
  showIndex: false,
  columns: [
    { label: '排序', prop: 'ordering', width: 80 },
    { label: '名称', prop: 'title', type: 'slot' },
    { label: '添加时间', prop: 'created_time', width: 200 },
    { label: '操作', prop: 'action', type: 'slot', align: 'center', width: 200 }
  ],
  limit: 100,
  showPage: false,
  maxNumber: 1
})

watch(() => props.cateType, (val) => {
  switch (val) {
    case 'content':
      tableConfig.value.columns.splice(2, 0, { label: '文章数', prop: 'newsnum_count', width: 120 })
      break;
    case 'goods':
      tableConfig.value.columns.splice(2, 0, { label: '产品数', prop: 'productsnum_count', width: 120 })
      break;
  }

}, { immediate: true })


const multipleSelection = ref([])

const emit = defineEmits(['actionClick'])
const dataAction = ({ key, type }: any) => {
  switch (key) {
    case 'add':
      editTypeRef.value.openDialog(null)
      break;
    case 'delete':
      if (!multipleSelection.value.length) return ElMessage.error('请选择要删除的数据')
      const ids = multipleSelection.value.map((item: any) => item.id).join(',')
      deleteAction(ids)
      break;
  }
}

const edit = (row: any) => {
  editTypeRef.value.openDialog(row)
}

const del = (row: any) => {
  deleteAction(row.id)
}

// 刷新列表
const dataListRef = ref()
const refreshList = () => {
  dataListRef.value.refresh()
}

const deleteLoading = ref(false)
const deleteAction = async (ids: string) => {
  if (deleteLoading.value) return ElMessage.error('请勿重复操作!')
  await ElMessageBox.confirm('确定要删除吗?', '提示', { type: 'warning' })
  try {
    deleteLoading.value = true
    const result = await contentDeleteTypeApi(typeApi, {id: ids})
    ElMessage.success('删除成功')
    refreshList()
  } catch (error) {
    console.log(error)
  } finally {
    deleteLoading.value = false
  }
}

</script>

<style scoped lang="scss">

</style>