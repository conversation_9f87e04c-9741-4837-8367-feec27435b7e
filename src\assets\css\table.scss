@import './common.scss';
.table-box {
  margin-top: 10px;
  margin-bottom: 10px;
}
.el-table__inner-wrapper::before {
  bottom: auto;
  top: 0;
}
// 表格样式
.el-table {
  background-color: transparent;
  &::before {
    height: 0;
  }
  thead {
    color: #7F8294;
    font-size: 14px;
    border-top: 1px solid #D5D9E7;
    tr {
      height: 36px;
    }
  }
  tbody {
    .hover-row {
      background-color: #F6F8FB;
    }
  }
}
.el-table td.el-table__cell,
.el-table th.el-table__cell.is-leaf {
  border-color: #E3E5EC;
}
// 表格操作按钮
.table-action {
  width: 100%;
  justify-content: center;
  flex-wrap: wrap;
  .item {
    margin-right: 20px;
    color: #636EA7;
    transition: all ease-in-out 0.3s;
    line-height: 28px;
    &:hover {
      color: $mainColor;
      cursor: pointer;
    }
    &:last-child {
      margin-right: 0;
    }
  }
}
:root {
  --el-input-border-color: #E3E5EC !important;
  --el-input-height: 34px !important;
}