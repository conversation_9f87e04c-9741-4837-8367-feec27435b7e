<template>
  <edit-dialog ref="editDialogRef" v-model:dialogShow="dialogShow" v-bind="formConfig" @sure="limitReplace">
    <div v-html="msgInfo"></div>
    <p style="margin-top: 10px;color: red;font-size: 13px;">注：继续保存会将违规词替换为*号</p>
  </edit-dialog>
</template>


<script setup lang="ts">
import { ref } from 'vue'
import { ElLoading } from 'element-plus'
import { useDialog } from '@/hooks/useDialog';
import { checkLimitWordsApi, replaceLimitWordsApi } from '@/api/content';

const { EditDialog, editDialogRef, dialogShow, openDialog } = useDialog()

const props = defineProps({
  cateType: {
    type: String,
    default: ""
  }
});

const emit = defineEmits(['continue', 'limit', 'loading'])

const formConfig = ref({
  title: '温馨提示',
  width: '400px',
  customForm: true,
  sureText: '继续保存',
  cancelText: '返回修改',
})

const msgInfo = ref('')
const formData:any = ref(null)

// 极限词检测
const checkLimit = async (form:any) => {
  formData.value = form
  const loading = ElLoading.service({
    lock: true,
    text: '检测中...',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  try {
    const res:any = await checkLimitWordsApi({
      content_type: props.cateType,
      ...form
    })
    msgInfo.value = res
    dialogShow.value = true
    emit('limit')
  } catch (error) {
    console.log(error)
    emit('continue', formData.value)
  } finally {
    loading.close()
  }
}

// 极限词替换
const limitReplace = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: '替换中...',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  try {
    const res:any = await replaceLimitWordsApi({
      content_type: props.cateType,
      ...formData.value
    })
    dialogShow.value = false
    emit('continue', res)
  } catch (error) {
    console.log(error)
  } finally {
    loading.close()
  }
}

defineExpose({ checkLimit })
</script>

<style scoped lang="scss">

</style>
