import { ref, watch, defineAsyncComponent } from "vue";
import { useRouter } from "vue-router";
import { getUpdateLog } from '@/api/logs'
import { lastTime, latelyUse, commonUse, siteReport, siteUsage, siteCount, siteQRCode } from "@/api/site";

export default function useDashboard() {

  const router = useRouter();
  
  // 网站使用报告 组件
  const WebsiteUsageReport = defineAsyncComponent(() => import("@/views/dashboard/components/WebsiteUsageReport.vue"));
  // 网站使用报告
  let statisticsList:any = ref([
    { name: "更新文章：", num: "--", unit: "篇", key: 'content_count' },
    { name: "浏览量：", num: "--", unit: "次", key: 'uv_count' },
    { name: "素材上传：", num: "--", unit: "M", key: 'bucket_size' },
    { name: "上传产品：", num: "--", unit: "个", key: 'goods_count' },
    { name: '访客数：', num: '--', unit: '人', key: 'look_count' },
    { name: '媒体发布：', num: '--', unit: '个', key:'media_count' }
  ]);
  // 获取网站使用报告
  const reportDate:any = ref("day");
  const getSiteReport = () => {
    siteReport({ type: reportDate.value }).then((data: any) => {
      statisticsList.value.forEach((v: any) => {
        v.num = data[v.key];
      });
    });
  };
  watch(() => reportDate.value,
    () => getSiteReport()
  );

  // 各端二维码 组件
  const QRCodeView = defineAsyncComponent(() => import("@/views/dashboard/components/QRCodeView.vue"));
  let mutList = ref([
    {name: "手机网站", qrCode: "", icon_small: "mobile03", icon_large: "mobile02", icon_large_w: "mobile01", key: 'dataUri'},
    {name: "微信小程序", qrCode: "", icon_small: "wx03", icon_large: "wx02", icon_large_w: "wx01", tab: "1", key: 'qrcode'},
    {name: "支付宝小程序", qrCode: "", icon_small: "zhi03", icon_large: "zhi02", icon_large_w: "zhi01", tab: "3", key: 'ali_qrcode'},
    {name: "百度小程序", qrCode: "", icon_small: "bai03", icon_large: "bai02", icon_large_w: "bai01", tab: "2", key: 'baidu_qrcode'},
    {name: "快手小程序", qrCode: "", icon_small: "ks03", icon_large: "ks02", icon_large_w: "ks01", tab: "4", key: 'kuaishou_qrcode'},
    {name: "抖音/头条小程序", qrCode: "", icon_small: "zi03", icon_large: "zi02", icon_large_w: "zi01", tab: "5", key: 'toutiao_qrcode'}
  ]);
  // 获取各端二维码
  const pcDomain = ref("");
  const getSiteQRCode = () => {
    siteQRCode().then((data: any) => {
      pcDomain.value = data.domain;
      mutList.value.forEach((v: any) => {
        v.qrCode = data[v.key]
      })
    })
  }

  // 获取更新日志
  const updateLog = ref<any>([]);
  const getUpdate = async () => {
    const result = await getUpdateLog({ limit: 1000, page: 1, content_type: 1 });
    updateLog.value = result;
    if (updateLog.value.length > 3) {
      updateLog.value.forEach((v: any) => {
        // 用replace方法及正则表达式实现
        v.content = v.content.replace(/<.*?>/gi, "");
      });
    }
  };

  // 媒体管理
  const MediaView = defineAsyncComponent(() => import("@/views/dashboard/components/MediaView.vue"));
  const mediaList = ref([
    { url: "/media/account", target: "_self", name: "微信公众号", url_web: "https://mp.weixin.qq.com/" },
    { url: "/media/account", target: "_self", name: "百家号", url_web: "https://baijiahao.baidu.com/" },
    { url: "/media/account", target: "_self", name: "抖音", url_web: "https://www.douyin.com/" },
    { url: "/media/account", target: "_self", name: "快手", url_web: "https://www.kuaishou.com/" },
    { url: "/media/account", target: "_self", name: "微博 ", url_web: "https://weibo.com/" },
    { url: "/media/account", target: "_self", name: "bilibili", url_web: "https://www.bilibili.com/" },
    // { url: "/media/account", target: "_self", name: "西瓜", url_web: "https://www.ixigua.com/" },
    // { url: "/media/account", target: "_self", name: "头条", url_web: "https://www.toutiao.com/" }
  ]);
  // 媒体管理点击
  const mediaClick = (key: number, item: any) => {
    // type（2账号管理  3官网地址）
    // that.requestData('SiteMenuLog/addmediamenu', {
    //   type
    // }, function (res) { });
    if (key == 2) {
      router.push(item.url)
    } else if (key == 3) {
      window.open(item.url_web, '_blank');
    }
  }

  // 上次登录时间
  const dateInfo = ref<any>({
    last_login: "----年--月--日",
    url: "http://www.baidu.com"
  });
  // 获取最后登录时间
  const getLastTime = () => {
    lastTime().then((data: any) => {
      dateInfo.value = data;
    });
  };

  return {
    WebsiteUsageReport, statisticsList, reportDate, getSiteReport,
    QRCodeView, mutList, pcDomain, getSiteQRCode,
    getUpdate, updateLog,
    MediaView, mediaList, mediaClick,
    dateInfo, getLastTime,
  }

}
