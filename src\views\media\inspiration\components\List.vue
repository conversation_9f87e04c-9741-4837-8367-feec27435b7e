<template>
  <div class="ins-list-app flex-x h100 pb10"  v-loading="loading">
    <!-- 左侧列表 :style="`height: ${insAppHeight};`" -->
    <div class="list-left flex-1 h100">
      <template v-for="(item, index) in list">
        <div class="list-container">
          <div class="top-box flex">
            <div class="list-title">{{ item.title }}</div>
            <!-- 自定义 -->
            <div
              v-if="index == 0"
              :class="['edit-box', 'flex', { show: isEdit }]"
              @click="isEdit = !isEdit"
            >
              <img v-if="isEdit" src="@/assets/images/media/icon4.png" alt="" />
              <img v-else src="@/assets/images/media/icon04.png" alt="" />
              <span>{{ isEdit ? "保存" : "自定义" }}</span>
            </div>
          </div>
          <div class="list-content flex-wrap">
            <div class="list-item flex" v-for="(v, i) in item.list" :key="i">
              <a class="flex" @click="jump(v.link)">
                <div class="icon-box">
                  <img v-if="v.icon" :src="v.icon" alt="v.title" />
                  <!-- 显示第一个字符的背景 -->
                  <div v-else class="icon-box-bg">
                    {{ v.title.substring(0, 1) }}
                  </div>
                </div>
                <div class="info">
                  <div :class="['name ellipsis', { 'edit-width': isEdit }]">
                    {{ v.title }}
                  </div>
                  <div class="desc">{{ v.description }}</div>
                </div>
              </a>
              <!-- 添加收藏 -->
              <div
                v-if="index != 0"
                :class="['add-icon', { show: isEdit }]"
                @click="collect(v.id)"
              >
                <img src="@/assets/images/media/icon05.png" alt="" />
              </div>
              <!-- 收藏 编辑、删除 -->
              <div
                v-if="index == 0 && isEdit"
                :class="['btn-box', 'flex', { show: isEdit }]"
              >
                <div class="btn-item" @click="toDel(v.id)">
                  <img src="@/assets/images/media/icon07.png" alt="" />
                </div>
                <div class="btn-item" @click="toEdit(v)">
                  <img src="@/assets/images/media/icon06.png" alt="" />
                </div>
              </div>
            </div>
            <div v-if="index == 0" class="list-item self-item" @click="toAdd">
              <div class="icon-box">
                <img src="@/assets/images/media/icon03.png" alt="" />
              </div>
              <div class="tips">添加属于自己的创意灵感网站</div>
            </div>
          </div>
        </div>
      </template>
    </div>
    <!-- 右侧分类 -->
    <div class="list-right">
      <div class="cate-container">
        <div
          :class="['cate-item', 'flex', { active: index == activeIndex }]"
          v-for="(item, index) in list"
          :key="index"
          @click="toCate(index)"
        >
          <span class="icon-box">
            <img :src="index == activeIndex ? item.icon_a : item.icon" alt="" />
          </span>
          <span class="cate-name">{{ item.title }}</span>
        </div>
      </div>
    </div>
    <!-- 添加弹窗 -->
    <add-list-view ref="addListRef" @refresh="getList"></add-list-view>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick, defineAsyncComponent } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getListApi, collectApi, deleteApi } from '@/api/media/inspiration'

const AddListView = defineAsyncComponent(() => import('./AddList.vue'))

const addListRef = ref()

const loading = ref(false)
const list:any = ref([])
const activeIndex = ref(0)
const isEdit = ref(false)

// 跳转页面
const jump = (link:string) => {
  if(!link.includes('http://') && !link.includes('https://')){
    link = 'http://' + link;
  }
  window.open(link, '_blank')
}
// 获取灵感创意
const getList = async (changeCate = false) => {
  loading.value = true;
  try {
    const res:any = await getListApi()
    loading.value = false;
    list.value = res;
    if (changeCate) {
      let cateType:any = window.localStorage.getItem('media_type')
      var str = ''
      if (cateType == 'article') str = '创意文案'
      else if (cateType == 'video') str = '创意短片'
      var toIndex = res.findIndex((item:any) => item.title == str);
      nextTick(() => {
        if (toIndex > -1) toCate(toIndex)
        window.localStorage.removeItem('media_type')
      })
    }
  } catch (error) {
    loading.value = false;
    console.log(error)
    // ElMessage.error(error)
  }
}
// 收藏
const collect = async (id:string) => {
  try {
    const res:any = await collectApi(id)
    ElMessage.success('操作成功')
    getList()
  } catch (error) {
    console.log(error)
    // ElMessage.error(error)
  }
}
// 删除网站
const toDel = async (id:string) => {
  await ElMessageBox.confirm('确定要删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
  try {
    const res:any = await deleteApi(id)
    ElMessage.success('操作成功')
    getList()
  } catch (error) {
    console.log(error)
  }
}
// 编辑
const toEdit = (item:any) => {
  addListRef.value.openDialog({
    id: item.id,
    title: item.title,
    link: item.link,
    description: item.description,
    icon: item.icon
  })
}
// 定位到分类
const toCate = (index:number) => {
  activeIndex.value = index;
  // 滚动条
  let list = document.querySelectorAll('.list-container');
  list[index].scrollIntoView({ behavior: 'smooth', block: 'start' });
}
// 添加
const toAdd = () => {
  addListRef.value.openDialog({})
}

onMounted(() => {
  getList(true)
})

</script>

<style lang="scss" scoped>
.ins-list-app {
  .list-left {
    overflow-x: hidden;
    overflow-y: auto;
    margin-right: 15px;
    box-sizing: border-box;

    .list-container {
      background-color: #fff;
      margin-bottom: 16px;
      padding: 20px;

      .top-box {
        justify-content: space-between;
        margin-bottom: 15px;

        .list-title {
          font-size: 16px;
          color: #1c2b4b;
        }

        .edit-box {
          width: 76px;
          height: 29px;
          background: #ffffff;
          border-radius: 15px;
          border: 1px solid #2859ff;
          justify-content: center;
          cursor: pointer;
          transition: all ease-in-out 0.3s;

          span {
            font-size: 12px;
            color: #2859ff;
            margin-left: 5px;
          }

          &.show {
            background: #eaefff;
            border-color: #eaefff;
          }
        }
      }

      .list-content {
        .list-item {
          width: 210px;
          height: 90px;
          border-radius: 10px;
          cursor: pointer;
          margin-right: 20px;
          margin-bottom: 20px;
          padding: 15px;
          box-sizing: border-box;
          position: relative;
          transition: all ease-in-out 0.3s;
          overflow: hidden;
          align-items: flex-start;

          a {
            align-items: flex-start;
            width: 100%;
          }

          .icon-box {
            width: 30px;
            height: 30px;
            margin-right: 10px;
            flex-shrink: 0;

            img {
              display: block;
              width: 100%;
              height: 100%;
              object-fit: scale-down;
            }
          }

          .icon-box-bg {
            width: 30px;
            height: 30px;
            margin-right: 10px;
            background-color: #2859ff;
            color: #ffffff;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            font-size: 16px;
          }

          .info {
            flex: 1;
            overflow: hidden;

            .name {
              font-size: 14px;
              color: #111111;
              transition: all ease-in-out 0.3s;
              margin-bottom: 6px;
            }

            .desc {
              font-size: 12px;
              color: #666666;
              line-height: 18px;
              // 超出两行隐藏
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
            }
          }

          .add-icon {
            position: absolute;
            width: 16px;
            height: 16px;
            top: 6px;
            right: -20px;
            cursor: pointer;
            transition: all ease-in-out 0.3s;

            img {
              display: block;
              width: 100%;
              height: 100%;
              object-fit: scale-down;
            }

            &.show {
              right: 7px;
            }
          }

          .btn-box {
            position: absolute;
            top: 6px;
            right: 7px;

            .btn-item {
              width: 16px;
              height: 16px;
              cursor: pointer;
              margin-left: 6px;

              img {
                display: block;
                width: 100%;
                height: 100%;
                object-fit: scale-down;
              }
            }
          }

          &:hover {
            background: #f2f8fd;

            .info {
              .name {
                color: #2859ff;
                text-decoration-line: underline;
                width: calc(100% - 12px);
              }
            }

            .add-icon {
              right: 7px;
            }
          }

          &.self-item {
            background: #f0f2f6;
            border-radius: 6px;
            border: 1px solid #e5ecff;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            .tips {
              font-size: 12px;
              color: #999999;
              margin-top: 4px;
            }
          }
        }
      }

      .top-box {
        & + .list-content{
          .list-item:hover {
            .info{
              .name{
                width: auto;
                &.edit-width{
                  width: calc(100% - 38px);
                }
              }
            }
          }
        }
      }
    }
  }

  .list-right {
    width: 106px;
    // height: 100%;
    background-color: #fff;

    .cate-container {
      padding-top: 6px;
      padding-bottom: 6px;

      .cate-item {
        cursor: pointer;
        height: 36px;
        width: 100%;
        transition: all ease-in-out 0.3s;
        padding: 0 14px;
        box-sizing: border-box;

        .icon-box {
          width: 20px;
          height: 20px;
          margin-right: 5px;

          img {
            display: block;
            width: 100%;
            height: 100%;
            object-fit: scale-down;
          }
        }

        .cate-name {
          font-size: 12px;
          color: #999999;
          transition: all ease-in-out 0.3s;
        }

        &.active,
        &:hover {
          background: #f5f9ff;

          .cate-name {
            color: #2859ff;
          }
        }
      }
    }
  }
}

.ellipsis {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.edit-width {
  width: calc(100% - 38px);
}

</style>
