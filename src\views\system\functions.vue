<script setup lang="ts">
import {computed, defineAsyncComponent, ref} from "vue";

const navList = ref([
  { title: '更新日志', key: 'updateLog', component: defineAsyncComponent(() => import('@/views/system/components/UpdateLog.vue')) },
])

const activeIndex = ref(0)

const navItemClick = (item: any, index: number) => {
  activeIndex.value = index
}

const currentComponent = computed(() => {
  return navList.value[activeIndex.value].component
})
</script>

<template>
  <div class="h100 flex-y">
    <page-menu v-if="navList.length" :list="navList" @click-item="navItemClick" :activeIndex="activeIndex" />
    <div class="flex-1">
      <component ref="currentComponentRef" :is="currentComponent" />
    </div>
  </div>
</template>

<style scoped lang="scss">

</style>