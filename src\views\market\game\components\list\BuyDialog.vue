<script setup lang="ts">
import {useDialog} from "@/hooks/useDialog";
import MealList from "@/views/market/game/components/list/MealList.vue";
import {computed} from "vue";

const {EditDialog, editDialogRef, closeDialog} = useDialog();

interface Props {
  dialogShow: boolean;
}
const props = defineProps<Props>();
const emits = defineEmits(['success', 'update:dialogShow']);

const show = computed({
  get() {
    return props.dialogShow
  },
  set(val: any) {
    emits('update:dialogShow', val)
  }
})


const close = () => {
  emits('success');
  closeDialog();
}
</script>

<template>
  <!-- 购买弹窗 -->
  <edit-dialog ref="editDialogRef" v-model:dialog-show="show" title="小游戏运营购买" :custom-form="true" :show-bottom-btn="false" @close="closeDialog" @sure="closeDialog" width="750px">
    <meal-list @success="close" />
  </edit-dialog>
</template>

<style scoped lang="scss">

</style>
