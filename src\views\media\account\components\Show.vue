<template>
  <div v-if="nowInfoId" class="account-show h100 flex-y">
    <user-info-view :now-info-id="nowInfoId" :list-name="listName" @backList="backList" @delAuth="delAuth" />
    <item-list-view class="flex-1" :now-info-id="nowInfoId" :list-name="listName" />
  </div>
</template>

<script setup lang="ts">
import { ref, defineAsyncComponent } from 'vue'

const userInfoView = defineAsyncComponent(() => import('./UserInfo.vue'))
const itemListView = defineAsyncComponent(() => import('./ItemList.vue'))

const emit = defineEmits(['changeNav', 'delAuth'])

const nowInfo = ref({})
const nowInfoId = ref(0)
const listName = ref(0)
const setData = (data: any) => {
  console.log(data, 'setData')
  nowInfo.value = data
  nowInfoId.value = data.id
  listName.value = data.types
}

const backList = () => {
  emit('changeNav', { key: 'list' })
}

const delAuth = (id: number) => {
  emit('delAuth', id)
}

defineExpose({ setData })
</script>

<style scoped lang="scss">

@import '../styles/common.scss';

</style>