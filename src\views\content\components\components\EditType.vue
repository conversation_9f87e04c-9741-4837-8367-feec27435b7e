<template>
  <edit-dialog ref="editDialogRef" v-model:dialog-show="dialogShow" :title="dialogTitle" :width="dialogWidth" :customForm="true" @sure="handleSure">
    <el-form ref="editFormRef" :model="formData" :rules="rules" label-width="80px" label-position="right">
      <el-form-item label="排序">
        <div class="flex w100">
          <el-input :class="showParent ? 'width80' : 'flex-1'" v-model="formData.ordering" type="number"></el-input>
          <type-select v-if="showParent" class="flex-1 ml10" v-model:value="formData.parent_id" :apiType="apiType" :cateType="cateType" optionType="type-add"></type-select>
        </div>
      </el-form-item>
      <el-form-item label="分类名称" prop="title">
        <el-input v-model="formData.title" placeholder="请输入分类名称"></el-input>
      </el-form-item>
      <el-form-item v-if="showImage" label="图片" prop="image_intro">
        <upload-image class="w100" :uploadBtn="true" height="160px" v-model:imageIntro="formData.image_intro" />
      </el-form-item>
      <el-form-item v-if="showImage2" label="图2" prop="type_qhimg">
        <upload-image class="w100" :uploadBtn="true" height="160px" v-model:imageIntro="formData.type_qhimg" />
      </el-form-item>
    </el-form>
    <limit-word ref="limitWordRef" cate-type="category" @continue="limitContinue" />
  </edit-dialog>
</template>

<script setup lang="ts">
import { computed, ref, defineAsyncComponent, watch } from 'vue'
import { ElMessage } from 'element-plus';
import { useDialog } from '@/hooks/useDialog';
import { useLimitWord } from '@/hooks/useLimitWord'
import { contentAddTypeApi, contentEditTypeApi } from '@/api/content'

const { EditDialog, editDialogRef, dialogShow, dialogData, openDialog } = useDialog()
const { LimitWord, limitWordRef } = useLimitWord()

const TypeSelect = defineAsyncComponent(() => import('./TypeSelect.vue'))
const UploadImage = defineAsyncComponent(() => import('@/components/Uploads/uploadImg.vue'))

const props = defineProps({
  cateType: {
    type: String,
    default: 'content'
  },
  apiType: {
    type: String,
    default: ''
  },
  maxOrder: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['success'])

const defaultFormData = {ordering: props.maxOrder, title: '', parent_id: 1}

const dialogTitle = computed(() => {
  return dialogData.value && dialogData.value.id ? '编辑分类' : '添加分类'
})

const showParent = computed(() => {
  switch (props.cateType) {
    case 'content':
    case 'goods':
    case 'notice':
    case 'product':
    case 'activity':
    case 'recruitment':
      return true
    default:
      return false
  }
})

const showImage = computed(() => {
  switch (props.cateType) {
    case 'goods':
    case 'product':
      return true
    default:
      return false
  }
})

const showImage2 = computed(() => {
  switch (props.cateType) {
    case 'goods':
      return true
    default:
      return false
  }
})


const dialogWidth = computed(() => {
  switch (props.cateType) {
    case 'goods':
    case 'product':
      return '800px'
    default:
      return '500px'
  }
})

watch(() => props.maxOrder, (val) => {
  formData.value.ordering = val
})

watch(() => dialogShow.value, (val) => {
  if (!val) return
  let info = dialogData.value
  if (!info) {
    formData.value = JSON.parse(JSON.stringify(defaultFormData))
    formData.value.ordering = props.maxOrder
  } else {
    const { id, ordering, type_title, parent_id } = info
    formData.value = {
      id,
      ordering,
      title: type_title,
      parent_id
    }
    if (props.cateType == 'goods') {
      formData.value.image_intro = JSON.parse(info.images).image_intro
      formData.value.type_qhimg = info.type_qhimg
    }
    if (props.cateType == 'product') {
      formData.value.image_intro = info.images ? JSON.parse(info.images).image_intro : ''
    }
  }
})


const formData:any = ref(JSON.parse(JSON.stringify(defaultFormData)))

const rules = ref({
  title: [
    { required: true, message: '请输入分类名称', trigger: 'blur' }
  ]
})

const editFormRef = ref()
const submitLoading = ref(false)

const handleSure = async () => {
  if (submitLoading.value) return ElMessage.warning('请勿重复提交')
  await editFormRef.value.validate()
  await limitWordRef.value && await limitWordRef.value.checkLimit(formData.value)
}

const limitContinue = async (res: any) => {
  formData.value = res
  saveApi()
}

const saveApi = async () => {
  let api = contentAddTypeApi
  if (dialogData.value && dialogData.value.id) api = contentEditTypeApi
  try {
    submitLoading.value = true
    let params = formData.value
    if (dialogData.value && dialogData.value.id) params.id = dialogData.value.id
    const res = await api(props.apiType, params)
    ElMessage.success('操作成功')
    emit('success', res)
    dialogShow.value = false
  } catch (error) {
    console.log(error)
  } finally {
    submitLoading.value = false
  }
}

defineExpose({ openDialog })
</script>

<style scoped lang="scss">

</style>