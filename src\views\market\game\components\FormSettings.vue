<script setup lang="ts">
import DataList from "@/components/Global/DataList.vue";
import {ref, watch} from "vue";
import {ElMessage, ElMessageBox, FormInstance} from "element-plus";
import {useDialog} from "@/hooks/useDialog";
import AddItem from "@/views/market/game/components/formSettings/AddItem.vue";
import {deleteItemApi, getFormListApi, saveFormApi} from "@/api/game";
import {fieldType} from "./fieldType";

interface Props {
  data: any
}
const props = defineProps<Props>();

const emits = defineEmits(['changeNav']);

const {EditDialog, editDialogRef, dialogData, dialogShow, openDialog, closeDialog} = useDialog();

const dialogTitle = ref('添加表单项');
/**
 * 添加表单项
 */
const addFormItem = () => {
  console.log(props.data, 'data');
  dialogTitle.value = '添加表单项'
  openDialog(props.data)
};

/**
 * 删除接口
 * @param id
 */
const delApi = async (id: number | string) => {
  ElMessageBox.confirm('确认删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      await deleteItemApi({id});
      ElMessage.success('删除成功');
      dataListRef.value.refresh();
    } finally {
    }
  }).catch(() => {
  });
};
/**
 * 删除表单项
 */
const delFormItem = (row: any) => {
  if(row.length === 0) return ElMessage.warning('请先选择数据');
  const ids = row.map((item: any) => item.id).join(',');
  delApi(ids);
};

/**
 * 返回列表
 */
const goBack = () => {
  emits('changeNav', 0);
}

const actionList = ref([
  {
    name: '添加',
    icon: 'icon-tianjia',
    key: 'add',
    action: addFormItem
  },
  {
    name: '删除',
    icon: 'icon-shanchu1',
    key: 'delete',
    checkSelected: true,
    action: delFormItem
  },
  {
    name: '返回列表',
    icon: 'icon-fanhui1',
    key: 'back',
    action: goBack
  },
]);
/**
 * 按钮点击事件
 * @param button
 */
const dataAction = (button: any) => {
  if (button.checkSelected) {
    if (!tableConfig.value.multipleSelection.length) {
      ElMessage.warning('请先选择数据')
      return
    }
  }
  button.action(tableConfig.value.multipleSelection)
}

const tableConfig = ref({
  api: getFormListApi,
  multipleSelection: [],
  columns: [
    {
      label: "排序",
      prop: "ordering",
      sortable: true,
      width: 80
    },
    {
      label: '英文名称',
      prop: 'key'
    },
    {
      label: '中文名称',
      prop: 'title'
    },
    {
      label: '类型',
      prop: 'type',
      type: 'slot'
    },
    {
      label: '必填',
      prop: 'required',
      type:'slot'
    },
    {
      label: '添加时间',
      prop: 'create_time'
    },
    {
      label: '操作',
      prop: 'action',
      type: 'slot',
      align: 'center',
    },
  ],
  params: {category: 'xyx', id: props?.data?.id},
  showIndex: false,
});
const setFormRef = ref()

const form = ref({
  id: props?.data?.id,
  open_form: props?.data?.open_form,
  button_text: props?.data?.button_text,
})
const saveLoading = ref(false);
/**
 * 保存
 * @param formEl 表单实例
 */
const save = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.validate(async (valid: any) => {
    if (valid) {
      saveLoading.value = true;
      try{
        await saveFormApi(form.value)
        ElMessage.success('操作成功');
        dataListRef.value.refresh();
      }finally {
        saveLoading.value = false;
      }
    }
  })
}
const dataListRef = ref();
/**
 * 添加表单项成功回调
 */
const getAddForm = () => {
  dataListRef.value.refresh();
  closeDialog();
}

/**
 * 编辑
 * @param row
 */
const edit = (row: any) => {
  dialogTitle.value = '编辑表单项'
  dialogData.value = row
  const data = JSON.parse(JSON.stringify(row));
  data.id = props?.data?.id;
  data.field_id = row.id;
  openDialog(data)
}
/**
 * 删除
 * @param id
 */
const del = (id: number | string) => {
  if(!id) return
  delApi(id);
}
</script>

<template>
  <div class="wrap h100">
    <data-list ref="dataListRef" :tableConfig="tableConfig" :action-default="false" @clickBtn="dataAction"
               :actionList="actionList" v-model:multipleSelection="tableConfig.multipleSelection">
      <template #top-right>
        <el-form ref="setFormRef" :model="form" inline>
          <el-form-item label="表单状态：" prop="open_form">
            <el-radio-group v-model="form.open_form">
              <el-radio :value="1">开启</el-radio>
              <el-radio :value="0">关闭</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="表单标题：" prop="button_text">
            <el-input v-model="form.button_text" placeholder="请输入表单标题"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" class="btn" @click="save(setFormRef)" :loading="saveLoading">确定</el-button>
          </el-form-item>
        </el-form>
      </template>
      <template #type="{row}">
        <span>{{row.type && fieldType[row.type] ? fieldType[row.type] : '未知'}}</span>
      </template>
      <template #required="{row}">
        <span>{{+row.required === 1 ? '是' : '否'}}</span>
      </template>
      <template #action="{row}">
        <div class="table-action flex">
          <div class="item" @click="edit(row)">
            <i class="iconfont icon-bianji"></i> <span>编辑</span>
          </div>
          <div class="item" @click="del(row.id)">
            <i class="iconfont icon-shanchu1"></i> <span>删除</span>
          </div>
        </div>
      </template>
    </data-list>

    <!-- 添加表单项 -->
    <edit-dialog ref="editDialogRef" :dialog-show="dialogShow" :title="dialogTitle" :custom-form="true"
                 :show-bottom-btn="false"
                 @close="closeDialog" @sure="closeDialog" width="600px">
      <add-item :data="dialogData" @success="getAddForm" @close="closeDialog"/>
    </edit-dialog>
  </div>
</template>

<style scoped lang="scss">

</style>
