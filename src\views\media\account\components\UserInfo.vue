<template>
  <div class="top-user" v-if="authInfo">
    <div class="flex-between">
      <div class="left-info flex">
        <div class="info-head">
          <img v-if="authInfo.avatar" :src="authInfo.avatar" alt="头像">
        </div>
        <div class="info-box" v-if="listName == 1 || listName == 2 || listName == 5">
          <div class="flex">
            <div class="i-title">{{ authInfo.nickname }}</div>
            <div class="btn-box flex">
              <jzt-button class="gray" size="small" name="取消授权" icon="icon-quxiaoshouquan" @click="delAuth" />
            </div>
          </div>
          <div class="fan-box flex">
            <div class="gray" style="margin-right: 24px;" v-if="authInfo.province || authInfo.city">
              <i class="iconfont icon-dingwei"></i>
              {{ authInfo.province }} {{ authInfo.city }}
            </div>
            <div class="gray" v-if="authInfo.content">
              简介：{{ authInfo.content }}
            </div>
          </div>
          <div v-if="'fans' in authInfo || 'following' in authInfo" class="fan-box flex">
            <div class="gray" style="margin-right: 24px;cursor: pointer;" @click="toUserList('fan')">
              <i class="iconfont icon-wode-wodefensi"></i>
              <span>粉丝数：</span><span style="color: #2859FF;">{{ authInfo.fans }}</span>
            </div>
            <div class="gray" style="cursor: pointer;" @click="toUserList('fol')">
              <i class="iconfont icon-wodeguanzhu"></i>
              <span>关注数：</span><span style="color: #2859FF;">{{ authInfo.following }}</span>
            </div>
            <div v-if="'sex' in authInfo" class="gray">
              <i class="iconfont icon-ico-sex"></i>
              <span>性别：</span><span style="color: #2859FF;">{{ authInfo.sex }}</span>
            </div>
          </div>
        </div>
        <div class="info-box" v-else>
          <div class="i-title" style="margin-bottom: 10px;">{{ authInfo.nickname }}</div>
          <div class="btn-box flex">
            <jzt-button class="gray" size="small" name="取消授权" icon="icon-quxiaoshouquan" @click="delAuth" />
            <jzt-button class="ml5" size="small" name="返回账号列表" @click="backList" />
          </div>
        </div>
      </div>
      <div class="right-info flex">
        <template v-for="(item, index) in authUserInfo[listName]">
          <div class="item flex">
            <div class="number">{{ item.value }}</div>
            <div class="name">{{ item.name }}</div>
          </div>
        </template>
      </div>
    </div>
    <fans-view ref="fansViewRef" :now-info-id="nowInfoId" :list-name="listName" />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, defineAsyncComponent } from 'vue'
import { getAuthInfoApi } from '@/api/media/account'

const FansView = defineAsyncComponent(() => import('./FansView.vue'))
const fansViewRef = ref()

const props = defineProps({
  nowInfoId: {
    type: Number,
    default: 0
  },
  listName: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['backList', 'delAuth'])

/**
 * 授权账号信息
 * 平台类型  1抖音 2快手 3西瓜 4头条 5哔哩 6百家号 7公众号 8微博
 */
const authUserInfo: any = ref({
  1: [
    { name: '作品数', value: '--', key: 'videonum' },
    { name: '点击量', value: '--', key: 'readnum' },
    { name: '评论数', value: '--', key: 'commentnum' },
    { name: '点赞数', value: '--', key: 'likenum' }
  ],
  2: [
    { name: '作品数', value: '--', key: 'videonum' },
    { name: '播放数', value: '--', key: 'readnum' },
    { name: '评论数', value: '--', key: 'commentnum' },
    { name: '点赞数', value: '--', key: 'likenum' }
  ],
  3: [
    { name: '作品数', value: '--', key: 'videonum' },
    { name: '点击量', value: '--', key: 'readnum' },
    { name: '评论数', value: '--', key: 'commentnum' },
    { name: '点赞数', value: '--', key: 'likenum' }
  ],
  4: [
    { name: '作品数', value: '--', key: 'videonum' },
    { name: '点击量', value: '--', key: 'readnum' },
    { name: '评论数', value: '--', key: 'commentnum' },
    { name: '点赞数', value: '--', key: 'likenum' }
  ],
  5: [
    { name: '作品数', value: '--', key: 'videonum' },
    { name: '播放数', value: '--', key: 'readnum' },
    { name: '评论数', value: '--', key: 'commentnum' },
    { name: '点赞数', value: '--', key: 'likenum' }
  ],
  6: [
    { name: '作品数', value: '--', key: 'videonum' },
    { name: '点击量', value: '--', key: 'readnum' },
    { name: '评论数', value: '--', key: 'commentnum' },
    { name: '点赞数', value: '--', key: 'likenum' }
  ],
  7: [
    { name: '作品数', value: '--', key: 'videonum' },
    { name: '点击量', value: '--', key: 'readnum' },
    { name: '评论数', value: '--', key: 'commentnum' },
    { name: '点赞数', value: '--', key: 'likenum' }
  ]
})

// 获取账号授权信息
const userLoading = ref(false)
const authInfo: any = ref(null)
const getAuthInfo = () => {
  userLoading.value = true
  getAuthInfoApi({ id: props.nowInfoId }).then((res: any) => {
    authInfo.value = res
    authUserInfo.value[props.listName].forEach((item: any) => {
      item.value = res[item.key]
    })
  }).finally(() => {
    userLoading.value = false
  })
}

// 取消授权
const delAuth = () => {
  console.log(props.nowInfoId)
  emit('delAuth', props.nowInfoId)
}

// 返回账号列表
const backList = () => {
  emit('backList')
}

// 获取粉丝/关注列表
const toUserList = (type: string) => {
  // emit('toUserList', type)
  fansViewRef.value.openDialog(type)
}

watch(() => props.nowInfoId, () => {
  if (props.nowInfoId) {
    authInfo.value = null
    getAuthInfo()
  }
}, { immediate: true })

</script>

<style scoped lang="scss">
/*用户授权信息*/
.top-user {
  background: #FFFFFF;
  box-shadow: 0px 6px 12px 0px rgba(216, 224, 234, 0.2);
  border-radius: 10px;
  justify-content: space-between;
  padding: 10px 0 10px 10px;
  margin: 10px 10px 0 10px;
  // width: calc(100% - 20px);
  box-sizing: border-box;
  .left-info {
    .info-head {
      width: 50px;
      height: 50px;
      border-radius: 10px;
      overflow: hidden;
      margin-right: 20px;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    .info-box {
      .i-title {
        font-size: 16px;
        font-weight: 600;
        color: #1C2B4B;
        margin-right: 14px;
      }
      .fan-box {
        color: #7F8294;
        margin-top: 5px;
      }
    }
  }
  .right-info {
    .item {
      position: relative;
      width: 160px;
      flex-direction: column;
      justify-content: center;
      &:after {
        content: '';
        width: 1px;
        height: 60%;
        background-color: #E3E5EC;
        position: absolute;
        right: 0;
        top: 0;
        bottom: 0;
        margin: auto;
      }
      &:last-child:after {
        width: 0px;
      }
      .number {
        font-size: 24px;
        font-weight: 800;
        color: #1C2B4B;
        // margin-bottom: 16px;
      }
      .name {
        font-size: 14px;
        font-weight: 400;
        color: #7F8294;
      }
    }
  }
}
@import '../styles/common.scss';
</style>