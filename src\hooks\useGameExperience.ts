import {computed, ref} from "vue";
import {getGameStatusApi} from "@/api/game";
import store from "@/store";

export const useGameExperience = () => {
  /*是否是营销版*/
  const isAgent = computed(() => store.getters.isAgent)

  const experienceInfo: any = ref({})
  const experienceLoading = ref(false)
  const getExperienceInfo = async () => {
    experienceLoading.value = true;
    try{
      experienceInfo.value = await getGameStatusApi({isagent: +isAgent.value}) as any;
    }catch(e){}finally {
      experienceLoading.value = false;
    }
  }
  return {
    experienceInfo,
    getExperienceInfo,
    experienceLoading
  }
}
