<script setup lang="ts">
import TopTips from "@/components/TopTips.vue";
import {defineAsyncComponent, ref} from "vue";
import {saveSiteFaviconApi} from "@/api/siteManage";
import {ElMessage} from "element-plus";
import useSiteManageInfo from "@/hooks/useSiteManageInfo";

const UploadImage = defineAsyncComponent(() => import('@/components/Uploads/uploadImg.vue'))

const formData = ref({
  code: 200,
  url: '',
  size: 0,
  id: '',
  title: ''
})
const {loading, getFavicon} = useSiteManageInfo();
getFavicon(true, (res: any) => {
  formData.value.url = res.favicon
  // 截取res.favicon的最后一个/后面的内容
  formData.value.title = res.favicon.substring(res.favicon.lastIndexOf('/') + 1)
  formData.value.id = res.id
});

// 保存favicon TODO: 检测空间
const saveFavicon = async () => {
  if(!formData.value.url) {
    ElMessage.error('请上传图片')
    return
  }
  try{
    loading.value = true
    await saveSiteFaviconApi(formData.value)
    ElMessage.success('保存成功')
    await getFavicon(false)
  }catch (e){
    console.log(e);
  }finally {
    loading.value = false
  }
}

// 图片大小和名称
const setImgInfo = (file: File) => {
  formData.value.size = file.size
  formData.value.title = file.name
}
</script>

<template>
  <div class="h100 wrap">
    <div class="box">
      <top-tips
          text="网站icon图标是通过搜索引擎访问网站的时候，在浏览器上展示的一个小图标，一般都是用logo的缩小比例。（需要重新发布站点之后生效）"/>
      <div class="icon-wrap flex" v-loading="loading">
        <div class="upload-wrap">
          <upload-image :uploadBtn="true" height="130px" v-model:imageIntro="formData.url" @setImgInfo="setImgInfo"/>
          <div class="tips">推荐icon图标 <span>16×16</span> 像素</div>
        </div>
        <img src="https://jzt_dev_1.china9.cn/new_client/images/system/icon02.png" alt="">
      </div>
      <div class="btn-wrap">
        <el-button type="primary" class="save-btn" @click="saveFavicon">保存</el-button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.wrap {
  padding: 10px 0;
  .box {
    padding: 20px 30px;
    background: #fff;
    .icon-wrap {
      .upload-wrap{
        margin-right: 50px;
        .tips{
          font-size: 12px;
          font-weight: 500;
          color: #696C7D;
          text-align: center;
          margin-top: 14px;
          span{
            color: #2859FF;
          }
        }
      }
    }
    .btn-wrap{
      margin-top: 20px;
      text-align: right;
      .save-btn{
        width: 100px;
        height: 34px;
      }
    }
  }
}
</style>
