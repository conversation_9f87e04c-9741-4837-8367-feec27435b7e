<template>
  <div v-loading="postLoading" class="library-wrap h100">
    <div class="library-top flex">
      <div class="top-left flex">
        <div class="flex mr10">
          <label class="label">从素材库：</label>
          <el-select v-model="libraryCateId" placeholder="请选择" filterable suffix-icon="CaretTop"
            class="width200">
            <el-option value="" label="全部素材分类"></el-option>
            <el-option v-for="(item, index) in libraryCate" :key="index" :value="item.id" :label="item.type_title">
              <span v-html="item.title"></span>
            </el-option>
          </el-select>
        </div>
        <div class="flex mr30">
          <el-input v-model="searchName" placeholder="请输入素材名称搜索" @keydown.enter="searchByName" @blur="searchByName">
            <template #suffix>
              <i class="iconfont icon-sousuo" style="cursor: pointer" @click="searchByName"></i>
            </template>
          </el-input>
        </div>
        <div class="flex">
          <label class="label">提交到：</label>
          <el-select v-model="departmentId" placeholder="请选择部门" filterable suffix-icon="CaretTop" class="width200 ml10">
            <div v-loading="companyLoading">
              <el-option v-for="(item, index) in departmentList" :key="index" :value="item.id" :label="item.title"></el-option>
            </div>
          </el-select>
        </div>
      </div>
      <div class="top-right flex">
        <jzt-button name="取消" size="large" @click="cancel" style="margin-right: 10px;"></jzt-button>
        <jzt-button name="提交" size="large" class="active" @click="addGoods"></jzt-button>
      </div>
    </div>
    <div class="content-box">
      <library :isShowCate="false" :isRadio="false" :cate-id="libraryCateId" :showPage="true" :limit="24" :max="-1" :file-type="-2"
        v-model:library-img="libraryImg" ref="libraryRef" @cateListChange="libraryCateListChange" :search-name="searchName">
      </library>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, defineAsyncComponent, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { getCompanyListApi, getDepartmentListApi, shareSubmitApi } from '@/api/bucket'

const Library = defineAsyncComponent(() => import('@/components/Uploads/Library.vue'))
const libraryRef = ref()

const emit = defineEmits(['changeNav', 'success'])

const libraryCateId = ref('')
const libraryImg = ref([])
const libraryCate:any = ref([])
const searchName = ref('')
const companyId = ref('')
const departmentId = ref('')

const libraryCateListChange = (val: any) => {
  libraryCate.value = val
}

const searchByName = () => {
  libraryRef.value.getLibraryList(1);
}

// 添加
const postLoading = ref(false)
const addGoods = () => {
  console.log(libraryImg.value, 'libraryImg.value')
  if (!libraryImg.value.length) {
    ElMessage.error('请选择素材')
    return
  }
  postLoading.value = true
  // parent_id 公司id child_id 部门id  data 素材数据
  shareSubmitApi({
    parent_id: companyId.value,
    child_id: departmentId.value,
    data: libraryImg.value.map((item: any) => ({ id: item.id, type: item.type }))
  }).then((res: any) => {
    ElMessage.success('共享成功')
    emit('success')
    emit('changeNav', {key: 'bucket'})
  }).finally(() => {
    postLoading.value = false
  })
}

// 取消
const cancel = () => {
  emit('changeNav', {key: 'bucket'})
}

// 获取公司列表
const companyList = ref<{ id: string, title: string }[]>([])
const companyLoading = ref(false)
const getCompanyList = () => {
  companyLoading.value = true
  getCompanyListApi().then((res: any) => {
    if (res.length) {
      companyList.value = res
      companyId.value = res[0].id
      departmentList.value = res.filter((item: any) => item.pid != 0)
    }
  }).finally(() => {
    companyLoading.value = false
  })
}

// 获取部门列表
const departmentList = ref<{ id: string, title: string }[]>([])
// const departmentLoading = ref(false)
// const getDepartmentList = (id: string) => {
//   departmentLoading.value = true
//   getDepartmentListApi(id).then((res: any) => {
//     departmentList.value = res
//   }).finally(() => {
//     departmentLoading.value = false
//   })
// }

// watch(companyId, (newVal: string) => {
//   if (newVal) {
//     getDepartmentList(newVal)
//   }
// })

onMounted(() => {
  getCompanyList()
})
</script>

<style lang="scss" scoped>
.library-top {
  justify-content: space-between;
  padding: 10px 22px;
  font-size: 14px;
  color: #555;
}
.content-box {
  background: rgba(255, 255, 255, 0.99);
  box-shadow: 0px 6px 12px 0px rgba(216, 224, 234, 0.2);
  padding: 0;
  width: 100%;
  box-sizing: border-box;
  :deep(.library-list-box) {
    height: auto;
  }
}
</style>