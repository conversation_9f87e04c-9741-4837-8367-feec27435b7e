* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
body {
  background-color: #F6F8FB;
  font-size: 14px;
  color: #333333;
  font-family: 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}
a { text-decoration: none; }
/* 关于flex */
.flex {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.flex-x {
  display: flex;
}
.flex-y {
  display: flex;
  flex-direction: column;
}
.flex-column {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.flex-between {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.flex-around {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
}
.flex-around-column {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
}
.flex-align-center {
  display: flex;
  align-items: center;
}
.flex-1 {
  flex: 1;
  overflow: hidden;
}
.flex-wrap {
  @extend .flex-x;
  flex-wrap: wrap;
}
.flex-c-center {
  @extend .flex;
  justify-content: center;
}
.flex-shrink {
  flex-shrink: 0;
}
.flex-end {
  @extend .flex;
  justify-content: flex-end;
}

.cursor-pointer {
  cursor: pointer;
}

.w100 {
  width: 100%;
}
.h100 {
  height: 100%;
}
@for $i from 1 through 1000 {
 .width#{$i} {
    width: #{$i}px;
  }
 .height#{$i} {
    height: #{$i}px;
  }
}

// 循环生成margin
@for $i from 0 through 100 {
 .m#{$i} {
    margin: #{$i}px;
  }
 .mt#{$i} {
    margin-top: #{$i}px;
  }
 .mb#{$i} {
    margin-bottom: #{$i}px;
  }
 .ml#{$i} {
    margin-left: #{$i}px;
  }
  .mr#{$i} {
    margin-right: #{$i}px;
  }
  .p#{$i} {
    padding: #{$i}px;
  }
  .pt#{$i} {
    padding-top: #{$i}px;
  }
  .pb#{$i} {
    padding-bottom: #{$i}px;
  }
  .pl#{$i} {
    padding-left: #{$i}px;
  }
  .pr#{$i} {
    padding-right: #{$i}px;
  }
  .px#{$i} {
    padding-left: #{$i}px;
    padding-right: #{$i}px;
  }
  .py#{$i} {
    padding-top: #{$i}px;
    padding-bottom: #{$i}px;
  }
}

// 单行文本 超出省略号
.ellipsis-1 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
// 多行文本 超出省略号
@for $i from 2 through 5 {
  .ellipsis-#{$i} {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    line-clamp: #{$i};
    -webkit-line-clamp: #{$i};
    overflow: hidden;
  }
}

@mixin appearance-none {
  -webkit-appearance: none;
  appearance: none;
}

input[type="submit"], input[type="reset"], input[type="button"], input[type="text"], input[type="password"] {
  @include appearance-none;
  outline: 0;
}

/*混动条样式*/
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-thumb {
  border: 3px solid transparent;
  background-clip: padding-box;
  border-radius: 7px;
  min-height: 84px;
  background-color: rgba(187, 187, 187, 0.88);
}

::-webkit-scrollbar-track {
  background-color: transparent;
}
@supports not selector(::-webkit-scrollbar) {
  /* 火狐 滚动条 */
  * {
    scrollbar-width: thin;
    scrollbar-color: rgba(187, 187, 187, 0.88) transparent;
  }
}