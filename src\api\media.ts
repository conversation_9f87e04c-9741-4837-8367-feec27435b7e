import http from '@/utils/request'

// 获取我的视频/图文列表
export const getVideoList = (params: any) => {
  return http.request({
    url: 'sendVideo/selSendlog',
    method: 'get',
    params
  })
}

// 分享抖音
export const shareDouApi = (params: any) => {
  return http.request({
    url: 'sendVideo/shareDy',
    method: 'get',
    params
  })
}

// ai创作 文心一言
export const aiCreateWenxin = (data: any) => {
  return http.request({
    url: 'chatgpt/wenxin',
    method: 'post',
    data
  })
}

// ai创作 ChatGPT
export const aiCreateGPT = (data: any) => {
  return http.request({
    url: 'chatgpt/openai',
    method: 'post',
    data
  })
}

// 获取免费使用次数
export const getAINumApi = () => {
  return http.request({
    url: 'chatgpt/use_number',
    method: 'get'
  })
}

// 发布视频
export const sendVideoApi = (data: any) => {
  return http.request({
    url: 'sendVideo/sendVideo',
    method: 'post',
    data
  })
}

// 发布图文
export const sendArticleApi = (data: any) => {
  return http.request({
    url: 'sendArticle/sendArticle',
    method: 'post',
    data
  })
}

// 保存视频
export const saveVideoApi = (data: any) => {
  return http.request({
    url: 'sendVideo/addSendlog',
    method: 'post',
    data
  })
}

// 保存图文
export const saveArticleApi = (data: any) => {
  return http.request({
    url: 'sendArticle/addSendlog',
    method: 'post',
    data
  })
}

// 删除视频/图文
export const delVideoApi = (data: any) => {
  return http.request({
    url: 'sendArticle/delSendlog',
    method: 'post',
    data
  })
}

// 企业外部新闻 列表
export const getNewsListApi = (params: any) => {
  return http.request({
    url: 'ExternalNews/lst',
    method: 'get',
    params
  })
}

// 企业外部新闻 添加
export const addNewsApi = (data: any) => {
  return http.request({
    url: 'ExternalNews/edit',
    method: 'post',
    data
  })
}

// 企业外部新闻 发布
export const publishNewsApi = (data: any) => {
  return http.request({
    url: 'ExternalNews/push',
    method: 'post',
    data
  })
}

// 企业外部新闻 删除
export const delNewsApi = (data: any) => {
  return http.request({
    url: 'ExternalNews/del',
    method: 'post',
    data
  })
}

// 同步微信公众号 保存
export const saveWeChatApi = (data: any) => {
  return http.request({
    url: 'Materials/create',
    method: 'post',
    data
  })
}

// 同步微信公众号 发布
export const publishWeChatApi = (data: any) => {
  return http.request({
    url: 'Materials/push_news',
    method: 'post',
    data
  })
}

// 同步微信公众号 上传封面
export const uploadCoverApi = (data: any) => {
  return http.request({
    url: 'Materials/uploadImg',
    method: 'post',
    data
  })
}
