import http from '@/utils/request'

// 站点总览
export const getSiteOverviewApi = (params?: any) => {
  return http.request({
    url: '/ChangeSite/getList',
    method: 'get',
    params
  })
}

//  网站栏目管理
export const getSiteColumnApi = (params?: any) => {
  return http.request({
    url: '/ChangeSite/get_data',
    method: 'get',
    params
  })
}

// 改变网站栏目显示状态
export const changeSiteColumnApi = (params?: any) => {
  return http.request({
    url: '/ChangeSite/navshow',
    method: 'post',
    data: params
  })
}

// 获取站点favicon
export const getSiteFaviconApi = (params?: any) => {
  return http.request({
    url: '/SystemInfo/getFaviconInfo',
    method: 'get',
    params
  })
}

// 保存站点favicon
export const saveSiteFaviconApi = (params?: any) => {
  return http.request({
    url: '/SystemInfo/createFile',
    method: 'post',
    data: params
  })
}

// 获取站点layout和domain信息
export const getSiteLayoutDomainApi = (params?: any) => {
  return http.request({
    url: '/SystemInfo/getSeoInfo',
    method: 'get',
    params
  })
}

// 获取页面列表
export const getPageListApi = (params?: any) => {
  return http.request({
    url: '/ChangeSite/get_data',
    method: 'get',
    params
  })
}

// 保存seo信息
export const saveSeoInfoApi = (params?: any) => {
  return http.request({
    url: '/ChangeSite/addSeo',
    method: 'post',
    data: params
  })
}

// 一键设置seo信息
export const setSeoInfoApi = (params?: any) => {
  return http.request({
    url: '/ChangeSite/all_add',
    method: 'post',
    data: params
  })
}

// 设置电子标识
export const setElectronicApi = (params?: any) => {
  return http.request({
    url: '/ChangeSite/electronictags',
    method: 'post',
    data: params
  })
}

// 短信管理列表
export const getSmsListApi = (params?: any) => {
  return http.request({
    url: '/ContentList/smsList',
    method: 'get',
    params
  })
}

// 修改短信管理状态
export const changeSmsStatusApi = (params?: any) => {
  return http.request({
    url: '/ContentList/sms_start',
    method: 'post',
    data: params
  })
}

// 短信管理添加
export const addSmsApi = (params?: any) => {
  return http.request({
    url: '/ContentList/sms_create',
    method: 'post',
    data: params
  })
}

// 短信管理编辑
export const editSmsApi = (params?: any) => {
  return http.request({
    url: '/ContentList/sms_update',
    method: 'post',
    data: params
  })
}

// 短信管理删除
export const delSmsApi = (params?: any) => {
  return http.request({
    url: '/ContentList/smsdel',
    method: 'post',
    data: params
  })
}
