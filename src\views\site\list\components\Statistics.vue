<script setup lang='ts'>
import { watch, reactive, onMounted, ref, markRaw } from 'vue'
import { awaitHandle } from '@/utils'
import { ElMessage } from "element-plus"
import VisitLine from '@/components/ECharts/VisitLine.vue'
import { getYesterdayData, siteCount } from '@/api/site'

let dataInfo: any = ref({
  btnList: [
    { name: '昨天', value: 1 },
    { name: '最近7天', value: 2 },
    { name: '最近30天', value: 3 }
  ],
  typeList: [],
  btnIndex: 0,
  siteId: null,
  visitType: 1,
  visitValue: '',
  rangeDate: '',
  nowVisitData: <any>{}
})

const apiMap: any = markRaw({
  1: getYesterdayData,
  2: siteCount,
  3: getYesterdayData,
  4: getYesterdayData,
  5: getYesterdayData
})

const getVisitsByType = async () => {
  let type = dataInfo.value.visitType,
    value = dataInfo.value.visitValue;
  if(type == 5) value = dataInfo.value.rangeDate[0] + ' ~ ' + dataInfo.value.rangeDate[1]
  let res

  const api:any = apiMap[type]

  res = await awaitHandle(api({ type: type, value: value, typevalue: type, keyvalue: value }))

  if (res[1]) {
    dataInfo.value.nowVisitData = res[1]
  }
}

// 切换类型
const changeData = (index: number, value: any) => {
  dataInfo.value.btnIndex = index;
  dataInfo.value.visitType = value;
  getVisitsByType()
}

const disabledDate = (time: any) => {
  return time.getTime() > Date.now();
}

// 时间段选择
const rangeChange = (val: any) => {
  // console.log(val)
  var s = new Date(val[0]);
  var e = new Date(val[1]);
  //计算两个时间间隔天数
  var d = (e.getTime() - s.getTime()) / (1000 * 60 * 60 * 24);
  var d = (e.getTime() - s.getTime()) / (1000 * 60 * 60 * 24);
  // console.log(d, 'd');
  if (d > 31) {
    // layer.msg('最多选择31天', { icon: 5 });
    ElMessage.error('最多选择31天')
  } else {
    dataInfo.value.rangeDate = val
    dataInfo.value.btnIndex = 4;
    dataInfo.value.visitType = 5;
    getVisitsByType()
  }
}

onMounted(() => {
  getVisitsByType()
})

watch(
  () => dataInfo.value.visitValue,
  () => {
    dataInfo.value.btnIndex = 3;
    dataInfo.value.visitType = 4;
    getVisitsByType()
  }
)
</script>

<template>
  <div class="content-box">
    <div class="statistics-top flex">
      <div class="btn-box flex">
        <template v-for="(item, index) in dataInfo.btnList" :key="index">
          <div :class="['item', { 'active': dataInfo.btnIndex == index }]" @click="changeData(index, item.value)">
            {{ item.name }}
          </div>
        </template>
        <el-date-picker style="width: 160px;margin-right: 10px;" v-model="dataInfo.visitValue" prefix-icon="Calendar" format="YYYY-MM-DD" value-format="YYYY-MM-DD" type="date" placeholder="请选择日期" :disabled-date="disabledDate" />
        <el-date-picker style="width: 240px;" v-model="dataInfo.rangeDate" prefix-icon="Calendar" type="daterange" is-range
          range-separator="~" start-placeholder="开始日期" end-placeholder="结束日期" value-format="YYYY-MM-DD"
          @change="rangeChange" :disabled-date="disabledDate" />
      </div>
    </div>
    <div class="visit-box">
      <div class="visit-info flex">
        <div class="visit-part01 flex">
          <div class="item flex">
            <i></i><span class="key">浏览量(pv)</span><span class="value">{{ dataInfo?.nowVisitData?.look_nums }}</span>
          </div>
          <div class="item flex">
            <i style="background: #B3A3FF"></i><span class="key">访客量(uv)</span><span class="value"
              style="color: #B3A3FF;">{{ dataInfo?.nowVisitData?.ip_nums }}</span>
          </div>
        </div>
      </div>
      <div class="e-box">
        <visit-line v-if="dataInfo.nowVisitData" :data="dataInfo.nowVisitData" width="100%" height="400px" :isShowTitle="false"></visit-line>
      </div>
    </div>
  </div>
</template>

<style scoped lang='scss'>
.visit-box {
  background: rgba(255, 255, 255, 0.99);
  box-shadow: 0px 6px 12px 0px rgb(216 224 234 / 20%);
  padding: 20px 30px;
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 20px;
}

:deep(.el-input__wrapper) {
  border-radius: 30px;
}

.btn-box .material-upload {
  margin-right: 10px;
}

.statistics-top {
  width: 100%;
  justify-content: space-between;
  padding: 13px 22px;
  box-sizing: border-box;
}

.statistics-top .layui-form-item {
  margin-bottom: 0;
}

.statistics-top .layui-form-item .layui-inline {
  margin: 0;
}

.e-box {
  width: 100%;
  height: 400px;
}

.btn-box .item {
  padding: 0 13px;
  height: 28px;
  text-align: center;
  line-height: 28px;
  background: #FFFFFF;
  border: 1px solid #E3E5EC;
  border-radius: 14px;
  font-size: 12px;
  font-family: PingFang SC;
  font-weight: 500;
  color: #7F8294;
  transition: all ease-in-out 0.3s;
  cursor: pointer;
  margin-right: 10px;
}

.btn-box .item.active {
  background: #2859FF;
  box-shadow: 0px 4px 10px 0px rgba(40, 89, 255, 0.2);
  color: #FFFFFF;
}

.btn-box .item:hover {
  border-color: #A0B6FF;
  color: #2859FF;
}

.btn-box .item.active:hover {
  background: #2859FF;
  box-shadow: 0px 4px 10px 0px rgba(40, 89, 255, 0.2);
  color: #FFFFFF;
}

.visit-part01 .item {
  font-size: 12px;
  font-family: PingFang SC;
  font-weight: 400;
  color: #696C7D;
  margin-right: 56px;
}

.visit-part01 .item i {
  display: inline-block;
  width: 20px;
  height: 2px;
  background: #3765FF;
  border-radius: 1px;
  margin-right: 10px;
}

.visit-part01 .item .value {
  margin-left: 10px;
  font-size: 18px;
  font-family: PingFang SC;
  font-weight: 800;
  color: #3765FF;
}

.btn-box .layui-input {
  background: none;
  border: none;
  height: 100%;
  line-height: 100%;
  width: 80px;
}
</style>
