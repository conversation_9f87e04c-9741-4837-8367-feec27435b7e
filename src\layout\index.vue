<!--  -->
<template>
  <div class="app-main flex-y">
    <header-view class="w100"></header-view>
    <div class="app-right h100 flex-1 flex-x">
      <vertical-menu class="h100"></vertical-menu>
      <div class="app-container w100 flex-1">
        <div class="app-content h100">
          <!-- 动画 右侧进入 -->
          <router-view v-slot="{ Component }">
            <transition name="fade" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </div>
      </div>
    </div>
    <!-- <MarketDialog /> -->
  </div>
</template>

<script lang="ts" setup>
import { onMounted, defineAsyncComponent } from 'vue'
import { useStore } from 'vuex'
import { useRoute, useRouter } from 'vue-router'

const store = useStore()
const route = useRoute()
const router = useRouter()

const VerticalMenu = defineAsyncComponent(() => import('@/layout/components/VerticalMenu.vue'))
const HorizontalMenu = defineAsyncComponent(() => import('@/layout/components/HorizontalMenu.vue'))
const HeaderView = defineAsyncComponent(() => import('@/layout/components/Header.vue'))
const MarketDialog = defineAsyncComponent(() => import('@/components/MarketDialog.vue'))

onMounted(() => {
  // console.log(store, 'store')
  // store.dispatch('app/getMenuList')
})
</script>

<style lang='scss' scoped>
.app-main {
  height: 100vh;
  .app-right {    
    .app-container {
      overflow: auto hidden;
      .app-content {
        min-width: 1300px;
      }
    }
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: all 0.5s ease-in-out;
}

.fade-enter-from,
.fade-leave-to {
  transform: translateX(20px);
  opacity: 0;
}

</style>