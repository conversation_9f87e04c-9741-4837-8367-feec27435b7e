<script setup lang="ts">
import useSiteManageInfo from "@/hooks/useSiteManageInfo";
import {markRaw, ref} from "vue";
import {ElMessage} from "element-plus";
import {setElectronicApi} from "@/api/siteManage";

const formRef: any = ref(null);

const formData = ref({
  id: '',
  electronic_tags: ''
})

const {loading, getFavicon} = useSiteManageInfo();
getFavicon(true, (res: any) => {
  formData.value.id = res.id;
  formData.value.electronic_tags = res.electronic_tags;
});

const rules = markRaw({
  electronic_tags: [
    { required: true, message: '请输入电子标签代码', trigger: 'blur' },
  ]
})

const save = async () => {
  if (!formRef.value) return
  formRef.value.validate(async (valid: boolean, fields: string) => {
    if (valid) {
      try {
        loading.value = true
        const res = await setElectronicApi(formData.value)
        console.log(res);
        ElMessage.success('保存成功')
        await getFavicon()
      } catch (e) {
        console.log(e)
      } finally {
        loading.value = false
      }
    }
  })
}
</script>

<template>
  <div class="h100 wrap">
    <div class="box" v-loading="loading">
      <el-form ref="formRef" :model="formData" :rules="rules">
        <el-form-item label="电子标签代码：" prop="electronic_tags">
          <el-input type="textarea" :rows="5" placeholder="请输入电子标签代码" v-model="formData.electronic_tags" />
        </el-form-item>
        <el-form-item>
          <div style="text-align: right;width: 100%">
            <el-button type="primary" @click="save" class="btn">保存</el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<style scoped lang="scss">
.wrap {
  overflow: auto;
  padding: 10px 0;
  .box {
    width: 100%;
    background: #fff;
    padding: 20px 30px;
    .btn{
      width: 120px;
      height: 34px;
    }
  }
}
</style>
