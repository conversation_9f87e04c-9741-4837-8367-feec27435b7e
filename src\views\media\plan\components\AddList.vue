<template>
  <edit-dialog
    ref="editDialogRef"
    v-model:dialogShow="dialogShow"
    v-bind="formConfig"
    @success="refreshList">
  </edit-dialog>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import { useDialog } from '@/hooks/useDialog'
import { addCustomApi } from '@/api/media/plan'

const { EditDialog, editDialogRef, dialogShow, dialogData, openDialog } = useDialog()

const emits = defineEmits(['refresh'])

const formConfig = ref({
  title: '添加营销日期',
  width: '700px',
  formData: {},
  formConfig: {
    formList: [
      { label: '日期选择', prop: 'date', type: 'date', required: true, placeholder: '请选择日期' },
      { label: '活动名称', prop: 'title', type: 'input', required: true, placeholder: '请输入活动名称' },
      { label: '背景图', prop: 'bg_img', type: 'uploadImg', required: true, tips: '背景图尺寸建议236*130', uploadConfig: { uploadBtn: true, width: '236px', height: '130px', position: 'right' } }
    ],
    submit: addCustomApi
  }
})

const refreshList = () => {
  emits('refresh', true)
}

watch(dialogData, (newVal) => {
  console.log(newVal)
  if (newVal.id) {
    formConfig.value.title = '编辑营销日期'
  } else {
    formConfig.value.title = '添加营销日期'
  }
})

defineExpose({
  openDialog
})

</script>

<style lang="scss" scoped>
p.tips {
  margin-top: 10px;
  color: red;
  width: 100%;
}
</style>
