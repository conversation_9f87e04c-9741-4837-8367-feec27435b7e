import {computed, ref} from "vue";
import {ElMessage, ElMessageBox} from "element-plus";

export const useInteraction = (config: object, actions: any[], delApi: (p: { id: string }, p1: {
  headers: { "Content-Type": string }
}) => Promise<any>, exportUrl: string) => {
  // 获取当前环境
  const env = process.env.NODE_ENV;

  const basicConfig = ref({
    apiType: undefined,
    showIndex: false,
    multipleSelection: [],
    params: {}
  })

  const tableConfig = computed(() => {
    return Object.assign(basicConfig.value, config)
  })

  const dataListRef = ref<any>(null)

  /**
   * 刷新列表
   */
  const refreshTable = () => {
    dataListRef.value.refresh()
  }
  /**
   * 删除接口
   * @param ids
   */
  const deleteRow = (ids: string) => {
    ElMessageBox.confirm('确定删除吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      // 执行删除操作
      delApi && await delApi({id: ids}, {headers: {'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'}})
      refreshTable()
      ElMessage.success('删除成功');
    }).catch(() => {
      console.log('已取消删除');
    })
  }
  /**
   * 批量删除
   * @param rows
   */
  const deleteSelected = (rows: any[]) => {
    const ids = rows.map(item => item.id).toString()
    deleteRow(ids)
  }

  /**
   * 导出
   */
  const exportData = (id?: string[]) => {
    // 拼接url
    const baseUrl = env === 'development' ? 'https://jzt_dev_1.china9.cn/' : window.location.origin
    let url = baseUrl + exportUrl;
    if (id) {
      url += '?id=' + id.toString()
    }
    window.open(url);
  }

  /**
   * 批量导出
   */
  const exportSelected = (rows: any[]) => {
    let newArr = rows.map(item => item.id);
    exportData(newArr)
  }

  /**
   * 全部导出
   */
  const exportAll = () => {
    exportData()
  }

  /**
   * 按钮列表
   */
  const basicActionList = ref([
    {
      name: '批量导出',
      icon: 'icon-piliangdaochu',
      key: 'exportSelected',
      type: 'action',
      checkSelected: true,
      action: exportSelected
    },
    {
      name: '全部导出',
      icon: 'icon-piliangdaochu',
      key: 'exportAll',
      type: 'action',
      checkSelected: false,
      action: exportAll,
    },
    {name: '删除', icon: 'icon-shanchu1', key: 'delete', type: 'action', checkSelected: true, action: deleteSelected},
  ])
  const actionList = computed(() => {
  //   把actions中的元素，按照position字段中的数字插入到basicActionList中
    const actionList = [...basicActionList.value]
    actions.forEach(item => {
      actionList.splice(item.position, 0, item)
    })
    return actionList
  })

  /**
   * 按钮点击事件
   * @param button
   */
  const dataAction = (button: any) => {
    if (button.checkSelected) {
      if (!tableConfig.value.multipleSelection.length) {
        ElMessage.warning('请先选择数据')
        return
      }
    }
    button.action(tableConfig.value.multipleSelection)
  }

  /**
   * 删除当前行
   * @param row
   */
  const del = (row: any) => {
    deleteRow(row.id)
  }

  return {
    dataListRef,
    tableConfig,
    refreshTable,
    deleteRow,
    deleteSelected,
    exportData,
    exportSelected,
    exportAll,
    basicActionList,
    actionList,
    dataAction,
    del
  }
}
