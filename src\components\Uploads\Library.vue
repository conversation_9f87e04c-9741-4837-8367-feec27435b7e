<template>
  <div class="library-wrap">
    <div class="library-top flex" v-if="props.isShowCate">
      <el-form :inline="true" class="demo-form-inline" style="width: 100%">
        <el-row>
          <el-col :span="12">
            <el-form-item label="素材库分类：">
              <el-select v-model="cateId" placeholder="请选择素材库分类" style="flex: 1; width: 100%" filterable>
                <el-option label="全部素材分类" value="0" />
                <el-option v-for="item in libraryCate" :key="item.id" :label="item.type_title" :value="item.id">
                  <span v-html="item.title"></span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <el-input v-model="searchName" placeholder="请输入素材名称搜索" @keydown.enter="searchByName" @blur="searchByName" style="width: 100%">
                <template #suffix>
                  <i class="iconfont icon-sousuo" style="cursor: pointer" @click="searchByName"></i>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="check-all" v-if="!props.isRadio">
      <el-checkbox name="type" v-model="isCheckAll" label="全选" @change="changeCheckAll" :disabled="max != -1"/>
    </div>
    <div id="library-list-box" :class="['library-list-box', { 'has-page': props.showPage }]" @scroll="scrollChange">
      <template v-if="tableData.length > 0">
        <div class="library-list flex" id="library-list">
          <template v-for="(item, index) in tableData" :key="index">
            <div :class="['box_item', { active: item.isCheck }]" @click="checkItem(index, $event)">
              <div class="item-info-box">
                <div class="item-check-box">
                  <el-checkbox name="type" v-model="item.isCheck" @change="checkItem(index, $event)" :disabled="(!isRadio && checkedDataLength >= max && !item.isCheck) && max != -1" />
                </div>
                <div v-if="fileType == -2" class="img-title-img video-box">
                  <img v-if="item.type === 1" :src="item.url" @error="imgError($event, 'img')" />
                  <template v-else-if="item.type === 2">
                    <div v-if="getFileType(item.url) === 'audio'" class="audio-wrap"
                      :style="`position: relative; background-image: url(${require('@/assets/images/system/audio-error.jpg')}); height: 110px;`">
                      <i class="iconfont icon-yunhang"></i>
                      <audio :src="item.url"></audio>
                    </div>
                    <div v-else class="audio-wrap"
                      :style="filterBackground(item)">
                      <i class="iconfont icon-yunhang"></i>
                      <video :src="item.url" @canplay="item.hidePoster = true"></video>
                    </div>
                  </template>
                  <img v-else :src="getFileImg(item.url)" alt="" class="file">
                </div>
                <div v-else-if="fileType == -1" class="img-title-img video-box">
                  <div class="audio-wrap"
                    :style="filterBackground(item)">
                    <i class="iconfont icon-yunhang" @change="previewVideo(item.video, 2)"></i>
                    <video :src="item.video" @canplay="item.hidePoster = true"></video>
                  </div>
                </div>
                <template v-else>
                  <div v-if="fileType === 1" class="img-title img video-box">
                    <img :src="item.url" @error="imgError($event, 'img')" @change="previewVideo(item.url, 1)" />
                  </div>
                  <div v-else-if="fileType === 2" class="img-title img video-box">
                    <div v-if="getFileType(item.url) === 'audio'" class="audio-wrap"
                      :style="`position: relative; background-image: url(${require('@/assets/images/system/audio-error.jpg')}); height: 110px;`">
                      <i class="iconfont icon-yunhang" @change="previewVideo(item.url, 2)"></i>
                      <audio :src="item.url"></audio>
                    </div>
                    <div v-else class="audio-wrap"
                      :style="filterBackground(item)">
                      <i class="iconfont icon-yunhang" @change="previewVideo(item.url, 2)"></i>
                      <video :src="item.url" @canplay="item.hidePoster = true"></video>
                    </div>
                  </div>
                </template>
              </div>
              <div class="imgTitle">{{ item.title }}</div>
            </div>
          </template>
        </div>
        <template v-if="!props.showPage">
          <div class="none-data" v-if="loading">加载中</div>
          <div class="none-data" v-if="lPage == params.page">暂无更多</div>
        </template>
      </template>
      <div class="none-data h100 flex-c-center" v-else>这里还没有素材</div>
    </div>
    <div v-if="showPage && total > 0" class="pagination flex-c-center">
      <data-page v-model:page="params.page" :total="total" :totalPage="lPage" @pagination="getLibraryList"></data-page>
    </div>
    <preview
      :type="previewData.type"
      :url="previewData.url"
      v-if="previewData.show"
      @closePreview="closePreview"
    ></preview>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, reactive, defineAsyncComponent, computed } from "vue";
import { ElMessage } from "element-plus";
import { useLibrary } from "@/hooks/useLibrary";
import { typeListAll, getChildFolder, getShareFileApi } from "@/api/bucket";
import { getVideoList } from "@/api/media";

const DataPage = defineAsyncComponent(() => import('@/components/Global/DataPage.vue'))
const Preview = defineAsyncComponent(() => import('@/components/Uploads/Preview.vue'));

const { getFileImg } = useLibrary()

let props = defineProps({
  fileType: {
    // 文件类型 1 图片 2 视频 3 其他 -1我的视频 -2同步云经理
    type: Number,
    default: 1
  },
  libraryImg: {
    // 当前选择的素材库图片
    type: [String, Array],
    default: ''
  },
  cateId: {
    // 当前选择的素材库分类
    type: String,
    default: ""
  },
  limit: {
    type: Number,
    default: 10
  },
  isShowCate: {
    type: Boolean,
    default: true
  },
  showPage: {
    type: Boolean,
    default: false
  },
  isRadio: {
    type: Boolean,
    default: true
  },
  searchName: {
    type: String,
    default: ""
  },
  max: {
    type: Number,
    default: 6
  }
});

let libraryCate = ref<any>([]); // 素材库分类列表
let cateId = ref<any>(props.cateId); // 素材库分类列表
let searchName = ref<any>(""); // 素材库搜索
const emits = defineEmits(["update:libraryImg", "cateListChange"]);

let isCheckAll = ref<Boolean>(false);

let tableData:any = ref([]); //列表数据
let lPage = ref(1); //总页数
let total = ref(0); //总条数
let loading = ref(false); //是否正在加载
let defaultParams = {
  limit: props.limit,
  page: 1,
  bucket_type_id: cateId.value,
  type: props.fileType,
  title: props.searchName
};
let params = ref(JSON.parse(JSON.stringify(defaultParams)));

const filterBackground = (item: any) => {
  return !item.hidePoster && `background-image: url(${require('@/assets/images/system/video-error.jpg')})`
}

watch(
  () => props.searchName,
  v => {
    params.value.title = v;
  },
  {
    immediate: true
  }
);

// 获取素材库分类
const getLibraryCate = async () => {
  const result = await typeListAll();
  libraryCate.value = result;
  emits("cateListChange", result);
};

// 获取素材库列表
const getLibraryList = (page = 0) => {
  loading.value = true;
  if (page && Number(page)) {
    params.value.page = page;
  }
  let fileApi = getChildFolder;
  if (props.fileType === -1) {
    fileApi = getVideoList;
    params.value = { limit: params.value.limit, page: params.value.page, type: "video" };
  } else if (props.fileType === -2) {
    fileApi = getShareFileApi;
    if (!params.value.cat_id) params.value.cat_id = 'all'
    delete params.value.type
  }
  console.log(params.value, 111);
  fileApi(params.value).then((data:any) => {
    loading.value = false;
    console.log(data)
    let dataList = data.data;
    dataList.forEach((item: any, index: number) => {
      item.isCheck = false;
      item.hidePoster = false;
    });
    if (params.value.page > 1 && !props.showPage) {
      tableData.value = tableData.value.concat(dataList);
    } else {
      tableData.value = dataList;
    }
    total.value = data.total;
    lPage.value = data.last_page;
  });
};

// 选择素材
const checkItem = (index: number, event: any) => {
  let list = JSON.parse(JSON.stringify(tableData.value));
  if (event && event.target) {
    if (["I", "IMG"].includes(event.target.tagName)) {
      previewVideo(tableData.value[index].url, event.target.tagName === "I" ? 2 : 1);
    } else {
      if (props.isRadio) {
        list.forEach((item:any) => {
          item.isCheck = false;
        });
      }
      if (props.max !== -1 && !list[index].isCheck && checkedDataLength.value >= props.max) {
        ElMessage.warning(`最多只能选择${props.max}个`);
      } else {
        list[index].isCheck = !list[index].isCheck;
      }
    }
  } else {
    list[index].isCheck = !list[index].isCheck;
  }
  tableData.value = list;
};

// 监听滚动条滚动
const scrollChange = (e: any) => {
  if (!props.showPage) {
    const boxHeight = (document as any).getElementById("library-list-box").offsetHeight;
    // 获取table_list的真实高度（浮动内容的真实高度）
    const tableHeight = (document as any).getElementById("library-list").offsetHeight;
    // boxHeight和滑块浮动的高度相差小于50 && 不在加载中 && 不是最后一页
    if (
      tableHeight - (e.target.scrollTop + boxHeight) < 50 &&
      !loading.value &&
      params.value.page < lPage.value
    ) {
      // 这里触发加载请求
      params.value.page = params.value.page + 1;
      getLibraryList();
    }
  }
};

// 监听全选
const changeCheckAll = () => {
  let dataList = tableData.value;
  if (isCheckAll.value) {
    dataList.forEach((item:any, index: number) => {
      item.isCheck = true;
    });
  } else {
    dataList.forEach((item:any, index: number) => {
      item.isCheck = false;
    });
  }
  tableData.value = dataList;
};

// 视频预览
const previewData = reactive({
  show: false,
  url: "",
  type: 1
});
const previewVideo = (url: string, type: number) => {
  previewData.url = url;
  previewData.type = type;
  previewData.show = true;
};
const closePreview = () => {
  previewData.show = false;
};

onMounted(() => {
  getLibraryCate();
  getLibraryList();
});

const checkedDataLength = computed(() => {
  let dataList = tableData.value;
  let checkData = dataList.filter((item: any) => item.isCheck);
  return checkData.length;
})

watch(
  () => cateId.value,
  () => {
    params.value = JSON.parse(JSON.stringify(defaultParams));
    params.value.bucket_type_id = cateId.value;
    params.value.title = searchName.value;
    tableData.value = [];
    getLibraryList();
  }
);

watch(
  () => searchName.value,
  () => {
    params.value.title = searchName.value;
    console.log(params.value);
  }
);

watch(
  () => props.cateId,
  () => {
    params.value = JSON.parse(JSON.stringify(defaultParams));
    params.value.bucket_type_id = props.cateId;
    tableData.value = [];
    params.value.title = props.searchName;
    getLibraryList();
  }
);

watch(
  () => tableData.value,
  () => {
    let dataList = tableData.value;
    let checkData = dataList.filter((item: any) => item.isCheck);
    if (checkData.length == dataList.length && dataList.length) {
      isCheckAll.value = true;
    } else {
      isCheckAll.value = false;
    }
    if (checkData.length > 0) {
      let data;
      if (props.isRadio) {
        data = checkData[0].url;
      } else {
        data = checkData.map((item: any) => {
          let obj:any = {id: item.id, url: item.url, title: item.title}
          if (props.fileType == -1) {
            obj.url = item.video
          } else if (props.fileType == -2) {
            obj.type = item.type  
          }
          return obj;
        });
      }
      emits("update:libraryImg", data);
    } else {
      emits("update:libraryImg", []);
    }
  },
  { deep: true }
);

// 触发父元素搜索
const searchByName = () => {
  getLibraryList(1);
};

// 图片加载失败
const imgError = (e: any, type: string) => {
  let obj:any = {
    img: require('@/assets/images/system/img-error.jpg'),
    video: require('@/assets/images/system/video-error.jpg'),
    audio: require('@/assets/images/system/audio-error.jpg')
  };
  e.target.src = obj[type] || require('@/assets/images/system/img-error.jpg');
};

// 根据文件名判断是视频还是音频
const getFileType = (fileName: string) => {
  if (!fileName) {
    return "video";
  }
  try {
    console.log("[ fileName ] >", fileName);
    let extName = fileName.split(".").slice(-1)[0];
    console.log("[ extName ] >", extName);
    // 音频后缀列表
    const audioList = ["mp3", "wma", "wav", "amr", "aac"];
    if (audioList.includes(extName)) {
      return "audio";
    } else {
      return "video";
    }
  } catch (e) {
    console.log(e, "获取文件后缀失败");
    return "video";
  }
};

// defineExpose({ showDialog })
defineExpose({ getLibraryList });
</script>

<style scoped lang="scss">
@import '@/assets/css/bucket.scss';

.library-top {
  padding: 10px 0;
  margin-bottom: 0px;
}


.el-form--inline .el-form-item {
  width: calc(100% - 32px);
}

.audio-wrap {
  position: relative;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}
</style>
