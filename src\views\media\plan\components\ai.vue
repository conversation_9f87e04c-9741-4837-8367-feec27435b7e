<template>
  <div class="plan-ai-app">
    <!-- 升级提示 -->
    <div class="frosted-glass" v-if="!isAgent">
        <div class="glass-content-wrap float-wrap">
            <div class="to-wenxin">
                <a href="https://yiyan.baidu.com" target="_blank">
                    <img src="@/assets/images/media/wenxin1.png" alt="文心一言">
                    <p>跳转到文心一言</p>
                </a>
            </div>
            <div class="to-upgrade" @click="toggleScale">
                <img src="@/assets/images/media/jzt2.png" alt="升级到AI版">
                <p>联系您的客服升级至建站通AI版</p>
                <div class="tel">
                    官方电话：<a href="tel:************" :class="{scale: scale}">************</a>
                </div>
            </div>
        </div>
    </div>
    <div v-else class="chat-wrap chat-box">
        <div class="ai-content">
            <div v-if="!chatList.length" class="no-data">
                <img src="@/assets/images/media/no-account.png" alt=""/>
                <p>当前暂无编辑内容生成</p>
            </div>
            <div v-else class="chat-list" ref="chatBox">
                <div class="loading" v-if="!isLoad">加载中...</div>
                <div class="no-more" v-if="lastPage == listQ.page">没有更多了</div>
                <div v-for="(item, index) in chatList" :key="index" class="chat-item">
                    <div class="time">{{ item.create_time }}</div>
                    <div class="chat-info right">
                        <img src="@/assets/images/media/logo.png" alt="">
                        <div class="chat">{{ item.content }}</div>
                    </div>
                    <div class="recover">
                        <div class="chat-info left">
                            <img src="@/assets/images/media/wenxin.png" alt="">
                            <div class="chat" style="white-space: pre-line;">{{ changeStr(item.result) ||
                                '服务器异常，请重新提问'}}
                            </div>
                        </div>
                        <div class="btn-box" v-if="showBtn(item.result)">
                            <div class="btn" @click="copyText(item.result)">复制</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="input chat-box">
        <textarea v-model="content" placeholder="请输入内容"></textarea>
        <div class="btn-wrap">
            <button @click="create">立即生成</button>
        </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus';
import store from '@/store';
import { getAiChatApi, addAiApi } from "@/api/media/plan"


const siteInfo = computed(() => store.getters.siteInfo)
const siteId = computed(() => store.getters.siteId)
const isAgent = computed(() => store.getters.isAgent)

const scale = ref(false)
const chatBox = ref(null)

const chatList:any = ref([])
const content = ref('')
const isLoad = ref(false)
const lastPage = ref(1)
const listQ = ref({
  page: 1, limit: 10, chatType: 'chat', type: 'wenxin', jzt: 'jzt1', site_id: siteId.value, source: 1
})

const stateBtnFlag = ref(false)

// 升级电话加动画
const toggleScale = () => {
  scale.value = !scale.value;
  setTimeout(() => {
    scale.value = !scale.value;
  }, 1500);
}

//获取问答历史
const getHistory = async () => {
  isLoad.value = false
  try {
    const response:any = await getAiChatApi(listQ.value)
    isLoad.value = true
    let { data, last_page } = response
    let list = data.reverse()
    if (listQ.value.page === 1) {
      chatList.value = list;
      if (list.length > 0) scrollToBottom()
    } else {
      chatList.value = list.concat(chatList.value);
    }
    lastPage.value = last_page
    
  } catch (error) {
    console.log(error);
    isLoad.value = true
  }
}

// 滚动加载
const loadNextPage = () => {
  nextTick(() => {
    var el:any = chatBox.value;
    el.onscroll = () => {
      var scrollTop = el.scrollTop;
      if (scrollTop < 10) {
        // 需要执行的代码
        if (isLoad.value && listQ.value.page < lastPage.value) {
          listQ.value.page++;
          getHistory();
        }
      }
    };
  })
}

// 立即创作
const create = async () => {
  if (!stateBtnFlag.value) {
    chatList.value.push({
      content: content.value,
      result: '思考中...'
    })
    scrollToBottom()
    let data = [{role: "user", content: content.value}]
    stateBtnFlag.value = true
    let params:any = {inputWord: JSON.stringify(data), chatType: 'chat'}
    params.jzt = listQ.value.jzt
    params.site_id = listQ.value.site_id
    params.source = listQ.value.source
    content.value = '';
    try {
      var response = await addAiApi(params)
      stateBtnFlag.value = false
      chatList.value[chatList.value.length - 1].result = response
      scrollToBottom()
    } catch (error) {
      stateBtnFlag.value = false
      chatList.value[chatList.value.length - 1].result = '服务器异常，请点击重试'
      scrollToBottom()
    }
  } else {
    chatList.value[chatList.value.length - 1].result = '思考中...，请稍耐心等待'
    scrollToBottom()
  }
}

// 替换字符
const changeStr = (result:any) => {
  if (result) {
    return result.split('<br>').join('\n')
  } else {
    return ''
  }
}

// 滚动到底部
const scrollToBottom = () => {
  // nextTick 将回调延迟到下次DOM更新循环之后执行。在修改数据之后立即使用它，然后等待DOM更新
  nextTick(() => {
    // dom 元素更新后执行滚动条到底部 否则不生效
    let scrollElem:any = chatBox.value;
    // console.log('scrollHeight: ', scrollElem.scrollHeight);
    scrollElem && scrollElem.scrollTo({
      top: scrollElem.scrollHeight,
      behavior: 'smooth'
    });
  });
}

// 是否显示复制按钮
const showBtn = (str:any) => {
  var show = true
  switch (str) {
    case '':
    case '服务器异常，请重新提问':
    case '服务器异常，请刷新重试':
    case '服务器异常，请点击重试':
    case '思考中...':
    case '思考中...，请稍耐心等待':
    case 'Billing hard limit has been reached':
    case 'You exceeded your current quota, please check your plan and billing details.':
    case null:
    case undefined:
      show = false
      break;
  }
  return show
}

// 复制内容
const copyText = (str:any) => {
  var input = document.createElement('textarea');
  input.value = str;
  document.body.appendChild(input);
  input.select();
  document.execCommand('copy');
  document.body.removeChild(input);
  ElMessage.success('复制成功');
}
// 赋值并发送
const sendMsg = (item:any) => {
  content.value = `请您为我策划一下${item.title}的营销内容吧`
  create()
}

watch(
  () => chatList.value,
  (newVal, oldVal) => {
    // 判断是否需要加载下一页
    if (newVal.length > oldVal.length) {
      loadNextPage()
    }
  },
  { deep: true }
)

onMounted(() => {
  getHistory()
})

defineExpose({ sendMsg })

</script>

<style lang="scss" scoped>

.plan-ai-app {
  padding: 20px;
  box-sizing: border-box;
  position: relative;
  height: calc(100% - 37px);
  > div {
    display: flex;
    flex-direction: column;
    gap: 28px;
  }
  .chat-wrap {
    padding: 20px;
    height: calc(100% - 120px - 28px);
    box-sizing: border-box;
    margin-bottom: 28px;
  }
  .chat-box {
    background: #fff;
  }
  .input {
    border: 1px solid #2859FF;
    height: 120px;
    display: flex;
    flex-direction: column;
    padding: 12px;
    textarea {
      width: 100%;
      outline: none;
      border: none;
      padding: 3px 6px;
      box-sizing: border-box;
      color: #111;
      font-size: 14px;
      flex: 1;
      display: block;
      resize: none;
      background: transparent;
    }
    .btn-wrap {
      display: flex;
      justify-content: flex-end;
      button {
        width: 100px;
        height: 34px;
        background: #2859FF;
        border-radius: 4px;
        border: none;
        color: #fff;
      }
    }
  }
  .ai-content {
    height: 100%;
    box-sizing: border-box;
    .no-data {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      img {
        width: 200px;
        height: 126px;
      }
      p {
        font-size: 14px;
        color: #7F8294;
        margin-top: 20px;
      }
    }
  }
  .chat-list {
    height: 100%;
    overflow-y: auto;
    box-sizing: border-box;
    .chat-item {
      .chat-info {
        display: flex;
        align-items: flex-start;
        gap: 10px;
        .chat {
          background: #E8EBFD;
          padding: 10px 15px;
          white-space: pre-wrap;
          line-height: 26px;
          border-radius: 10px 0 10px 10px;
        }
        img {
          width: 44px;
          height: 44px;
          border: 1px solid #E3E5EC;
          border-radius: 4px;
          object-fit: scale-down;
        }
        &.right {
          flex-direction: row-reverse;
          margin-top: 10px;
        }
        &.left {
          .chat {
            background: #F6F6F6;
            border-radius: 0 10px 10px 10px;
          }
        }
      }
      .time {
        font-size: 12px;
        text-align: center;
        color: #bbbbbb;
        margin-top: 20px;
      }
    }
    .loading, 
    .no-more {
      text-align: center;
      color: #7f8294;
    }
  }
  .recover {
    display: flex;
    align-items: flex-end;
    margin-top: 20px;
  }
  .btn-box {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 100%;
    .btn {
      border: 1px solid #2859FF;
      border-radius: 3px;
      color: #2859FF;
      font-size: 14px;
      text-transform: uppercase;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      width: 44px;
      height: 28px;
      margin-left: 10px;
      margin-bottom: 6px;
    }
  }
  .frosted-glass {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    backdrop-filter: blur(6px);
    background-color: rgba(255, 255, 255, 0.3);
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .glass-content-wrap {
    &>div {
      width: 320px;
      height: 230px;
      background: #FFFFFF;
      border: 1px solid #A0B6FF;
      border-radius: 6px;
      float: left;
      text-align: center;
      img {
        margin-top: 32px;
      }
      p {
        font-size: 18px;
        font-weight: bold;
        color: #2859FF;
      }
    }
    .to-wenxin {
      a {
        display: block;
        width: 100%;
        height: 100%;
        text-align: center;
        text-decoration: none;
      }
    }
    .to-upgrade {
      background: #2859FF;
      margin-left: 40px;
      position: relative;
      cursor: default;
      p {
        color: #fff;
      }
      .tel {
        position: absolute;
        bottom: 0;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 0px 0px 6px 6px;
        width: 100%;
        height: 30px;
        line-height: 30px;
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: 500;
        color: #FFFFFF;
        a{
          color: inherit;
          text-decoration: none;
          display: inline-block;
        }
      }
    }
    .scale{
      animation: big 1s ease-in-out;
    }
  }
  .float-wrap::after {
    content: "";
    display: block;
    clear: both;
  }
}

</style>