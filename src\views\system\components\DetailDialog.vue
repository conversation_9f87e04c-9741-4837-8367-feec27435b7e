<script setup lang="ts">
const props = defineProps({
  dialogData: {
    type: Object,
    default: () => {}
  }
})
</script>

<template>
  <div class="detail-wrap">
    <el-descriptions :column="1">
      <el-descriptions-item label="封面图" v-if="dialogData.image_intro" label-class-name="cover" class-name="cover-content">
        <el-image class="img" fit="contain" :src="dialogData.image_intro" />
      </el-descriptions-item>
      <el-descriptions-item label="标题" v-if="dialogData.title">{{ dialogData.title }}</el-descriptions-item>
      <el-descriptions-item label="简介" v-if="dialogData.introtext">{{ dialogData.introtext }}</el-descriptions-item>
      <el-descriptions-item label="详情" v-if="dialogData.fulltext">
        <div class="detail" v-html="dialogData.fulltext"></div>
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<style scoped lang="scss">
.img{
  width: 100%;
  height: 200px;
}
:deep(.cover){
  vertical-align: top;
}
.detail, :deep(.cover-content){
  display: inline-block;
}
.detail-wrap{
  max-height: 500px;
  overflow: auto;
}
</style>
