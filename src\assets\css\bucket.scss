.check-all {
  height: 36px;
  border: 1px solid #e3e5ec;
  border-bottom: none;
  padding: 0 24px;
}
.library-list-box {
  height: 444px;
  overflow: auto;
  background: #ffffff;
  border: 1px solid #e3e5ec;
  border-radius: 3px;
  &.has-page {
    border: none;
    .library-list {
      padding: 0px 12px;
      .box_item {
        width: calc(100% / 8);
        margin-top: 20px;
        margin-bottom: 0;
      }
    }
  }
  .library-list {
    flex-wrap: wrap;
    padding: 24px;

    .box_item {
      width: calc(100% / 5);
      padding: 5px 12px;
      box-sizing: border-box;
      height: 180px;
      cursor: pointer;
      margin-bottom: 20px;

      img,
      video,
      audio {
        width: 100%;
        height: 100%;
        transition: all ease-in-out 0.3s;
      }

      img,
      video,
      audio {
        object-fit: contain;
        height: 100%;
      }
      .imgTitle {
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: 500;
        color: #1c2b4b;
        line-height: 32px;
        text-align: center;
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .item-info-box {
        position: relative;
        width: 100%;
        height: 120px;
        padding: 0px;
        box-sizing: border-box;
        border: 1px solid transparent;
        transition: all ease-in-out 0.3s;
        overflow: hidden;
        .video-box {
          height: 100%;
          padding: 8%;
        }
        .el-checkbox {
          height: auto;
          position: absolute;
          transition: all ease-in-out 0.3s;
          z-index: 10;
          top: -100px;
          left: -100px;
        }
      }
      &:hover,
      &.active {
        .item-info-box {
          background: #f3f6ff;
          border-radius: 4px;
          padding: 10px;
          display: block;
          box-sizing: border-box;
          .el-checkbox {
            top: 10px;
            left: 10px;
          }
        }
        .small-view-opreations {
          height: 32px;
          i {
            display: block;
            margin: 0;
          }
        }
      }
      &.active {
        .item-info-box {
          border: 1px solid #a0b6ff;
        }
      }
    }
  }
  .none-data {
    text-align: center;
    line-height: 54px;
    color: #999;
    background-color: #f6f8fb;
  }
  .audio-wrap {
    position: relative;
    background-size: cover;
    background-position: center;
  }

  .icon-yunhang {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    color: #fff;
    font-size: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9;
  }
  .small-view-opreations {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 0;
    background: #F3F6FF;
    border-radius: 0px 0px 4px 4px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 0 20px;
    transition: height .5s;
    i {
      font-size: 16px;
      color: #2859FF;
      cursor: pointer;
      display: none;
      transition: all .5s .5s;
      line-height: 1;
    }
  }
}