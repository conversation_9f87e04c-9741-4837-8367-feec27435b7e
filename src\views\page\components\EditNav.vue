<template>
  <div class="info-add">
    <div class="content-box">
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px">
        <el-form-item label="外链地址" prop="url">
          <el-input v-model="formData.url" placeholder="请输入外链地址" />
        </el-form-item>
      </el-form>
      <div class="flex-end">
        <jzt-button class="active width120" name="保存" @click="handleSave" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import { getNavigationDetailApi, editNavApi } from '@/api/navigation'

const props = defineProps({
  id: {
    type: String,
    default: ''
  }
})

const formRef = ref()
const formData = ref({
  id: '',
  url: ''
})
const rules = ref({
  url: [{ required: true, message: '请输入外链地址', trigger: 'blur' }]
})

const handleSave = () => {
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      let loadingInstance = ElLoading.service({
        lock: true,
        text: 'Loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      editNavApi(formData.value).then((res: any) => {
        ElMessage.success('保存成功')
      }).finally(() => {
        loadingInstance.close()
      })
    }
  })
}

onMounted(() => {
  let id = Number(props.id)
  if (!id) return
  getNavigationDetailApi(id).then((res: any) => {
    console.log(res, 'res')
    formData.value.id = res.id
    formData.value.url = res.url
  })
})

</script>

<style scoped lang="scss">

</style>