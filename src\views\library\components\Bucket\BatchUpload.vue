<template>
  <edit-dialog ref="editDialogRef" v-model:dialogShow="dialogShow" v-bind="formConfig">
    <div class="batch-upload">
      <div class="batch-upload-body">
        <div class="operating-area">
          <el-form :inline="true" :model="batchUploadSettings" class="bucket-settings">
            <div>
              <el-form-item class="mb0" label="比例：">
                <el-input-number v-model="batchUploadSettings.x" placeholder="1" type="number" :min="1" controls-position="right" size="small" />
                <el-input-number v-model="batchUploadSettings.y" placeholder="1" type="number" :min="1" controls-position="right" size="small" />
              </el-form-item>
              <el-form-item class="mb0" label="">
                <el-switch
                  v-model="batchUploadSettings.fillMode"
                  inline-prompt
                  active-text="切割"
                  inactive-text="平铺"
                  style="
                    --el-switch-on-color: #2859ff;
                    --el-switch-off-color: #7f8294;
                  "
                  :active-value="1"
                  :inactive-value="2"
                  size="large"
                  :disabled="
                    tableData.length === 0 ||
                    !batchUploadSettings.x ||
                    !batchUploadSettings.y
                  "
                />
              </el-form-item>
              <el-form-item class="mb0" label="">
                <el-button
                  type="primary"
                  :disabled="
                    tableData.length === 0 ||
                    !batchUploadSettings.x ||
                    !batchUploadSettings.y
                  "
                  style="
                    --el-button-bg-color: #2859ff;
                    --el-button-border-color: #2859ff;
                  "
                  @click="cut"
                  >裁剪</el-button
                >
              </el-form-item>
            </div>
            <div>
              <el-form-item class="upload-btn-wrap" label="">
                <el-button
                  type="primary"
                  @click="selectImages"
                  style="
                    --el-button-bg-color: #2859ff;
                    --el-button-border-color: #2859ff;
                  "
                  >添加多文件</el-button
                >
                <input
                  ref="uploadBtnRef"
                  type="file"
                  class="el-button--primary"
                  id="testList"
                  style="display: none"
                  multiple
                  accept="image/*"
                  @change="getImages"
                />
              </el-form-item>
            </div>
          </el-form>
        </div>
        <div class="batch-list">
          <el-table
            :data="tableData"
            style="width: 100%"
            header-row-class-name="tbHeader"
            :empty-text="'暂无数据'"
          >
            <el-table-column prop="base64" label="图片预览" width="140">
              <template #default="{ row }">
                <el-image :src="row.base64" style="width: 100px; height: 100px;" fit="scale-down" :preview-src-list="[row.base64]" :preview-teleported="true" />
              </template>
            </el-table-column>
            <el-table-column prop="name" label="名称" />
            <el-table-column prop="size" label="大小">
              <template #default="{ row }">
                {{ formatByteSize(row.size) }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="170">
              <template #default="scope">
                <span :style="getColor(scope.row.status)">{{
                  getStatus(scope.row.status)
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
              <template #default="scope">
                <el-button type="primary" class="is-text" @click="handleDelete(scope)">
                  <i class="iconfont icon-shanchu1"></i>
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="btn-box">
          <el-button
            type="primary"
            @click="batchUploadSubmit"
            :disabled="tableData.length === 0"
            >添加</el-button
          >
          <el-button type="default" @click="closeBatchUpload">取消</el-button>
        </div>
      </div>
    </div>
  </edit-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useDialog } from '@/hooks/useDialog'
import { formatByteSize, base64ToFile, isVerticalImage } from '@/utils'
import { uploadBatchImgApi } from '@/api/upload'

const { EditDialog, editDialogRef, dialogShow, dialogData, openDialog, closeDialog } = useDialog()

const props = defineProps({
  folder: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['success'])

const formConfig = ref({
  title: '批量添加',
  width: '880px',
  customForm: true,
  showBottomBtn: false
})

const batchUploadSettings = ref({
  x: null,
  y: null,
  fillMode: 1,
  fileList: []
})

const uploadBtnRef = ref()
const tableData = ref<any[]>([])

// 裁剪
const cutting = ref(false)
const cut = async () => {
  console.log('cut')
  let { x, y } = batchUploadSettings.value
  if (!x || !y) {
    ElMessage.error('请输入裁剪比例')
    return
  }
  cutting.value = true
  let list = tableData.value.filter((item: any) => item.status !== 6)
  for (let i = 0; i < list.length; i++) {
    console.log('list[i]', list[i])
    let file = list[i]
    try {
      list[i].status = 7
      let res: any = await cutImage(file)
      console.log('res', res)
      list[i].orgData = res.file
      list[i].base64 = res.base64
      list[i].status = 3
    } catch (error: any) {
      console.log('error', error)
      list[i].status = 4
    }
    if (i === list.length - 1) {
      cutting.value = false
    }
  }
}

// 裁剪图片
const cutImage = (file: any) => {
  return new Promise((resolve, reject) => {
    // 按比例裁剪图片
    let { x: xProportion, y: yProportion, fillMode } = batchUploadSettings.value
    if (!xProportion || !yProportion) {
      reject(new Error('未设置裁剪比例'))
      return
    }
    // fileMode 1: 切割 2: 平铺
    let img = new Image()
    img.src = file.oldBase64
    img.onload = () => {
      console.log('img', img)
      let { width: imgWidth, height: imgHeight } = img

      // 如果输入的比例和图片比例一致 直接返回
      if (imgWidth / imgHeight == xProportion / yProportion) {
        resolve({ base64: file.oldBase64, file: file.orgData })
        return
      }

      // 裁剪图片
      let canvas = document.createElement('canvas')
      let ctx = canvas.getContext('2d')
      if (!ctx) {
        reject(new Error('绘制失败'))
        return
      }

      let setProportion = xProportion / yProportion
      let canvasX = 0, canvasY = 0

      canvas.width = imgWidth
      canvas.height = imgWidth / setProportion
      canvasX = 0
      if (fillMode == 2) {
        canvasY = (canvas.height - imgHeight) / 2
      } else {
        canvasY = (imgHeight - canvas.height) / 2
      }
      if (canvasY < 0) {
        canvas.height = imgHeight
        canvas.width = imgHeight * setProportion
        if (fillMode == 2) {
          canvasX = (canvas.width - imgWidth) / 2
        } else {
          canvasX = (imgWidth - canvas.width) / 2
        }
        canvasY = 0
      }
      
      if (fillMode == 2) {
        // 平铺 空白填充白色
        ctx.fillStyle = '#fff'
        ctx.fillRect(0, 0, canvas.width, canvas.height)
        ctx.stroke()
        ctx.drawImage(img, canvasX, canvasY, imgWidth, imgHeight)
      } else {
        // 切割
        ctx.drawImage(img, canvasX, canvasY, canvas.width, canvas.height, 0, 0, canvas.width, canvas.height)
      }
      // 获取裁剪后的图片 并转file
      let base64 = canvas.toDataURL('image/jpeg', 0.8)
      // 转file
      let _file = base64ToFile(base64, file.name)
      console.log('_file', _file)
      resolve({base64, file: _file})
    }
  })
}

// 选择图片
const selectImages = () => {
  uploadBtnRef.value.click()
}

// 获取图片
const getImages = (e: any) => {
  console.log('getImages', e)
  let files = e.target.files, oldDataLength = 0;
  oldDataLength = files.length;

  // 过滤不是图片的文件
  files = Array.from(files).filter((file: any) => file.type.indexOf('image') !== -1)
  if (files.length < oldDataLength) {
    ElMessage.info(`以为您过滤掉${(oldDataLength - files.length)}个非图片文件`);
  }

  for (let i = 0; i < files.length; i++) {
    let value = files[i];
    var imgFile = new FileReader();
    imgFile.readAsDataURL(value);
    imgFile.onload = function () {
      // 大于5M 不提示
      let status = 0
      if (value.size > 5 * 1024 * 1024) status = 6
      tableData.value.push({
        name: value.name,
        size: value.size,
        status: status,
        orgData: value,
        base64: this.result,
        oldBase64: this.result,
        oldData: value
      });
    };
  }
}

const getStatus = (data: number) => {
  let obj: any = {
    0: "等待上传",
    1: "上传成功",
    2: "上传失败",
    3: "裁剪成功，等待上传",
    4: "裁剪失败",
    5: "上传中",
    6: "图片大小不能超过5M",
    7: "裁剪中"
  };
  return obj[data];
};

const getColor = (data: number) => {
  let obj: any = {
    0: "color:#999",
    1: "color:#67C23A",
    2: "color:#F56C6C",
    3: "color:#67C23A",
    4: "color:#F56C6C",
    5: "color:#2859ff",
    6: "color:#F56C6C",
    7: "color:#2859ff"
  };
  return obj[data];
};

// 删除图片
const handleDelete = (scope: any) => {
  console.log('handleDelete', scope)
  const index = scope.$index
  ElMessageBox.confirm('确定要删除吗？', '提示').then(() => {
    tableData.value.splice(index, 1)
  }).catch((error: any) => {
    console.log('error', error)
  })
}

// 关闭批量上传
const closeBatchUpload = () => {
  tableData.value = []
  uploadBtnRef.value.value = ''
  closeDialog()
}

// 点击上传
const submitting = ref(false)
const batchUploadSubmit = async () => {
  console.log('submit')
  if (cutting.value) {
    ElMessage.warning('裁剪中，请稍后再试')
    return
  }
  if (submitting.value) {
    ElMessage.warning('提交中，请稍后再试')
    return
  }
  submitting.value = true
  let list = tableData.value.filter((item: any) => item.status !== 6)
  for (let i = 0; i < list.length; i++) {
   try {
      list[i].status = 5
      await uploadImage(list[i].orgData)
      list[i].status = 1
    } catch (error: any) {
      console.log('error', error)
      list[i].status = 2
    }
    if (i === list.length - 1) {
      closeBatchUpload()
      emit('success')
      submitting.value = false
    }
  }
}

// 上传图片
const uploadImage = (file: any) => {
  return new Promise((resolve, reject) => {
    console.log('uploadImage', file)
    let formData = new FormData()
    formData.append('type', 'img')
    formData.append('file', file)
    formData.append('file_key', 'file')
    formData.append('classid', '1')
    formData.append('f_id', props.folder.toString())
    uploadBatchImgApi(formData).then((res: any) => {
      console.log('res', res)
      resolve(res)
    }).catch((error: any) => {
      console.log('error', error)
      reject(error)
    })
  })
}

defineExpose({ openDialog })
</script>

<style scoped lang="scss">
.batch-upload {
  :deep(.el-button:not(.is-text)) {
    background-color: #ffffff;
    border: 1px solid #a0b6ff;
    &:hover,
    &:focus {
      background-color: #fff;
      border-color: #4770fa;
      color: #4770fa;
    }
    &.is-disabled {
      background-color: #7897fd !important;
      border-color: #7897fd !important;
    }
  }
  :deep(.el-button.el-button--primary:not(.is-text)) {
    background-color: #2859ff;
    border-color: #2859ff;
    color: #fff;
    &:hover,
    &:focus {
      background-color: #4770fa;
      border-color: #4770fa;
    }
  }
  .upload-btn-wrap {
    margin-right: 0;
    margin-bottom: 0;
  }
  h4 {
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: bold;
    color: #1c2b4b;
    text-align: center;
    margin-bottom: 20px;
  }
  .close {
    position: absolute;
    top: 15px;
    right: 15px;
  }
  .batch-upload-body {
    .bucket-settings {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    :deep(.el-input-number) {
      width: 50px;
      .el-input-number__increase,
      .el-input-number__decrease {
        width: 14px;
        height: 50%;
      }
      &:first-child {
        margin-right: 6px;
      }
      .el-input__wrapper,
      .el-input__inner {
        padding-left: 0;
        padding-right: 14px;
        height: 100%;
      }
    }
    :deep(.el-input--small) {
      height: 28px;
      width: 100%;
    }
    :deep(.is-text) {
      &.is-show {
        color: #fff;
      }
    }
    .batch-list {
      border: 1px solid #e3e5ec;
      margin-top: 16px;
      margin-bottom: 20px;
      height: 420px;
      overflow: auto;
      :deep(.tbHeader th) {
        background-color: #f6f8fb;
      }
      :deep(.el-button.is-text) {
        color: #636ea7;
        background-color: transparent;
        border: none;
        &:hover,
        &:focus {
          color: #8893cb;
        }
        i {
          margin-right: 6px;
        }
      }
    }
  }
  .btn-box {
    width: 100%;
    text-align: center;
    :deep(.el-button) {
      width: 110px;
      height: 34px;
      border-radius: 3px;
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 500;
      color: #2859ff;
    }
  }
}
</style>