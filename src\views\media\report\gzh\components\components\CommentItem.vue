<script setup lang="ts">
import {ref} from "vue";
import {deleteCommentApi, replyCommentApi} from "@/api/report/imgText";
import {ElMessage, ElMessageBox} from "element-plus";

const props = defineProps<{
  item: any;
  msgId: string;
}>();

const emits = defineEmits(['reply']);

const loading = ref(false);

const reply = async (item: any) => {
  const params = {
    msgid: props.msgId,
    content: item.reply.replyContent,
    user_comment_id: item.user_comment_id
  };
  loading.value = true;
  try{
    await replyCommentApi(params);
    ElMessage.success('回复成功');
    emits('reply');
  }catch (e) {
    console.log(e)
  }finally {
    loading.value = false;
  }
};

const del = async (item: any, type: 'del' | 'dele') => {
  ElMessageBox.confirm('是否删除该评论', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    loading.value = true;
    try{
      await deleteCommentApi({msgid: props.msgId, user_comment_id: item.user_comment_id, datatype: type});
      ElMessage.success('删除成功');
      emits('reply');
    }catch (e) {
      console.log(e)
    }finally {
      loading.value = false;
    }
  }).catch(() => {
    console.log('取消删除');
  });
};
</script>

<template>
  <div class="comment-item flex" v-loading="loading">
    <div class="avatar">
      <el-avatar shape="square" :size="40" :src="item.headimgurl"/>
    </div>
    <div class="info-box flex-1">
      <p class="i-name">{{ item.nickname }}</p>
      <p class="i-c">{{ item.content }}</p>
      <div class="i-time-box flex">
        <span class="i-time">{{ item.create_time }}</span>
        <el-button link type="primary" class="reply-btn">回复</el-button>
        <el-button link type="danger" @click="del(item, 'del')">删除</el-button>
      </div>
      <div class="replay-box mt-20" v-if="item.reply">
        <div class="c-item flex">
          <div class="avatar"></div>
          <div class="info-box flex-1">
            <p class="i-name">{{ item.reply.nickname }}</p>
            <p class="i-c">{{ item.reply.content }}</p>
            <div class="i-time-box flex">
              <span class="i-time">{{ item.reply.create_time }}</span>
              <el-button link type="primary" class="reply-btn">回复</el-button>
              <el-button link type="danger" @click="del(item, 'dele')">删除</el-button>
            </div>
          </div>
        </div>
      </div>
      <div class="writ-box mt-20 flex">
        <el-input v-model="item.reply.replyContent" type="textarea" :rows="{min: 2, max: 2}" placeholder="回复评论" class="replay-input flex-1" :resizable="false"/>
        <el-button type="primary" class="reply-btn" @click="reply(item)">发表</el-button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.mt-20 {
  margin-top: 20px;
}
.comment-item {
  align-items: flex-start;
  border-bottom: 1px #F0F2F5 solid;
  padding-top: 20px;
  padding-bottom: 20px;
  &:last-child {
    border-bottom: none;
  }
  .avatar {
    width: 40px;
    margin-right: 13px;
    flex-shrink: 0;
    :deep(.el-avatar) {
      border-radius: 4px;
      background-color: transparent;
    }
  }
  .info-box {
    .i-name {
      font-size: 14px;
      font-family: PingFang;
      font-weight: 500;
      color: #999999;
      line-height: 1;
      margin-bottom: 8px;
    }
    .i-c {
      font-size: 14px;
      font-family: PingFang;
      font-weight: 500;
      color: #333333;
      line-height: 20px;
      margin-bottom: 8px;
    }
    .i-time-box {
      font-size: 14px;
      font-family: PingFang;
      font-weight: 500;
      .i-time {
        font-size: 14px;
        margin-right: 29px;
        color: #999999;
      }
      .reply-btn:hover {
        color: var(--jzt-color-main);
      }
    }
    .replay-box, .writ-box {
      background: #F9F9F9;
      border-radius: 4px;
      padding: 0 20px;
      .c-item {
        align-items: flex-start;
        border-bottom: 1px #F0F2F5 solid;
        padding-top: 20px;
        padding-bottom: 20px;
        &:last-child {
          border-bottom: none;
        }
      }
    }
    .writ-box {
      background: #fff;
      border: 1px solid #DEDEDE;
      padding: 10px;
      align-items: end;
      .replay-input {
        :deep(.el-textarea__inner) {
          background: transparent;
          box-shadow: none;
          resize: none;
          padding: 0;
        }
      }
      .reply-btn{
        width: 60px;
        height: 26px;
        line-height: 26px;
        text-align: center;
        background: #3583FB;
        border-radius: 13px;
        font-size: 14px;
        font-family: PingFang;
        font-weight: 500;
        color: #FFFFFF;
      }
    }
  }
}
</style>
