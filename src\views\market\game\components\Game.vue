<script setup lang="ts">
import {ref} from "vue";
import {getCompanyInfoApi, getGameTypeApi} from "@/api/game";
import DataPage from "@/components/Global/DataPage.vue";
import UpdateGame from "@/views/market/game/components/list/UpdateGame.vue";

const emits = defineEmits(['changeNav']);

/*列表*/
const gameList: any = ref([]);
const params = ref({
  page: 1,
  limit: 40
})
const total = ref(0);
const lPage = ref(1);
const loading = ref(false);
const getGameList = async () => {
  try{
    loading.value = true;
    const res:any = await getGameTypeApi(params.value);
    gameList.value = res.data;
    total.value = res.total;
    lPage.value = res.last_page;
  }finally {
    loading.value = false;
  }
};
getGameList();

/*获取公司和域名信息*/
const companyAndDomain = ref({
  company_name: '',
  domain: ''
});
const getCompanyAndDomain = async () => {
  try{
    const res:any = await getCompanyInfoApi();
    companyAndDomain.value.company_name = res.company_name;
    companyAndDomain.value.domain = res.site_domain;
  }catch (e){
    console.log(e);
  }
};
getCompanyAndDomain();

/*编辑*/
const updateInfo = ref({
  title: '添加小游戏',
  show: false,
  data: null
});
const add = (item: any) => {
  updateInfo.value.show = true;
  updateInfo.value.title = '添加小程序';
  const data = JSON.parse(JSON.stringify(item))
  delete data.id;
  delete data.game_url;
  delete data.icon;
  updateInfo.value.data = {...data, ...companyAndDomain.value, game_id: item.id, status: 1, phone: '', share_title: '', share_desc: '', share_img: ''};
};
const success = () => {
  emits('changeNav', 0);
  updateInfo.value.show = false;
  updateInfo.value.data = null;
};
</script>

<template>
  <div class="wrap h100">
    <div class="list-wrap w100">
      <div class="list flex flex-wrap w100">
        <div v-for="item in gameList" :key="item.id" class="item flex-between" @click="add(item)">
          <div class="icon-text flex">
            <img :src="item.icon" alt="" class="icon">
            <span>{{ item.title }}</span>
          </div>
          <i class="iconfont icon-tianjia"></i>
        </div>
      </div>
    </div>
    <div class="page" v-if="lPage > 1">
      <data-page v-model:page="params.page" :total="total" :totalPage="lPage" @pagination="getGameList"></data-page>
    </div>

    <!--编辑-->
    <update-game :title="updateInfo.title" v-model:dialog-show="updateInfo.show" :data="updateInfo.data" v-if="updateInfo.data" @success="success" />
  </div>
</template>

<style scoped lang="scss">
.wrap {
  .list-wrap {
    height: calc(100% - 62px);
    overflow: auto;
    padding: 10px 14px;
    .item {
      width: 176px;
      height: 80px;
      background: #FFFFFF;
      box-shadow: 0px 6px 12px 0px rgba(216, 224, 234, 0.2);
      margin-right: 6px;
      margin-bottom: 6px;
      padding: 0 16px;
      box-sizing: border-box;
      cursor: pointer;
      img {
        max-width: 34%;
      }
      span {
        font-size: 14px;
        color: #696C7D;
        margin-left: 8px;
      }
    }
  }
}
</style>
