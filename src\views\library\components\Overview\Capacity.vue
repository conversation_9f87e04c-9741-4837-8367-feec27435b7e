<template>
  <div ref="chart" style="width: 100%; height: 100%;"></div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import * as echarts from 'echarts';
import { getSpacePercentApi } from '@/api/bucket';

const chart = ref<HTMLDivElement | null>(null);

const spaceData = ref<any>({})
const getSpacePercent = async () => {
  const res:any = await getSpacePercentApi()
  spaceData.value = res
  initChart()
}

const initChart = () => {
  if (chart.value) {
    const myChart = echarts.init(chart.value);
    let source = spaceData.value.buck_count || '0MB';
    let count = spaceData.value.count || '0MB';
    let joinStr = '';
    let top = '66%';
    const option = {
      backgroundColor: '#fff',
      title: [
        {
          text: '空间容量',
          left: '50%',
          top: '31',
          textAlign: 'center',
          textStyle: {
            fontSize: '20',
            fontWeight: 'bold',
            color: '#1C2B4B',
            textAlign: 'center',
          },
        },
        {
          text: spaceData.value.buck_bfb + '%',
          left: '50%',
          top: '50%',
          textAlign: 'center',
          textStyle: {
            fontSize: '28',
            fontWeight: 'bold',
            color: '#1C2B4B',
            textAlign: 'center',
          },
        },
        {
          text: ['{a|' + source + '}', '{b| / ' + count + '}'].join(joinStr),
          left: '46%',
          top: top,
          textAlign: 'center',
          textStyle: {
            rich: {
              a: {
                fontSize: '16',
                fontWeight: '400',
                color: '#3765FF',
                lineHeight: 20,
              },
              b: {
                fontSize: '16',
                fontWeight: '400',
                color: '#696C7D',
                lineHeight: 20,
              }
            }
          },
        }
      ],
      polar: {
        radius: ['62%', '72%'],
        center: ['50%', '60%'],
      },
      angleAxis: {
        max: 100,
        clockwise: true,
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
        splitLine: {
          show: false,
        },
      },
      radiusAxis: {
        type: 'category',
        show: true,
        axisLabel: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
      },
      series: [
        {
          name: '',
          type: 'bar',
          roundCap: true,
          z: 2,
          showBackground: true,
          backgroundStyle: {
            color: '#E6ECF5',
          },
          data: [spaceData.value.buck_bfb],
          coordinateSystem: 'polar',
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 180, .9, 1, [
                {
                  offset: 1,
                  color: '#3866FF',
                },
                {
                  offset: 1,
                  color: '#3866FF',
                },
                {
                  offset: 1,
                  color: '#3866FF',
                },
              ]),
            },
          },
        },
      ],
    };
    myChart.setOption(option);
  }
}

onMounted(async () => {
  getSpacePercent()
})
</script>

<style scoped lang="scss">
/* 添加样式以适应你的设计 */
</style>
