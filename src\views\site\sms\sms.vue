<template>
  <div class="h100 flex-y">
    <page-menu v-if="navList.length" :list="navList" @click-item="navItemClick" :activeIndex="activeIndex" />
    <div class="flex-1">
      <component ref="currentComponentRef" :is="currentComponent" :cate-type="cateType" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import {computed, defineAsyncComponent, markRaw, ref} from "vue";

const navList = markRaw([
  { title: '短信列表', key: 'list', component: defineAsyncComponent(() => import('@/views/site/sms/components/List.vue')) },
])

const activeIndex = ref(0)

const navItemClick = (item: any, index: number) => {
  activeIndex.value = index
}

const currentComponent = computed(() => {
  return navList[activeIndex.value].component
})

const cateType = computed(() => { return navList[activeIndex.value].key })
</script>

<style lang="scss" scoped>

</style>
