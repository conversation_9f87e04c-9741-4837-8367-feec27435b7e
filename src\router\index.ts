import { Router, createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router'
import NProgress from "@/utils/nprogress";
import Layout from '@/layout/index.vue';
import RouterViewWrap from '@/components/RouterViewWrap.vue';
import store from '@/store';
import { initRouter } from './utils'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'home',
    component: Layout,
    // redirect: '/navigation',
    children: [
      {
        path: '/',
        name: 'index',
        component: () => import('@/layout/components/RouterView.vue'),
        meta: {
          title: '网站资料',
          isNav: true,
          noJump: true, // 禁止跳转
        },
        children: [
          {
            path: '/page',
            name: 'page',
            component: () => import('@/views/page/index.vue'),
            meta: {
              title: '页面',
              isNav: false
            }
          }
        ]
      },
      // 高级设置
      {
        path: '/setting',
        name: 'setting',
        redirect: '/navigation',
        component: RouterViewWrap,
        meta: {
          title: '高级设置',
          isNav: true,
          noJump: true, // 禁止跳转
        },
        children: [
          {
            path: '/navigation',
            name: 'navigation',
            component: () => import('@/views/navigation/index.vue'),
            meta: {
              title: '修改导航',
              isNav: true
            }
          },
          { // 信息管理
            path: '/setting/text',
            name: 'text',
            component: () => import('@/views/content/text.vue'),
            meta: {
              title: '信息管理',
              isNav: false
            }
          },
          { // 轮播管理
            path: '/setting/banner',
            name: 'banner',
            component: () => import('@/views/content/banner.vue'),
            meta: {
              title: '轮播管理',
              isNav: true
            }
          },
          { // 商品管理
            path: '/setting/shop',
            name: 'shop',
            component: () => import('@/views/content/shop.vue'),
            meta: {
              title: '商品管理',
              isNav: true
            }
          },
          { // 公告管理
            path: '/setting/notice',
            name: 'notice',
            component: () => import('@/views/content/notice.vue'),
            meta: {
              title: '公告管理',
              isNav: false
            }
          },
          { // 活动管理
            path: '/setting/activity',
            name: 'activity',
            component: () => import('@/views/content/activity.vue'),
            meta: {
              title: '活动管理',
              isNav: false
            }
          },
          { // 互动管理
            path: '/setting/interaction',
            name: 'interaction',
            component: () => import('@/views/content/consult/consult.vue'),
            meta: {
              title: '三方管理',
              isNav: true
            }
          },
          { // 招聘管理
            path: '/setting/recruitment',
            name: 'recruitment',
            component: () => import('@/views/content/recruitment.vue'),
            meta: {
              title: '招聘管理',
              isNav: true
            }
          },
          { // 广告管理
            path: '/setting/advertisement',
            name: 'advertisement',
            component: () => import('@/views/content/advertisement.vue'),
            meta: {
              title: '广告管理',
              isNav: true
            }
          },
          { // 图片管理
            path: '/setting/image',
            name: 'image',
            component: () => import('@/views/content/image.vue'),
            meta: {
              title: '图片管理',
              isNav: true
            }
          },
          {
            path: '/setting/advanced',
            name: 'advanced',
            component: () => import('@/views/website/index.vue'),
            meta: {
              title: '高级编辑',
              isNav: true,
              tips: '(建议专业设计师操作)'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/login/index.vue'),
    meta: {
      title: '登录'
    }
  }
]

// 获取路由信息
const getRoutes = async (to: any, store: any) => {
  const navigationList = store.getters.navigationList;
  const leftMenuList = store.getters.leftMenuList;
  if (!leftMenuList.length) {
    // console.log(router, 'router')
    const result: any = await store.dispatch('app/getNavigationList', routes)
    // console.log(result, 'result')
    firstEnter(to, store)
  } else {
    firstEnter(to, store)
  }
}

// 首次进入路由跳转
const firstEnter = (to: any, store: any) => {
  const navigationList = store.getters.navigationList;
  const leftMenuList = store.getters.leftMenuList
  if (leftMenuList.length && (to.path === '/' || !navigationList.length)) {
    let v = navigationList[0]
    if (v.path) router.push(v.path)
    else if (v.id) router.push({ path: '/page', query: { jwp_id: v.jwp_id, id: v.id, title: v.title, type: v.type } })
    else router.push('/page')
  } else {
    router.push(to.fullPath)
  }
}

// 处理路由
const handleRoutes = (to: any, next: any, store: any) => {
  const navigationList = store.getters.navigationList;
  if (to.path === '/') {
    getRoutes(to, store)
  } else {
    next()
  }
}


export const router: Router = createRouter({
  history: createWebHashHistory(),
  routes
})

router.beforeEach((to: any, from, next) => {
  const siteInfo = store.getters.siteInfo;
  NProgress.start()
  // 设置标题
  document.title = `${to.meta.title ? to.meta.title + '-' : ''}${siteInfo && siteInfo.web_name || '建站通'}`
  if (!siteInfo && to.path!== '/login') {
    store.dispatch('site/getSiteInfo').then(() => {
      getRoutes(to, store)
    })
  } else handleRoutes(to, next, store)
})

router.afterEach(() => {
  NProgress.done()
})

export default router
