import http from '@/utils/request'

// 站点列表
export const getSiteListApi = (params?: any) => {
  return http.request({
    url: '/site/lst',
    method: 'get',
    params
  })
}

// 站点详情
export const getSiteInfoApi = (params?: any) => {
  return http.request({
    url: '/site/info',
    method: 'get',
    params
  })
}

// 切换站点
export const changeSiteApi = (params?: any) => {
  return http.request({
    url: '/site/change',
    method: 'post',
    data: params
  })
}

/* 控制台相关 */
// 上次登录时间
export const lastTime = (params?: object) => {
  return http.request({
    url: '/Space/expiRation',
    method: 'post',
    data: params
  })
};
// 最近使用
export const latelyUse = (params?: object) => {
  return http.request({
    url: '/SiteMenuLog/getList',
    method: 'post',
    data: params
  })
};
// 常用操作
export const commonUse = (params?: object) => {
  return http.request({
    url: '/SiteMenuLog/Common_menu',
    method: 'post',
    data: params
  })
};
// 网站使用报告
export const siteReport = (params?: object) => {
  return http.request({
    url: '/SiteMenuLog/tjList',
    method: 'post',
    data: params
  })
};
// 网站使用量
export const siteUsage = (params?: object) => {
  return http.request({
    url: '/Space/Proportion',
    method: 'post',
    data: params
  })
};
// 网站访问统计
export const siteCount = (params?: object) => {
  return http.request({
    url: '/SiteMenuLog/statList',
    method: 'post',
    data: params
  })
};
// 各端二维码
export const siteQRCode = (params?: object) => {
  return http.request({
    url: '/Space/qrCode',
    method: 'post',
    data: params
  })
};

// 获取昨日数据
export const getYesterdayData = (params?: object) => {
  return http.request({
    url: '/SiteMenuLog/tjScreen',
    method: 'post',
    data: params
  })
};