<template>
  <div class="h100 flex-y">
    <page-menu v-if="navList.length" :list="navList" @click-item="navItemClick" :activeIndex="activeIndex">
      <template #add>
        <div :style="{position: 'absolute', top: jumpObj.top + 'px', left: jumpObj.left + 'px', width: 'max-content'}" @click="jumpUrl(jumpObj.url)">
          <img :src="jumpObj.icon" alt="" />
        </div>
      </template>
    </page-menu>
    <div class="flex-1">
      <list-view v-show="activeIndex === 0" ref="listViewRef" :type="type" @changeNav="changeNav" />
      <send-video v-show="activeIndex === 1" ref="sendVideoRef" :type="type" @changeNav="changeNav" @success="refreshList" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineAsyncComponent, markRaw, computed } from 'vue'
import { useMedia } from '@/hooks/useMedia'

const { jumpUrl } = useMedia()

const ListView = markRaw(defineAsyncComponent(() => import('./List.vue')))
const SendVideo = markRaw(defineAsyncComponent(() => import('./SendVideo.vue')))

const listViewRef = ref()
const sendVideoRef = ref()

const props = defineProps({
  type: {
    type: String,
    default: 'video'
  }
})

const typeName = computed(() => {
  return props.type == 'video' ? '视频' : '图文'
})

const jumpObj = computed(() => {
  return props.type == 'video' ? {
    url: '/#/media/inspiration?type=video',
    icon: require('@/assets/images/media/icon09.png'),
    top: -4,
    left: 100
  } : {
    url: '/#/media/inspiration?type=article',
    icon: require('@/assets/images/media/icon08.png'),
    top: 2,
    left: 100
  }
})

const navList = ref([
  { title: `我的${typeName.value}`, key: 'list', component: ListView },
  { title: `发布${typeName.value}`, key: 'add', component: SendVideo, hidden: true }
])
const activeIndex = ref(0)

const navItemClick = (item: any, index: number, data: any) => {
  activeIndex.value = index
}

const rowData = ref(null)
const changeNav = (data:any) => {
  const { key, val, type } = data
  if (type) {
    if (val) rowData.value = val
    else rowData.value = null
    sendVideoRef.value.setData({type, data: rowData.value})
  }
  navList.value.forEach((item:any, index:number) => {
    if (item.key === key) {
      activeIndex.value = index
    }
  })
}

// 刷新列表
const refreshList = () => {
  listViewRef.value.refresh()
}

</script>

<style scoped lang="scss">

</style>