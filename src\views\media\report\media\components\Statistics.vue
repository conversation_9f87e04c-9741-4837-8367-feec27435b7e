<script setup lang="ts">
import {computed, ref} from "vue";
import StatisticsList from "@/views/media/report/components/StatisticsList.vue";
import {getMediaArticleApi, getMediaBindApi, getMediaStatisticsApi, getMediaVideoApi} from "@/api/report/media";
import SyncStatistics from "@/views/media/report/media/components/SyncStatistics.vue";

/*顶部统计*/
const statistics = ref([
  {icon: require('@/assets/images/report/1.png'), title: '媒体管理点击次数', value: 0},
  {icon: require('@/assets/images/report/2.png'), title: '媒体管理今日点击次数', value: 0},
  {icon: require('@/assets/images/report/3.png'), title: '文章同步成功次数', value: 0},
  {icon: require('@/assets/images/report/4.png'), title: '视频同步成功次数', value: 0},
]);
const getStatistics = async () => {
  const res: any = await getMediaStatisticsApi();
  statistics.value[0].value = res.media_num;
  statistics.value[1].value = res.today_media_num;
  statistics.value[2].value = res.content_success;
  statistics.value[3].value = res.video_success;
};
getStatistics();

/*账号绑定数*/
const accountView = ref<{ [key: string]: { icon: string, title: string, value: number, unit: string } }>({
  wechat: {icon: require('@/assets/images/report/wx.png'), title: '微信公众号', value: 0, unit: '个'},
  baijiahao: {icon: require('@/assets/images/report/bjh.png'), title: '百家号', value: 0, unit: '个'},
  douyin: {icon: require('@/assets/images/report/dy.png'), title: '抖音', value: 0, unit: '个'},
  kuaishou: {icon: require('@/assets/images/report/ks.png'), title: '快手', value: 0, unit: '个'},
  bilibili: {icon: require('@/assets/images/report/bili.png'), title: 'bilibili', value: 0, unit: '个'},
});
const getAccountView = async () => {
  const res: any = await getMediaBindApi();
  Object.keys(res).forEach(key => accountView.value[key].value = res[key].today_count);
};
const accountViewList = computed(() => Object.values(accountView.value));
getAccountView();

/*文章绑定数*/
const articleView = ref<{ count: number, success: number, fail: number, info?: any }>({
  count: 0,
  success: 0,
  fail: 0,
});
const getArticleView = async () => {
  const res: any = await getMediaArticleApi()
  Object.assign(articleView.value, {...res, ...{count: res.content_count, success: res.content_success, fail: res.content_error}});
};
getArticleView();
const articleList = computed(() => {
  const titleMap = {
    wechat: {title: '公众号同步次数', icon: require('@/assets/images/report/wx.png'), value: 0},
    baijiahao: {title: '百家号同步次数', icon: require('@/assets/images/report/bjh.png'), value: 0},
    yjl: {title: '云经理同步次数', icon: require('@/assets/images/report/yjl.png'), value: 0},
    ymp: {title: '云名片同步次数', icon: require('@/assets/images/report/yjl.png'), value: 0},
  }
  return articleView.value.info ? Object.entries(titleMap).map(([key, value]) => ({
    ...value,
    value: articleView.value.info[key]?.count || 0,
    success: articleView.value.info[key]?.success || 0,
    fail: articleView.value.info[key]?.error || 0,
  })) : []
});

/*视频绑定数*/
const videoView = ref<{ count: number, success: number, fail: number, info?: any }>({
  count: 0,
  success: 0,
  fail: 0,
});
const getVideoView = async () => {
  const res: any = await getMediaVideoApi()
  Object.assign(videoView.value, {...res, ...{count: res.video_count, success: res.video_success, fail: res.video_error}});
};
getVideoView();
const videoList = computed(() => {
  const titleMap = {
    douyin: {title: '抖音同步次数', icon: require('@/assets/images/report/dy.png'), value: 0},
    kuaishou: {title: '云经理同步次数', icon: require('@/assets/images/report/ks.png'), value: 0},
    bilibili: {title: 'bilibili同步次数', icon: require('@/assets/images/report/bili.png'), value: 0},
  }
  return videoView.value.info? Object.entries(titleMap).map(([key, value]) => ({
    ...value,
    value: videoView.value.info[key]?.count || 0,
    success: videoView.value.info[key]?.success || 0,
    fail: videoView.value.info[key]?.error || 0,
  })) : []
});
</script>

<template>
  <div id="media-data-app" class="h100">
    <div class="statistics mt-10 white-bg">
      <statistics-list :data="statistics" :col-settings="{md: 6, sm: 8, xs: 24}" />
    </div>
    <div class="account-view mt-10 white-bg">
      <div class="title">账号绑定数</div>
      <div class="account-list">
        <statistics-list :data="accountViewList" :col-settings="{md: 5, sm: 8, xs: 24}" class-type="class-1"/>
      </div>
    </div>
    <sync-statistics name="文章同步数" title="文章同步次数" type="content" :icon="require('@/assets/images/report/5.png')" :view-info="articleView" :article-list="articleList"/>
    <sync-statistics name="视频同步数" title="视频同步次数" type="video" :icon="require('@/assets/images/report/6.png')" :view-info="videoView" :article-list="videoList" :col-settings="{md: 8, sm: 8, xs: 12}"/>
  </div>
</template>

<style scoped lang="scss">
.white-bg {
  background: #fff;
  margin-bottom: 10px;
  padding: 20px;
  box-sizing: border-box;
}
#media-data-app{
  padding: 0 15px;
  overflow: auto;
  .mt-10{
    margin-top: 10px;
  }
  .mt-20 {
    margin-top: 20px;
  }
  .statistics{
    padding-top: 25px;
    padding-bottom: 25px;
    :deep(.item){
      height: 63px;
      .i-title{
        font-size: 16px;
      }
      .number-unit{
        margin-top: 5px;
      }
    }
  }
  .title{
    font-weight: bold;
    font-size: 16px;
    color: #1c2b4b;
  }
  .account-list {
    padding: 16px 0;
    :deep(.item) {
      height: 80px;
    }
  }
}
</style>
