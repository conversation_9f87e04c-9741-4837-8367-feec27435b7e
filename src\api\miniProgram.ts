import http from '@/utils/request'

// 获取小程序页面信息
export const getWechatInfoApi = (params?: any) => {
  return http.request({
    url: '/wechat/getInfo',
    method: 'get',
    params
  })
}

// 获取小程序授权信息
export const getAuthApi = (url: string, params?: any) => {
  return http.request({
    // TODO 把接口换回来
    url: `/${url}/checkAuth`,
    // url: `http://localhost:8888/miniprogram/${url}AuthInfo`,
    method: 'get',
    params,
    ...{ hideToast: true, allResult: true }
  })
}

// 获取审核记录
export const getAuditRecordApi = (url: string, params?: any) => {
  return http.request({
    // TODO 把接口换回来
    url: `/${url}/content`,
    // url: `http://localhost:8888/miniprogram/${url}Content`,
    method: 'post',
    data: params
  })
}

// 获取二维码
export const getQrCodeApi = (url: string, params?: any) => {
  return http.request({
    // TODO 把接口换回来
    url: `/${url}/yulan`,
    // url: `http://localhost:8888/miniprogram/${url}QrCode`,
    method: 'get',
    params
  })
}

// 删除授权
export const deleteAuthApi = (url: string, params?: any) => {
  return http.request({
    // TODO 把接口换回来
    url: `/${url}/offline`,
    // url: `http://localhost:8888/miniprogram/${url}DeleteAuth`,
    method: 'post',
    data: params
  })
}

// 提交审核
export const auditApi = (url: string, params?: any) => {
  return http.request({
    // TODO 把接口换回来
    url: `/${url}/audit`,
    // url: `http://localhost:8888/miniprogram/${url}Audit`,
    method: 'post',
    data: params
  })
}

// 获取微信小程序模板
export const getWechatTemplateApi = (url: string, params?: any) => {
  return http.request({
    // TODO 把接口换回来
    url: `/${url}/publish_list`,
    // url: `http://localhost:8888/miniprogram/${url}Template`,
    method: 'post',
    data: params
  })
}

// 微信小程序重新提交审核
export const reAuditWechatApi = (url: string, params?: any) => {
  return http.request({
    // TODO 把接口换回来
    url: `/${url}/publish_again`,
    // url: `http://localhost:8888/miniprogram/${url}ReAudit`,
    method: 'post',
    data: params
  })
}

// 发布小程序
export const releaseApi = (url: string, params?: any) => {
  return http.request({
    // TODO 把接口换回来
    url: `/${url}/release`,
    // url: `http://localhost:8888/miniprogram/${url}Release`,
    method: 'post',
    data: params
  })
}

// 设置域名
export const setDomainApi = (url: string, params?: any) => {
  return http.request({
    // TODO 把接口换回来
    url: `/${url}/set_domain`,
    // url: `http://localhost:8888/miniprogram/${url}SetDomain`,
    method: 'post',
    data: params
  })
}

// 百度小程序提交代码
export const baiduSubmitCodeApi = (url: string, params?: any) => {
  return http.request({
    // TODO 把接口换回来
    url: `/${url}/submit_code`,
    // url: `http://localhost:8888/miniprogram/${url}SubmitCode`,
    method: 'post',
    data: params
  })
}

// 获取百度小程序审核信息
export const getBaiduAuditApi = (url: string, params?: any) => {
  return http.request({
    // TODO 把接口换回来
    url: `/${url}/submit_audit`,
    // url: `http://localhost:8888/miniprogram/${url}AuditInfo`,
    method: 'get',
    params
  })
}

// 提交百度小程序审核
export const submitBaiduAuditApi = (url: string, params?: any) => {
  return http.request({
    // TODO 把接口换回来
    url: `/${url}/submit_audit`,
    // url: `http://localhost:8888/miniprogram/${url}SubmitAudit`,
    method: 'post',
    data: params
  })
}

// 重新提交百度小程序审核
export const resubmitBaiduAuditApi = (url: string, params?: any) => {
  return http.request({
    // TODO 把接口换回来
    url: `/${url}/publishs`,
    // url: `http://localhost:8888/miniprogram/${url}ReSubmitAudit`,
    method: 'post',
    data: params
  })
}

// 获取支付宝小程序图片
export const getAlipayImgApi = (params?: any) => {
  return http.request({
    url: `/zhifubao/sel`,
    method: 'post',
    data: params
  })
}

// 保存支付宝小程序图片
export const saveAlipayImgApi = (params?: any) => {
  return http.request({
    url: `/zhifubao/edit`,
    method: 'post',
    data: params
  })
}

// 获取抖音、头条小程序图片
export const getDouyinImgApi = (params?: any) => {
  return http.request({
    url: `/ContentList/dyks_serch`,
    method: 'post',
    data: params
  })
}

// 保存抖音、头条小程序图片
export const saveDouyinImgApi = (params?: any) => {
  return http.request({
    url: `/ContentList/dyks_qrcode`,
    method: 'post',
    data: params
  })
}

// 获取快手小程序去授权跳转链接
export const getKuaishouAuthApi = (params?: any) => {
  return http.request({
    // TODO 把接口换回来
    url: `/kuaishou/get_authorization_url`,
    // url: `http://localhost:8888/miniprogram/kuaishouAuthInfo`,
    method: 'get',
    params,
    ...{ hideToast: true, allResult: true }
  })
}

// 快手小程序重新提交
export const resubmitKuaishouApi = (url: string, params?: any) => {
  return http.request({
    // TODO 把接口换回来
    url: `/${url}/publish`,
    // url: `http://localhost:8888/miniprogram/${url}ReSubmit`,
    method: 'post',
    data: params
  })
}
