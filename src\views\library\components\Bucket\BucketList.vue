<template>
  <div class="bucket-list-wrap flex-y h100">
    <!-- 文件夹层级 -->
    <div class="folder-level flex px10 mb10">
      <div v-if="folderList.length > 1" class="folder-level-item flex"><span @click="goBack"><i class="iconfont icon-fanhui1"></i> 返回上一级</span> |</div>
      <div class="folder-level-item flex" :class="{ active: currentFolder === item.id }" v-for="item in folderList" :key="item.id">
        <span @click="toSecondFiles(item)" v-html="item.name"></span> {{ currentFolder !== item.id ? '>' : '' }}
      </div>
    </div>
    <!-- 文件列表 -->
    <div class="file-list flex-1 flex-y">
      <div class="check-all">
        <el-checkbox v-model="isCheckAll" label="全选" @change="changeCheckAll" />
      </div>
      <div id="library-list-box" class="library-list-box flex-1" v-loading="loading">
        <template v-if="tableData.length > 0">
          <div class="library-list flex" id="library-list">
            <template v-for="(item, index) in tableData" :key="index">
              <div :class="['box_item', { active: item.isCheck }]" @click="checkItem(index, $event)">
                <div class="item-info-box">
                  <div class="item-check-box">
                    <el-checkbox name="type" v-model="item.isCheck" @change="checkItem(index, $event)" />
                  </div>
                  <div class="img-title img video-box">
                    <template v-if="item.isFile === 2">
                      <img v-if="item.type === 1" :src="item.url" @error="imgError($event, 'img')" />
                      <template v-else-if="item.type === 2">
                        <div v-if="getFileType(item.url) === 'audio'" class="audio-wrap h100"
                          :style="`position: relative; background-image: url(${require('@/assets/images/system/audio-error.jpg')}); height: 110px;`">
                          <i class="iconfont icon-yunhang"></i>
                          <audio :src="item.url"></audio>
                        </div>                      
                        <div v-else class="audio-wrap h100"
                          :style="!item.hidePoster && `background-image: url(${require('@/assets/images/system/video-error.jpg')})`">
                          <i class="iconfont icon-yunhang"></i>
                          <video :src="item.url" @canplay="item.hidePoster = true"></video>
                        </div>
                      </template>
                      <img v-else :src="getFileImg(item.url)" alt="" class="file">
                    </template>
                    <template v-else>
                      <img src="@/assets/images/bucket/folder1.png" class="folder-img" @click="toSecondFiles(item)">
                    </template>
                  </div>
                  <div class="small-view-opreations">
                    <template v-if="item.type === 2 && item.isFile === 2 && !isFree">
                      <i class="iconfont  operation-buttons toggle-online" :class="item.status == 1 ? 'icon-xiaxian': 'icon-shangxian'"
                        @click="onOffLine(item)"></i>
                    </template>
                    <i v-if="item.isFile == 2 && !isFree" class="iconfont icon-lianjie-" @click.stop="copyLink(item.url)"></i>
                    <i class="iconfont icon-bianji" @click.stop="editItem(item)"></i>
                    <i class="iconfont icon-shanchu1" @click.stop="deleteItem(item)"></i>
                  </div>
                </div>
                <div class="imgTitle">
                  <div v-if="item.edit" class="edit-title mt10">
                    <el-input @click.stop v-model="item.title" @blur="editTitle(item)" />
                  </div>
                  <template v-else>
                    <span class="node-title" v-html="item.title"></span>
                  </template>
                </div>
              </div>
            </template>
          </div>
        </template>
        <div class="none-data h100 flex-c-center" v-else>这里还没有素材</div>
      </div>
      <div v-if="total > 0" class="pagination flex-c-center">
        <data-page v-model:page="params.page" :total="total" :totalPage="lPage" @pagination="getFileList"></data-page>
      </div>
    </div>
    <preview
      :type="previewData.type"
      :url="previewData.url"
      v-if="previewData.show"
      @closePreview="closePreview"
    ></preview>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue'
import { useLibrary } from '@/hooks/useLibrary';

const emit = defineEmits(['folderChange', 'closeCate'])

const { imgError, getFileType, DataPage, isCheckAll, changeCheckAll, refresh,
  Preview, previewData, checkItem, closePreview, getFileImg, sizeFilter,
  getFileList, tableData, total, lPage, params, loading, checkedTableData,
  folderList, currentFolder, toSecondFiles, goBack, addBucketCate, 
  deleteBucket, deleteItem, isFree, onOffLine, copyLink, editItem, editTitle, transfer,
  download
} = useLibrary(emit)

const paramsTitleChange = (title: string) => {
  if (title == params.value.title) return
  params.value.title = title
  params.value.page = 1
  getFileList()
}

watch(previewData, (newVal) => {
  console.log(newVal, 'previewData')
})

onMounted(() => {
  getFileList()
})

defineExpose({ paramsTitleChange, refresh, addBucketCate, deleteBucket, toSecondFiles, checkedTableData, transfer, deleteItem, download })
</script>

<style lang="scss" scoped>
@import '@/assets/css/bucket.scss';
.bucket-list-wrap {
  .folder-level {
    .folder-level-item {
      font-size: 14px;
      color: #7F8294;
      cursor: pointer;
      span {
        color: #2859FF;
        margin: 0 10px;
        display: inline-block;
      }
      &.active {
        span {
          color: #7F8294;
          cursor: auto;
        }
      }
    }
  }
  .file-list {
    .check-all {
      background-color: #fff;
      border-left: none;
      border-right: none;
      padding: 0 10px;
    }
    .library-list-box {
      height: auto;
      background-color: #fff;
      border-left: none;
      border-right: none;
      .library-list {
        padding: 10px;
        .box_item {
          width: calc(100% / 8);
          height: 178px;
          margin-bottom: 10px;
        }
      }
    }
  }
}
</style>
