<template>
  <div class="flex-y h100">
    <data-list ref="dataListRef" :table-config="tableConfig" @clickBtn="dataAction" v-model:multipleSelection="tableConfig.multipleSelection">
      <template #cover="{ row }">
        <div class="cover-box width64 height40 mt8 mb8">
          <el-image class="w100 h100" :src="handleCover(row)" fit="cover" :preview-src-list="[handleCover(row)]" :preview-teleported="true">
          </el-image>
          <div v-if="type == 'video'" class="play-icon flex-c-center" @click="previewVideo(row.video)">
            <i class="iconfont icon-yunhang"></i>
          </div>
        </div>
      </template>
      <template #info="{ row }">
        <span v-if="row.sendinfo && row.sendinfo.length" class="text-main" @click="handleInfo(row)">{{row.info}}</span>
        <span v-else>未发布</span>
      </template>
      <template #action="{ row }">
        <div class="table-action flex">
          <div class="item" @click="editInfo(row)">
            <i class="iconfont icon-bianji"></i> <span>编辑</span>
          </div>
          <div class="item" @click="publishInfo(row)">
            <i class="iconfont icon-fabu"></i> <span>发布</span>
          </div>
          <div v-if="type == 'video'" class="item" @click="shareInfo(row.id)">
            <i class="iconfont icon-douyin"></i> <span>分享抖音</span>
          </div>
          <div class="item" @click="delInfo(row.id)">
            <i class="iconfont icon-shanchu1"></i> <span>删除</span>
          </div>
        </div>
      </template>
    </data-list>
    <send-info-view ref="sendInfoRef" />
    <share-dou-view ref="shareDouRef" />
    <preview ref="previewRef" :type="previewData.type" :url="previewData.url" v-if="previewData.show" @closePreview="closePreview" />
  </div>
</template>

<script setup lang="ts">
import { ref, defineAsyncComponent, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { useDataList } from '@/hooks/useDataList'
import { getVideoList, delVideoApi } from '@/api/media'

const { DataList, dataListRef, handleCover, deleteAction, refresh } = useDataList()

const SendInfoView = defineAsyncComponent(() => import('./SendInfo.vue'))
const sendInfoRef = ref()

const ShareDouView = defineAsyncComponent(() => import('./ShareDouView.vue'))
const shareDouRef = ref()

const Preview = defineAsyncComponent(() => import('@/components/Uploads/Preview.vue'));
const previewRef = ref()

const props = defineProps({
  type: {
    type: String,
    default: 'video'
  }
})

const emit = defineEmits(['changeNav'])

const tableConfig = ref({
  api: getVideoList,
  showIndex: false,
  columns: [
    { label: props.type == 'image' ? '封面' : '视频', prop: 'cover', width: props.type == 'image' ? 70 : 100, type: 'slot' },
    { label: '标题', prop: 'title', showOverflowTooltip: true },
    { label: '发布状态', prop: 'info', type: 'slot', width: 180 },
    { label: '创建时间', prop: 'create_time', width: 180 },
    { label: '操作', prop: 'action', align: 'center', width: 400, type: 'slot' }
  ],
  params: { type: props.type },
  multipleSelection: []
})

// 查看发布详情
const handleInfo = (row: any) => {
  sendInfoRef.value.openDialog(row)
}

// 数据操作
const dataAction = (data: any) => {
  // console.log(data)
  const { key } = data
  switch (key) {
    case 'add':
      emit('changeNav', { key: 'add', type: 'add' })
      break;
    case 'delete':
      if (!tableConfig.value.multipleSelection.length) return ElMessage.warning('请选择要删除的数据')
      deleteAction(tableConfig.value.multipleSelection, delVideoApi)
      break;
  }
}

// 编辑
const editInfo = (row: any) => {
  emit('changeNav', { key: 'add', val: row, type: 'edit' })
}

// 发布
const publishInfo = (row: any) => {
  emit('changeNav', { key: 'add', val: row, type: 'publish' })
}

// 分享抖音
const shareInfo = (id: number[]) => {
  shareDouRef.value.openDialog(id)
}

// 删除
const delInfo = (id: number) => {
  // console.log(id)
  deleteAction([id], delVideoApi)
}

// 视频预览
const previewData = reactive({
  show: false,
  url: "",
  type: 2
});
const previewVideo = (url: string) => {
  previewData.url = url;
  previewData.show = true;
};
const closePreview = () => {
  previewData.show = false;
};

defineExpose({ refresh })

</script>

<style scoped lang="scss">
.cover-box {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  .play-icon {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    .iconfont {
      font-size: 30px;
      color: #fff;
    }
  }
}
.text-main {
  cursor: pointer;
}
</style>