import http from '@/utils/request'
import { ElMessage } from 'element-plus';

// 判断是否开发环境
const isDev = process.env.NODE_ENV === 'development';

const { VUE_APP_PROXY_DOMAIN, VUE_APP_PROXY_DOMAIN_REAL } = process.env;

const baseUrl = isDev ? VUE_APP_PROXY_DOMAIN : VUE_APP_PROXY_DOMAIN_REAL;

// 内容列表 获取
export const contentListApi = (type: string, params?: any, config: any = {}) => {
  return http.request({
    url: `${type}/index`,
    ...config,
    method: 'get',
    params
  })
}

// 内容列表 添加
export const contentAddApi = (type: string, params?: any) => {
  return http.request({
    url: `${type}/create`,
    method: 'post',
    data: params
  })
}

// 内容列表 编辑
export const contentEditApi = (type: string, params?: any) => {
  return http.request({
    url: `${type}/update`,
    method: 'post',
    data: params
  })
}

// 内容列表 删除
export const contentDeleteApi = (type: string, params?: any) => {
  return http.request({
    url: `${type}/delete`,
    method: 'post',
    data: params
  })
}

// 内容列表 修改排序
export const contentSortApi = (type: string, params?: any) => {
  return http.request({
    url: `${type}/sort`,
    method: 'post',
    data: params
  })
}

// 内容列表 修改标题
export const contentTitleApi = (type: string, params?: any) => {
  return http.request({
    url: `${type}/title`,
    method: 'post',
    data: params
  })
}

// 内容列表 修改状态
export const contentStatusApi = (type: string, params?: any) => {
  return http.request({
    url: `${type}/start`,
    method: 'post',
    data: params
  })
}

// 内容列表 批量转移
export const contentTransferApi = (type: string, params?: any) => {
  return http.request({
    url: `${type}/transfer`,
    method: 'post',
    data: params
  })
}

// 拖拽排序  orders：初始排序 ids:拖拽后的排序  type:模块类型
export const contentDragApi = (type: string, params?: any) => {
  return http.request({
    url: `Module/Drag`,
    method: 'post',
    data: {
     ...params,
      type
    }
  })
}

// 素材库批量添加
export const contentAddMaterialApi = (type: string, params?: any) => {
  return http.request({
    url: `${type}/create_all`,
    method: 'post',
    data: params
  })
}

// 简历列表
export const contentResumeListApi = (type: string, params?: any) => {
  return http.request({
    url: `${type}/resume`,
    method: 'get',
    params
  })
}
// 导出简历
export const contentResumeExportApi = (type: string, params?: any) => {
  if (!params) return ElMessage.error('缺少必要参数');
  window.open(`${baseUrl}${type}/resume_export?${params}`, '_blank')
}


// 获取分类
export const contentTypeApi = (type: string, params?: any, config: any = {}) => {
  return http.request({
    url: `${type}/index`,
    ...config,
    method: 'get',
    params
  })
}

// 添加分类
export const contentAddTypeApi = (type: string, params?: any) => {
  return http.request({
    url: `${type}/create`,
    method: 'post',
    data: params
  })
}

// 编辑分类
export const contentEditTypeApi = (type: string, params?: any) => {
  return http.request({
    url: `${type}/update`,
    method: 'post',
    data: params
  })
}

// 删除分类
export const contentDeleteTypeApi = (type: string, params?: any) => {
  return http.request({
    url: `${type}/delete`,
    method: 'post',
    data: params
  })
}

// 获取产品特殊字段
export const goodsKeysApi = () => {
  return http.request({
    url: 'goodsnew/selkeys',
    method: 'post'
  })
}

// 极限词检测
export const checkLimitWordsApi = (data?: any) => {
  return http.request({
    url: 'LimitWords/checkOut',
    method: 'post',
    data,
    ...{ hideToast: true }
  })
}

// 极限词替换
export const replaceLimitWordsApi = (data?: any) => {
  return http.request({
    url: 'LimitWords/replaceOut',
    method: 'post',
    data,
    ...{ hideToast: true }
  })
}


