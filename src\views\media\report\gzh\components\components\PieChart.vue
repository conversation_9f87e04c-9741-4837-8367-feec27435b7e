<script setup lang="ts">
import {nextTick, ref, watch} from "vue";
import * as echarts from "echarts";

const pieChartRef = ref(null);
interface Props {
  colors: string[],
  width: string,
  height: string,
  data: any[],
  name: string
}
const props = defineProps<Props>()
const chart = ref<any>(null)

const initPieChart = () => {
  if (!pieChartRef.value) return;
  chart.value = echarts.init(pieChartRef.value);
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      icon: 'rect',
      itemWidth: 8,
      itemHeight: 8,
      align: 'auto',
      bottom: 40,
      type: 'plain',
      itemGap: 50,
    },
    label: {
      show: true,
      color: 'rgba(51, 51, 51, 1)',
      fontWeight: 'bold',
      formatter: '{b} {c}次 {d}%',
    },
    series: [
      {
        name: props.name,
        type: 'pie',
        radius: ['35%', '60%'],
        center: ['50%', '41%'],
        data: props.data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };
  chart.value.setOption(option);
}

watch(() => props.data, () => {
  nextTick(() => {
    if (chart.value) {
      chart.value.dispose();
      chart.value.clear()
    }
    initPieChart()
  })
}, {immediate: true, deep: true})
</script>

<template>
  <div ref="pieChartRef" :style="`width: ${width}; height: ${height};`"></div>
</template>

<style scoped lang="scss">

</style>
