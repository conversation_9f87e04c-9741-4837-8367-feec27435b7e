import { Module } from 'vuex'
import { SiteState } from '@/types/store/site'
import { Site } from '@/types/models/site'
import { RootState } from '@/types/store'
import { getSiteListApi, getSiteInfoApi, changeSiteApi } from '@/api/site'

const state: SiteState = {
  siteList: [],
  siteInfo: null
}

const mutations = {
  SET_SITE_LIST(state: SiteState, siteList: Site[]) {
    state.siteList = siteList
  },
  SET_SITE_INFO(state: SiteState, siteInfo: Site) {
    state.siteInfo = siteInfo
  }
}

const actions = {
  // 获取站点列表
  getSiteList({ commit, state }: any) {
    return new Promise<void>(async (resolve, reject) => {
      // console.log(state.siteList, '站点列表')
      const result:any = await getSiteListApi()
      commit('SET_SITE_LIST', result)
      if (!state.siteInfo || !state.siteInfo.id) commit('SET_SITE_INFO', result[0])
    })
  },
  // 设置当前站点
  async setSiteInfo({ commit }: any, siteInfo: Site) {
    const result: any = await changeSiteApi({site_id: siteInfo.id})
    window.location.reload()
    // commit('SET_SITE_INFO', siteInfo)
  },
  // 获取站点信息
  getSiteInfo({ commit, state }: any) {
    return new Promise<void>(async (resolve, reject) => {
      const result:any = await getSiteInfoApi()
      // console.log(result, '站点信息')
      commit('SET_SITE_INFO', result)
      resolve(result)
    })
  }
}

const site: Module<SiteState, RootState> = {
  namespaced: true,
  state,
  mutations,
  actions
}

export default site
