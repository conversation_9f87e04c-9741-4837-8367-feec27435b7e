<template>
  <div class="flex-y h100 mt10">
    <div class="el-table-box flex-1">
      <el-table v-loading="listLoading" :data="tableData" height="100%"
        style="width: 100%" ref="multipleTableRef"
        @selection-change="handleSelectionChange"
        row-key="item_id"
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
        <!-- 复选框 -->
        <el-table-column type="selection" width="55" align="center"></el-table-column>
        <el-table-column prop="cover" label="封面" width="94" align="center">
          <template #default="{ row }">
            <el-image :class="`width${isVideo ? '50' : '70'} height${isVideo ? '90' : '40'} mt8 mb8`" style="border-radius: 8px;"
              :src="handleCover(row)" fit="cover" :preview-src-list="[handleCover(row)]" :preview-teleported="true">
            </el-image>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="标题" align=""></el-table-column>
        <el-table-column prop="create_time" label="时间" width="180" align="">
          <template #default="{ row }">
            <template v-if="listName == 8">{{ time(row.create_time) }}</template>
            <template v-else>{{ row.create_time }}</template>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">已发布</template>
        </el-table-column>
        <template v-if="listName != 7">
          <el-table-column prop="info" label="数据" width="200">
            <template #default="{ row }">
              <div v-html="row.info"></div>
            </template>
          </el-table-column>
        </template>
        <template v-if="listName != 3 && listName != 4">
          <el-table-column label="操作" width="300" align="center" fixed="right">
            <template #default="{ row }">
              <div class="table-action flex">
                <a v-if="row.share_url" class="item" target="_blank" :href="row.share_url">
                  <i class="iconfont icon-chakanxiangqing"></i> <span>查看原作品</span>
                </a>
                <div class="item" @click="delInfo([row.item_id])">
                  <i class="iconfont icon-shanchu1"></i> <span>删除</span>
                </div>
                <div v-if="listName == 1" class="item" @click="showComment(row)">
                  <i class="iconfont icon-chakanxiangqing"></i> <span>查看评论</span>
                </div>
              </div>
            </template>
          </el-table-column>
        </template>
      </el-table>
    </div>
    <div v-if="tableTotal > 0" style="position: relative;">
      <data-page v-model:page="tableParams.page" :total="tableTotal" :totalPage="tableTotal" @pagination="getTableData" />
      <div v-if="listName != 3 && listName != 4" class="show-top flex">
        <div class="btn-box flex">
          <jzt-button name="删除" icon="icon-shanchu1" @click="delData" />
        </div>
      </div>
    </div>
    <!-- 查看评论 -->
    <comment-view ref="commentViewRef" :now-info-id="nowInfoId" :list-name="listName" />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, defineAsyncComponent } from 'vue'
import { ElMessage } from 'element-plus'
import { useDataList } from '@/hooks/useDataList'
import { getWorkListApi, deleteWorkApi } from '@/api/media/account'

const { deleteAction, handleCover } = useDataList()

const DataPage = defineAsyncComponent(() => import('@/components/Global/DataPage.vue'))
const CommentView = defineAsyncComponent(() => import('./CommentView.vue'))

const commentViewRef = ref()

const props = defineProps({
  nowInfoId: {
    type: Number,
    default: 0
  },
  listName: {
    type: Number,
    default: 0
  }
})

const isVideo = computed(() => {
  switch (props.listName) {
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
      return true
    default: return false
  }
})

// 数据
const listLoading = ref(false)
const tableData = ref([])
const tableParams = ref({ page: 1, limit: 10, id: 0 })
const tableTotal = ref(0)

// 获取数据
const getTableData = () => {
  listLoading.value = true
  getWorkListApi(tableParams.value).then((res: any) => {
    tableData.value = res.data
    tableTotal.value = res.total
  }).finally(() => {
    listLoading.value = false
  })
}

/**
 * 多选
 */
const multipleSelection = ref([])
const handleSelectionChange = (val: any) => {
  multipleSelection.value = val
}

// 时间格式转化
const time = (date: any) => {
  date = new Date(date);
  var y = date.getFullYear()
  var m = date.getMonth() + 1
  m = m < 10 ? '0' + m : m
  var d = date.getDate()
  d = d < 10 ? '0' + d : d
  var h = date.getHours()
  h = h < 10 ? '0' + h : h
  var minute = date.getMinutes()
  minute = minute < 10 ? '0' + minute : minute
  var second = date.getSeconds()
  second = second < 10 ? '0' + second : second
  return y + '-' + m + '-' + d + ' ' + h + ':' + minute + ':' + second
}

// 批量删除
const delData = async () => {
  if (!multipleSelection.value.length) return ElMessage.warning('请选择要删除的作品')
  let ids = multipleSelection.value.map((item: any) => item.item_id)
  await deleteAction('', deleteWorkApi, {id: props.nowInfoId,item_id: ids})
  tableParams.value.page = 1
  getTableData()
}

// 删除单个
const delInfo = async (id: any) => {
  await deleteAction('', deleteWorkApi, {id: props.nowInfoId,item_id: id})
  tableParams.value.page = 1
  getTableData()
}

// 查看评论
const showComment = (row: any) => {
  commentViewRef.value.openDialog(row)
}

watch(() => props.nowInfoId, (val) => {
  tableParams.value.page = 1
  tableParams.value.id = val
  getTableData()
}, { immediate: true })

</script>

<style scoped lang="scss">
.show-top {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  .btn-box {
    margin-left: 20px;
  }
}
</style>