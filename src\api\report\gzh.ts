

// 获取公众号信息
import http from "@/utils/request";

export const getGzhInfoApi = (params?: any) => {
  return http.request({
    url: '/StatisticsApi/getGzhInfo',
    method: 'get',
    params
  })
}

// 公众号列表
export const getGzhListApi = (params?: any) => {
  return http.request({
    url: '/NewMedia/accountList',
    method: 'get',
    params
  })
}

// 切换公众号
export const changeGzhApi = (params?: any) => {
  return http.request({
    url: '/StatisticsApi/editwechat',
    method: 'post',
    data: params
  })
}

// 内容分析
export const getContentAnalysisApi = (params?: any) => {
  return http.request({
    url: '/StatisticsApi/edittw',
    method: 'get',
    params
  })
}

// 渠道构成
export const getChannelStructureApi = (params?: any) => {
  return http.request({
    url: '/StatisticsApi/editqd',
    method: 'get',
    params
  })
}

// 用户分析
export const getUserAnalysisApi = (params?: any) => {
  return http.request({
    url: '/StatisticsApi/editpl',
    method: 'get',
    params
  })
}

// 总览数据
export const getOverviewDataApi = (params?: any) => {
  return http.request({
    url: '/StatisticsApi/seltj',
    method: 'get',
    params
  })
}
