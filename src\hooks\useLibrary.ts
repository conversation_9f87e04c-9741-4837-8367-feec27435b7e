import { defineAsyncComponent, ref, computed, watch } from 'vue'
import { useStore } from 'vuex'
import { ElMessage, ElLoading, ElMessageBox } from 'element-plus'
import { getBucketListApi, addBucketCateApi, deleteBucketApi, editStatusApi, editBucketTitleApi, transferBucketApi, downloadBucketApi } from '@/api/bucket'

export const useLibrary = (emit: any = null) => {
  const store = useStore()
  const userIp = computed(() => store.getters.userIp)

  const isFree = computed(() => store.getters.isFree)

  watch(userIp, (newVal) => {
    console.log(newVal, 'userIp')
    if (!newVal) store.dispatch('app/getUserIp')
  }, { immediate: true })

  // 分页组件
  const DataPage = defineAsyncComponent(() => import('@/components/Global/DataPage.vue'))

  // 图片加载失败
  const imgError = (e: any, type: string) => {
    let obj:any = {
      img: require('@/assets/images/system/img-error.jpg'),
      video: require('@/assets/images/system/video-error.jpg'),
      audio: require('@/assets/images/system/audio-error.jpg')
    };
    e.target.src = obj[type] || require('@/assets/images/system/img-error.jpg');
  }

  // 根据文件名判断是视频还是音频
  const getFileType = (fileName: string) => {
    if (!fileName) {
      return "video";
    }
    try {
      console.log("[ fileName ] >", fileName);
      let extName = fileName.split(".").slice(-1)[0];
      console.log("[ extName ] >", extName);
      // 音频后缀列表
      const audioList = ["mp3", "wma", "wav", "amr", "aac"];
      if (audioList.includes(extName)) {
        return "audio";
      } else {
        return "video";
      }
    } catch (e) {
      console.log(e, "获取文件后缀失败");
      return "video";
    }
  }

  // 文件预览
  const Preview = defineAsyncComponent(() => import('@/components/Uploads/Preview.vue'));
  const previewData = ref({
    show: false,
    url: "",
    type: 1
  })

  const previewVideo = (url: string, type: number) => {
    if (!url) return
    previewData.value.url = url
    previewData.value.type = type
    previewData.value.show = true
  }

  const closePreview = () => {
    previewData.value.show = false
  }

  // 选择素材
  const checkItem = (index: number, event: any) => {
    let list = JSON.parse(JSON.stringify(tableData.value));
    if (event && event.target) {
      if (["I", "IMG"].includes(event.target.tagName)) {
        previewVideo(tableData.value[index].url, event.target.tagName === "I" ? 2 : 1);
      } else {
        list[index].isCheck = !list[index].isCheck
      }
    } else {
      list[index].isCheck = !list[index].isCheck;
    }
    tableData.value = list;
    // 判断是否全选
    isCheckAll.value = tableData.value.every((item:any) => item.isCheck)
  }

  // 全选
  const isCheckAll = ref(false)
  const changeCheckAll = () => {
    tableData.value.forEach((item:any) => {
      item.isCheck = isCheckAll.value
    })
  }

  // 获取对应文件类型的图标
  const getFileImg = (res: string) => {
    let fileExtension = res.substring(res.lastIndexOf('.') + 1);
    let fileType = 'default'
    const extensions:any = {
      excel: ["xlsx", "xls", "csv"],
      ppt: ["ppt", "pptx"],
      pdf: ["pdf"],
      word: ["doc", "docx"],
      txt: ["txt"],
      zip: ["zip", "rar", "7z"]
    };

    Object.keys(extensions).forEach(key => {
      if (extensions[key].includes(fileExtension)) {
        fileType = key;
      }
    });
    return require(`@/assets/images/bucket/${fileType}.png`);
  }

  const sizeFilter = (bytes: number) => {
    if (bytes === 0) return '0 bytes';
    const k = 1024;
    const sizes = ['bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // 获取文件列表 相关
  const loading = ref(false)
  const tableData:any = ref([])
  const total = ref(0)
  const lPage = ref(0)
  const params = ref({
    page: 1,
    limit: 24,
    parent_id: 0,
    title: ''
  })
  const getFileList = async () => {
    try {
      loading.value = true
      const res:any = await getBucketListApi(params.value)
      console.log(res, '素材列表')
      const { data, total: totalNum, last_page } = res
      tableData.value = data.map((item:any) => {
        return {
          ...item,
          isCheck: false,
          hidePoster: false,
          isFile: "bucket_type_id" in item ? 2 : 1
        }
      })
      total.value = totalNum
      lPage.value = last_page
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  }

  const refresh = () => {
    params.value.page = 1
    tableData.value = []
    getFileList()
  }

  const checkedTableData = computed(() => {
    return tableData.value.filter((item:any) => item.isCheck)
  })

  const folderList = ref([
    { name: '全部文件', id: 0 },
  ])
  
  const currentFolder = ref(0)
  
  const toSecondFiles = (item: any) => {
    console.log('toSecondFiles', item)
    // 如果item是数组
    if (Array.isArray(item)) {
      folderList.value = folderList.value.slice(0, 1)
      folderList.value = [...folderList.value, ...item]
      params.value.parent_id = item[item.length - 1].id
      params.value.page = 1
      params.value.title = ''
      currentFolder.value = item[item.length - 1].id
      if (emit) emit('folderChange', item[item.length - 1])
    } else {
      params.value.parent_id = item.id
      params.value.page = 1
      params.value.title = ''
      currentFolder.value = item.id
      if (emit) emit('folderChange', item)
      // 重复不追加 切去掉对应后面的
      const index = folderList.value.findIndex((v: any) => v.id === item.id)
      if (index == -1) folderList.value.push({ name: item.title, id: item.id })
      else folderList.value.splice(index + 1)
    }
    getFileList()
  }
  
  const goBack = () => {
    toSecondFiles(folderList.value[folderList.value.length - 2])
  }

  // 新建分类
  const addBucketCate = () => {
    return new Promise(async (resolve, reject) => {
      const loadingInstance = ElLoading.service()
      try {
        const res:any = await addBucketCateApi({p_id: currentFolder.value, title: '新建文件夹'})
        resolve(res)
        refresh()
      } catch (error) {
        console.log(error)
      } finally {
        loadingInstance.close()
      }
    })
  }

  // 删除
  const deleteBucket = (list: any = null) => {
    return new Promise(async (resolve, reject) => {
      if (!list) list = tableData.value.filter((item:any) => item.isCheck)
      if (list.length == 0) return ElMessage.warning('请选择要删除的文件或文件夹')
      list = list.map((item:any) => {
        return {
          id: item.id,
          type: item.isFile
        }
      })
      await ElMessageBox.confirm('删除之后网站引用的也会失效，确定要删除吗？', '提示', { type: 'warning' })
      const loadingInstance = ElLoading.service()
      try {
        const res:any = await deleteBucketApi({data: list, ip: userIp.value})
        ElMessage.success('删除成功')
        resolve(res)
        refresh()
        if (emit) emit('closeCate')
      } catch (error) {
        console.log(error)
      } finally {
        loadingInstance.close()
      }
    })
  }

  // 单独删除
  const deleteItem = (item: any) => {
    deleteBucket([item])
  }

  // 上线下线
  const onOffLine = async (item: any) => {
    const { id, status } = item
    let type;
    if (status === 1) type = 2;
    else type = 1;
    const loadingInstance = ElLoading.service()
    try { 
      const res:any = await editStatusApi({ id, type })
      ElMessage.success('操作成功')
      refresh()
    } catch (error) {
      console.log(error)
    } finally {
      loadingInstance.close()
    }
  }

  // 复制链接
  const copyLink = (url: string) => {
    if (navigator.clipboard) {
      navigator.clipboard.writeText(url)
      ElMessage.success('复制成功')
    } else {
      const textArea = document.createElement('textarea');
      textArea.value = url;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      ElMessage.success('复制成功')
    }
  }

  // 编辑标题
  const editItem = (item: any) => {
    if (item.isFile == 2 && item.type == 1) {

    }
    else item.edit = true
  }

  // 修改标题
  const editTitle = async (item: any) => {
    // 
    const loadingInstance = ElLoading.service()
    try {
      const res:any = await editBucketTitleApi({ id: item.id, title: item.title }, item.isFile)
      ElMessage.success('修改成功')
      refresh()
    } catch (error) {
      console.log(error)
    } finally {
      loadingInstance.close()
    }
  }

  // 转移
  const transfer = async (data: any) => {
    console.log(data, 'transfer')
    console.log(checkedTableData.value, 'checkedTableData')
    if (data.id == null || data.id == undefined) return ElMessage.warning('请选择要转移的文件夹')
    let list = checkedTableData.value.map(({id, isFile}: any) => ({id, type: isFile}))
    // 文件夹不能转移到自己
    let _folderList = list.filter((item:any) => item.type == 1)
    if (_folderList.findIndex((item:any) => item.id == data.id) != -1) return ElMessage.warning('不能将分类转移到分类本身')
    const loadingInstance = ElLoading.service()
    try {
      const res:any = await transferBucketApi({ data: list, type_id: data.id })
      ElMessage.success('转移成功')
      refresh()
      emit('closeCate')
    } catch (error) {
      console.log(error)
    } finally {
      loadingInstance.close()
    }
  }

  // 下载
  const download = async () => {
    console.log('download')
    if (checkedTableData.value.length == 0) return ElMessage.warning('请选择要下载的文件')
    // 不支持文件夹下载
    const list = checkedTableData.value.filter((item:any) => item.isFile == 2)
    if (!list.length) return ElMessage.warning('暂不支持文件夹下载')
    const loadingInstance = ElLoading.service()
    try {
      const res:any = await downloadBucketApi(list.map((item:any) => item.id))
    } catch (error) {
      console.log(error)
    } finally {
      loadingInstance.close()
    }
  }

  return {
    imgError, getFileType, DataPage, refresh,
    Preview, previewData, previewVideo, closePreview,
    tableData, total, lPage, params, loading, getFileList, checkedTableData,
    getFileImg, sizeFilter, checkItem, isCheckAll, changeCheckAll,
    folderList, currentFolder, toSecondFiles, goBack, addBucketCate,
    deleteBucket, deleteItem, isFree, onOffLine, copyLink, editItem, editTitle, transfer,
    download
  }
}