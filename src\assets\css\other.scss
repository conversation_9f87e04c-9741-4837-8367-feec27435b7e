:root {
  --el-border-color: #E3E5EC;
  --el-component-size: 34px;
  --el-border-radius-base: 3px;
  --jzt-color-main: #2859FF;
  --jzt-color-aux: #A0B6FF;
  --el-color-primary: var(--jzt-color-main);
}
.el-select-dropdown__item.selected {
  background-color: var(--jzt-color-main);
  color: var(--el-bg-color-overlay);
}
.el-input {
  --el-input-focus-border: var(--jzt-color-aux) !important;
}
.el-select__wrapper {
  min-height: var(--el-component-size);
}
.el-input__wrapper {
  height: var(--el-component-size);
}

/* nprogress适配ep的primary */
#nprogress {
  & .bar {
    background-color: var(--el-color-primary) !important;
  }

  & .peg {
    box-shadow: 0 0 10px var(--el-color-primary),
      0 0 5px var(--el-color-primary) !important;
  }

  & .spinner-icon {
    border-top-color: var(--el-color-primary);
    border-left-color: var(--el-color-primary);
  }
}