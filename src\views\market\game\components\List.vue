<script setup lang="ts">
import DataList from "@/components/Global/DataList.vue";
import {markRaw, ref} from "vue";
import {applyGameApi, deleteGameApi, downGameApi, gameListApi, upGameApi} from "@/api/game";
import {ElMessage, ElMessageBox} from "element-plus";
import {useGameExperience} from "@/hooks/useGameExperience";
import {useDialog} from "@/hooks/useDialog";
import UpdateGame from "@/views/market/game/components/list/UpdateGame.vue";
import BuyDialog from "@/views/market/game/components/list/BuyDialog.vue";
import QrcodeVue from 'qrcode.vue'

const {EditDialog} = useDialog();

const dataListRef: any = ref(null);
const tableConfig = ref({
  api: gameListApi,
  multipleSelection: [],
  params: {
    title: ''
  },
  columns: [
    {
      label: "游戏标题",
      prop: "title",
    },
    {
      label: "游戏名称",
      prop: "game",
      type: "slot"
    },
    {
      label: "公司名称",
      prop: "company_name",
      width: '200'
    },
    {
      label: "分享标题",
      prop: "share_title",
      type: "slot"
    },
    {
      label: "分享简介",
      prop: "share_desc",
      type: "slot"
    },
    {
      label: "添加时间",
      prop: "create_time",
      width: "170",
    },
    {
      label: "发布状态",
      prop: "status",
      width: "120",
      type: "slot"
    },
    {
      label: "操作",
      prop: "action",
      width: '300',
      type: "slot",
      align: 'center'
    },
  ],
});
const statusMap: {[key: number]: string} = markRaw({
  1: '已发布',
  0: '未发布'
})
const refresh = () => {
  dataListRef.value.refresh();
};

const emits = defineEmits(['changeNav'])
/**
 * 添加
 */
const add = () => {
  emits('changeNav', 1);
};

const loading = ref(false);
/**
 * 删除接口
 * @param id
 * @param showLoading
 */
const delApi = async (id: number | string, showLoading = false) => {
  ElMessageBox.confirm('确认删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    if(showLoading) loading.value = true;
    try {
      await deleteGameApi({id});
      ElMessage.success('删除成功');
      dataListRef.value.refresh();
    } finally {
      loading.value = false;
    }
  }).catch(() => {
  });
};

/**
 * 删除
 * @param row
 */
const delItem = (row: any) => {
  if(row.length === 0) return ElMessage.warning('请先选择数据');
  const ids = row.map((item: any) => item.id).join(',');
  delApi(ids, true);
};

const actionList = ref([
  {
    name: "添加",
    icon: "icon-tianjia",
    action: add,
    checkSelected: false
  },
  {
    name: "删除",
    icon: "icon-shanchu1",
    action: delItem,
    checkSelected: true
  },
]);
/**
 * 按钮点击事件
 * @param button
 */
const dataAction = (button: any) => {
  if (button.checkSelected) {
    if (!tableConfig.value.multipleSelection.length) {
      ElMessage.warning('请先选择数据')
      return
    }
  }
  button.action(tableConfig.value.multipleSelection)
}

/*选中数据*/
const updateInfo = ref<{
  title: string,
  show: boolean,
  data: any,
}>({
  title: '',
  show: false,
  data: null
});
/**
 * 编辑
 * @param row
 */
const edit = (row: any) => {
  updateInfo.value.title = '编辑小游戏';
  updateInfo.value.show = true;
  updateInfo.value.data = {
    game_id: row.game_id,
    title: row.title,
    share_title: row.share_title,
    share_desc: row.share_desc,
    company_name: row.company_name,
    phone: row.phone,
    domain: row.domain,
    url: row.url,
    share_img: row.share_img,
    status: row.status,
    id: row.id
  };
};

/**
 * 删除
 */
const del = (id: number | string) => {
  id && delApi(id);
}

/**
 * 上下架
 * @param row
 */
const changeStatus = async (row: any) => {
  const status = row.status == 1 ? 0 : 1;
  if(+status === 1) {
    await upGameApi({id: row.id});
  }else{
    await downGameApi({id: row.id});
  }
  row.status = status;
  dataListRef.value.refresh();
};

const {editDialogRef: qrCodeRef, dialogShow: qrCodeShow, openDialog: openQrCode, closeDialog: closeQrCode, dialogData: qrCodeData} = useDialog();
/**
 * 显示二维码
 */
const showQrCode = (row: any) => {
  openQrCode(row);
}

/**
 * 获取体验情况
 */
const {experienceInfo, getExperienceInfo} = useGameExperience();
getExperienceInfo();

/**
 * 跳转体验
 */
const tryLoading = ref(false);
const toExperience = async () => {
  tryLoading.value = true;
  try{
    await applyGameApi();
    ElMessage.success('申请成功');
    await getExperienceInfo();
  }catch (e) {
    console.log(e);
  }finally {
    tryLoading.value = false;
  }
};

const buyShow = ref(false);
/**
 * 购买时长
 */
const toBuy = () => {
  buyShow.value = true;
};

/**
 * 表单设置
 */
const showFormSettings = (row: any) => {
  emits('changeNav', 2, row);
};

/**
 * 表单数据
 */
const showFormData = (row: any) => {
  emits('changeNav', 3, row);
}
</script>

<template>
  <div class="game-list h100">
    <data-list ref="dataListRef" :tableConfig="tableConfig" :action-default="false" @clickBtn="dataAction"
               :actionList="actionList" v-model:multipleSelection="tableConfig.multipleSelection">
      <template #top-right>
        <div class="money-info flex">
          <img src="@/assets/images/system/icon16.png" alt="">
          <span v-if="experienceInfo.display == 1" class="text text-action" @click="toExperience">免费试用7天~</span>
          <span v-if="experienceInfo.diff_days" class="text">{{ experienceInfo.diff_days }}</span>
          <el-button type="primary" class="to-buy" @click="toBuy">购买时长</el-button>
          <!-- 购买弹窗 -->
          <buy-dialog v-model:dialog-show="buyShow" @success="getExperienceInfo" />
          <!-- 搜索 -->
          <div class="search flex">
            <el-input placeholder="请输入标题搜索" v-model="tableConfig.params.title" clearable style="width: 300px" />
          </div>
        </div>
      </template>
      <template #game="{row}">
        {{row?.game?.title || '未知'}}
      </template>
      <template #share_title="{row}">
        {{row?.share_title || '未设置'}}
      </template>
      <template #share_desc="{row}">
        {{row?.share_desc || '未设置'}}
      </template>
      <template #status="{row}">
        {{statusMap[row.status]}}
      </template>
      <template #action="{ row }">
        <div class="table-action flex">
          <div class="item" @click="edit(row)">
            <i class="iconfont icon-bianji"></i> <span>编辑</span>
          </div>
          <div class="item" @click="changeStatus(row)">
            <template v-if="row.status === 1">
              <i class="iconfont icon-xiajia"></i> <span>下架</span>
            </template>
            <template v-else>
              <i class="iconfont icon-fabu"></i> <span>发布</span>
            </template>
          </div>
          <div class="item" @click="showQrCode(row)">
            <i class="iconfont icon-erweima"></i> <span>游戏二维码</span>
          </div>
          <div class="item" @click="showFormSettings(row)">
            <i class="iconfont icon-yunyingbaogao"></i> <span>表单设置</span>
          </div>
          <div class="item" @click="showFormData(row)">
            <i class="iconfont icon-xiangqing1"></i> <span>表单数据</span>
          </div>
          <div class="item" @click="del(row.id)">
            <i class="iconfont icon-shanchu1"></i> <span>删除</span>
          </div>
        </div>
      </template>
    </data-list>

    <!--编辑-->
    <update-game :title="updateInfo.title" v-model:dialog-show="updateInfo.show" :data="updateInfo.data" v-if="updateInfo.data" @success="refresh" />
    <!-- 二维码 -->
    <edit-dialog ref="qrCodeRef" :dialog-show="qrCodeShow" title="游戏二维码" :custom-form="true" :show-bottom-btn="false"
                  @close="closeQrCode" @sure="closeQrCode" width="300px">
      <div class="flex-c-center">
        <qrcode-vue :value="qrCodeData?.game?.game_url" :size="200" level="H"></qrcode-vue>
      </div>
    </edit-dialog>
  </div>
</template>

<style scoped lang="scss">
.game-list {
  .money-info {
    img {
      width: 20px;
      margin-right: 3px;
    }
    .text {
      font-size: 14px;
      color: #70749B;
      margin-right: 10px;
      &.text-action {
        text-decoration: underline;
        color: #1C2B4B;
        cursor: pointer;
      }
    }
    .to-buy {
      padding: 6px 10px;
      border: none;
      height: auto;
    }
    .search{
      margin-left: 10px;
    }
  }
}
</style>
