const { defineConfig } = require('@vue/cli-service')
module.exports = defineConfig({
  transpileDependencies: true,
  // 打包配置 文件夹设置
  outputDir: 'jzt_content',
  assetsDir: 'static',
  indexPath: 'index.html',
 
  // 配置webpack
  publicPath: process.env.NODE_ENV === 'production' ? './' : '/',
  devServer: {
    port: 8800,
    proxy: {
      '/api': {
        target: process.env.VUE_APP_PROXY_DOMAIN_REAL,
        changeOrigin: true,
        pathRewrite: {
          '^/api': ''
        }
      },
      '/newside': {
        target: process.env.VUE_APP_PROXY_DOMAIN_REAL3,
        changeOrigin: true,
        pathRewrite: {
          '^/newside': ''
        }
      }
    },
    client: {
      overlay: false
    }
  }
})

