<script setup lang="ts">
import DataTable from "@/components/Global/DataTable.vue";
import {markRaw, ref} from "vue";
import {createOrderApi, getOrderStatusApi, getPackageListApi, getWxPayQrApi} from "@/api/game";
import {ElMessage} from "element-plus";
import {useDialog} from "@/hooks/useDialog";

const emits = defineEmits(['success']);

const list: any = ref([]);
// 获取数据
const loading = ref(false);
const getData = async () => {
  loading.value = true;
  try{
    list.value = await getPackageListApi() as any;
  }finally {
    loading.value = false;
  }
}
getData();
const columns = markRaw([
  {
    label: "时长",
    prop: "title",
  },
  {
    label: "价格",
    prop: "price",
    width: 100,
  },
  {
    label: "操作",
    prop: "action",
    type: "slot",
    width: 200,
    align: "center",
  }
])

/**
 * 创建套餐订单
 */
const createOrder = async (id: string | number) => {
  if(!id) return null;
  return await createOrderApi({type_id: id, pay_type: 1});
}
const orderLoading = ref(false);
const { EditDialog, editDialogRef, dialogData, dialogShow, openDialog, closeDialog }= useDialog();
// 支付状态
const paySuccess = ref(false);
/**
 * 获取支付状态，直至成功
 * @param order_sn {string} 订单号
 */
const orderStatus = async (order_sn: string) => {
  if(!order_sn) return;
  try{
    const res: any = await getOrderStatusApi({order_sn});
    if(res && +res.status === 1) {
      ElMessage.success('支付成功');
      emits('success');
      closeDialog();
      paySuccess.value = true;
    }else{
      // 未支付 五秒查询一次状态
      setTimeout(function () {
        orderStatus(order_sn)
      }, 5000)
    }
  }catch (e){
    console.log(e);
  }
}
/**
 * 购买套餐
 * @param data {Object} 套餐数据
 * @param way {string} 支付方式 目前只有微信扫码支付 所以默认传wx-scancode
 * @param type {string} 支付类型
 */
const buyMeal = async (data: any, way: string = 'wx', type: string = 'wx-scancode') => {
//   创建套餐订单
  orderLoading.value = true;
  paySuccess.value = false;
  try{
    const order: any = await createOrder(data.id);
    const apiMap: any = {
      wx: 'wxpay'
    }
    if(order){
      const res: any = await getWxPayQrApi({
        jzt: 'jzt1',  // 建站通版本
        type: type,  // 支付类型（微信扫码支付：wx-scancode）
        order_type: 'game',
        order_sn: order.order_sn,  //订单号  通过创建订单接口返回
        order_desc: '小游戏套餐购买'  // 订单介绍
      }, apiMap[way]);
      if(res){
        switch (way){
          case 'wx':
            openDialog(res);
            await orderStatus(order.order_sn);
            break;
          default:
            openDialog(res);
            await orderStatus(order.order_sn);
            break;
        }
      }
    }
  }finally {
    orderLoading.value = false;
  }
}
</script>

<template>
  <div class="w100 meal-wrap" v-loading="loading">
    <data-table :table-data="list" :show-index="false" :show-multiple="false" :columns="columns">
      <template #action="{row}">
        <el-button type="primary" @click="buyMeal(row)" class="buy">购买</el-button>
      </template>
    </data-table>

    <edit-dialog ref="editDialogRef" :dialog-show="dialogShow" title="扫码支付" :custom-form="true" :show-bottom-btn="false" @close="closeDialog" @sure="closeDialog">
      <div class="flex-column" v-if="dialogData && !paySuccess">
        <el-image :src="dialogData" alt="" style="width: 240px" />
        <p class="tips">请使用微信扫码支付</p>
      </div>
    </edit-dialog>
  </div>
</template>

<style scoped lang="scss">
.meal-wrap{
  max-height: 600px;
  overflow: auto;
  border-left: 1px solid #ebeef5;
  border-right: 1px solid #ebeef5;
  .buy{
    height: auto;
    padding: 4px 10px;
    border: none;
  }
}
.tips {
  font-size: 16px;
  color: #696C7D;
}
</style>
