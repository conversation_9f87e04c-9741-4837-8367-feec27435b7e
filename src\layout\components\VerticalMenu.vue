<!-- 左侧导航 -->
<template>
  <div :class="['left-container', {'is-iframe': isIframe}]">
    <div v-loading="loading" style="width: 100%;flex: 1;height: calc(100% - 82px);">
      <nav class="nav-container">
        <div class="nav-wrap">
          <template v-for="(item, index) in leftMenuList">
            <menu-tree :data="item" :index="index" :level="0" />
          </template>
        </div>
      </nav>
    </div>
    <!-- <img src="@/assets/images/logo.png" alt="" class="left-logo"> -->
  </div>
</template>

<script lang="ts" setup>
import { ref, defineAsyncComponent, onMounted, nextTick, computed } from 'vue';
import { useRouter } from 'vue-router';
import store from '@/store';

const router = useRouter();

const leftMenuList = computed(() => store.getters.leftMenuList)

const MenuTree = defineAsyncComponent(() => import('./MenuTree.vue'))

const menuList: any = ref([]);

// 获取站点导航列表
const loading = ref(false);

onMounted(() => {

})

const isIframe = computed(() => {
  return true
  return window.self !== window.top
})
</script>

<style lang='scss' scoped>
.left-container {
  background: #fff;
  width: 180px;
  flex-shrink: 0;
  border-right: 1px solid #e6ecf5;
  transition: all ease-in-out 0.3s;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding-top: 18px;
  gap: 10px;
  &.is-iframe {
    width: 130px;
    .left-logo {
      margin-top: 0;
    }
  }
  // 投影
  // box-shadow: 0px 4px 10px 0px #F1F5FF;
  // margin-right: 1px;

  &.close-menu {
    width: 82px;
    overflow-x: hidden;

    .left-logo {
      width: 80%;
    }

    .top {
      .icon {
        &:first-child {
          display: block;
        }

        &:last-child {
          display: none;
        }
      }
    }

    .nav-container {
      .item {
        padding-left: 0;

        .icon-box {
          display: flex;
        }

        .m-title {
          opacity: 0;
          width: 0;
        }
      }
    }
  }

  .top {
    padding-top: 35px;
    padding-bottom: 12px;

    .icon {
      margin: 0 26px 0 20px;
      cursor: pointer;
      width: 35px;

      // &:first-child {}
    }
  }

  .left-logo {
    width: fit-content;
    margin-bottom: 26px;
    margin-top: 20px;
  }
}

.nav-container {
  height: 100%;
  width: 100%;
  position: relative;
  padding: 0;

  .nav-wrap {
    height: 100%;
    width: 100%;
    overflow: auto;
  }


}

/*二级菜单*/
.second-menu {
  background: linear-gradient(90deg, rgba(240, 242, 246, 1), rgba(238, 241, 245, 0.4));
  height: calc(100vh - 120px);
  padding-top: 25px;
  box-sizing: border-box;
  overflow-x: hidden;
  width: max-content;

  .item {
    height: 40px;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 500;
    color: #696c7d;
    padding: 0 24px;
    margin-bottom: 10px;
    position: relative;
    cursor: pointer;
    width: 100%;

    &:before {
      content: '';
      position: absolute;
      width: 8px;
      height: 100%;
      background: #2859ff;
      border-radius: 4px;
      top: 0;
      left: -8px;
      transition: all ease-in-out .3s;
    }

    .m-title {
      margin-bottom: 0;
    }
  }
}

.is-marketing {
  width: 16px;
  height: 12px;
  transform: translateY(-10px);
  margin-left: 5px;
}

.flex-title {
  display: flex;
  align-items: center;
}

.is-marketing-position {
  position: absolute;
  top: -10px;
  right: 7px;
}

.nav-container .submenu .item:hover,
.nav-container .submenu .item.selected {
  background: #f5f9ff;
  border-radius: 2px;
}

.nav-container .submenu .item:hover .m-title,
.nav-container .submenu .item.selected .m-title {
  color: #2859ff;
}

.nav-loading-wrap {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ffffff50;
  z-index: 99;

  .loading-text {
    font-size: 14px;
    color: #999999;
    margin-left: 10px;
  }
}
</style>
