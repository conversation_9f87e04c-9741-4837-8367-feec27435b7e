import { RouteRecordRaw } from "vue-router";
import { router } from "./index";
import { useStore } from "vuex";
const Layout = () => import("@/layout/index.vue");
const RouterViewWrap = () => import("@/components/RouterViewWrap.vue");
const IFrame = () => import("@/layout/FrameView.vue");

// 初始化路由
export const initRouter = () => {
  return new Promise(async (resolve, reject) => {
    const store = useStore();
    const menus:any = await store.dispatch("app/getMenuList");
    console.log(menus, 'menus');
    if (menus.length) {
      const asyncRouter = filterAsyncRouter(JSON.parse(JSON.stringify(menus)));
      resolve(asyncRouter);
    }
    router.addRoute({
      path: "/:pathMatch(.*)",
      redirect: "/error/404"
    });
  });
}

// 过滤后端传来的动态路由 重新生成规范路由
export const filterAsyncRouter = (routers: any, pname: string = "") => { // 遍历后台传来的路由字符串，转换为组件对象
  return routers.filter((v: any) => {
    if (v.component) {
      if (v.component === 'Layout') { // Layout组件特殊处理
        v.component = Layout;
      } else if (v.component === 'RouterViewWrap') {
        v.component = RouterViewWrap;
      } else {
        const component = v.component;
        v.component = () => process.env.NODE_ENV === "development" ? require(`@/views/${component}.vue`) : import(`@/views/${component}.vue`)
      }
    }
    v.meta = { title: v.title, icon: v.icon };
    // 如果path开头没有/ 则加上
    if (v.path && v.path.indexOf("/")!== 0) v.path = "/" + v.path;
    if (pname) router.addRoute(pname, v);
    else router.addRoute(v);
    if (v.children && v.children.length) {
      v.children = filterAsyncRouter(v.children, v.name)
    }
    return true
  })
}
