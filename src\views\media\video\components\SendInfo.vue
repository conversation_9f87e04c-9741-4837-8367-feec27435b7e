<template>
  <edit-dialog ref="editDialogRef" v-model:dialogShow="dialogShow" v-bind="formConfig">
    <div class="up-status">
      <div class="up-title">标题：{{ dialogData.title }}</div>
      <div class="up-status-all">
        <span>成功：<i class="success">{{ statistics.successNum || 0 }}</i></span>
        <span>失败：<i class="fail">{{ statistics.errorNum || 0 }}</i></span>
        <span v-if="'upNum' in statistics">正在发布：<i class="releasing">{{ statistics.upNum || 0 }}</i></span>
      </div>
      <div class="up-status-list">
        <div class="up-status-item" v-for="(item, index) in checkList">
          <div class="flex-between">
            <div class="left-info flex">
              <img :src="item.avatar" class="avatar" alt="">
              <div class="info-box">
                <p class="info-user one-line">{{ dialogData.title }}</p>
                <p class="info-type">{{ item.type }}：{{ item.title }}</p>
              </div>
            </div>
            <div class="right-info">
              <div :class="['up-tips', { 'green': item.upStatus == 2 }, { 'red': item.upStatus == 1 }, { 'blue': !item.upStatus }]">
                发布{{ !item.upStatus ? '中' : '' }}{{ item.upStatus == 2 ? '成功' : '' }}{{ item.upStatus == 1 ? '失败' : ''
                }}
              </div>
              <div class="up-progress" v-if="!item.upStatus"><span :style="{ width: item.percent + '%' }"></span></div>
              <div class="up-time one-line">{{ item.time }}</div>
            </div>
          </div>
          <div class="up-msg one-line" v-if="item.upStatus == 1 && item.upMsg" :title="item.upMsg">{{ item.upMsg }}</div>
        </div>
      </div>
    </div>
  </edit-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useDialog } from '@/hooks/useDialog'
import { sendVideoApi, sendArticleApi } from '@/api/media'

const { EditDialog, editDialogRef, dialogData, dialogShow, openDialog } = useDialog()

const props = defineProps({
  type: {
    type: String,
    default: 'video'
  }
})

const formConfig = ref({
  title: '发布状态',
  width: '780px',
  customForm: true,
  showBottomBtn: false
})

const statistics:any = ref<{ successNum?: number, errorNum?: number, upNum?: number }>({ successNum: 0, errorNum: 0 })
const checkList = ref<any[]>([])

const filterStatus = (info: string) => {
  let statusList = info.split('  ');
  statistics.value = {}
  statusList.forEach(v => {
    const [status, count] = v.split('：');
    const statusStr = status === '成功' ? 'successNum' : status === '失败' ? 'errorNum' : '';
    if (statusStr) {
      statistics.value[statusStr] = +count;
    }
  })
}

watch(dialogShow, (val) => {
  if (val) {
    let list = dialogData.value.sendinfo || []
    checkList.value = list.map((item: any) => ({ ...item, upStatus: item.status, upMsg: item.status_text, time: item.create_time }))
    if (dialogData.value.info) filterStatus(dialogData.value.info)
  }
})

// 发布
const sendInfo = (accountList:any, data:any) => {
  // console.log(accountList, id)
  // dialogData.value.sendinfo = accountList
  // dialogShow.value = true
  openDialog({ sendinfo: accountList, ...data })
  let api = props.type == 'video' ? sendVideoApi : sendArticleApi
  for(let i = 0; i < accountList.length; i++) {
    let item = accountList[i]
    api({ log_id: data.id, id: item.id }).then((res:any) => {
      console.log(res)
      checkList.value[i].percent = 100
      checkList.value[i].upStatus = 2
      statistics.value.successNum++
    }).catch((err:any) => {
      console.log(err, 'err')
      checkList.value[i].percent = 100
      checkList.value[i].upStatus = 1
      checkList.value[i].upMsg = err
      statistics.value.errorNum++
    })

  }
} 

defineExpose({ openDialog, sendInfo })
</script>

<style scoped lang="scss">
.up-status {
  .up-title {
    font-weight: bold;
    color: #1C2B4B;
  }
  .up-status-all {
    font-size: 14px;
    color: #7F8294;
    margin-top: 8px;
  }
  .up-status-all {
    i {
      font-style: normal;
      &.success {
        color: #3BB034;
      }
      &.fail {
        color: #F10000;
      }
      &.releasing {
        color: #2859FF;
      }
    }
    span {
      color: #1C2B4B;
    }
    span + span {
      margin-left: 14px;
    }
  }
  
  .up-status-list {
    display: flex;
    flex-wrap: wrap;
    margin-top: 24px;
    max-height: 320px;
    overflow-y: auto;
    .up-status-item {
      width: 100%;
      margin-bottom: 24px;
      border: 1px solid #E3E5EC;
      padding: 12px 15px;
      box-sizing: border-box;
      .left-info {
        width: calc(100% - 150px);
      }
      .avatar {
        width: 56px;
        height: 56px;
        background: #fff;
        border-radius: 6px;
        margin-right: 10px;
        border: 1px solid #E3E5EC;
        flex: 0 0 56px;
      }
      .info-box {
        width: calc(100% - 66px);
      }
      .info-user {
        font-size: 14px;
        font-weight: bold;
        color: #1C2B4B;
      }
      .info-type {
        font-size: 14px;
        color: #B6B8C5;
        margin-top: 5px;
      }
      .right-info {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        .up-time {
          font-size: 14px;
          color: #B6B8C5;
          margin-top: 10px;
        }
      }
      .up-tips {
        font-size: 14px;
        font-weight: bold;
        color: #3BB034;
        &.green {
          color: #3BB034;
        }
        &.red {
          color: #F10000;
        }
        &.blue {
          color: #2859FF;
        }
      }
      .up-msg {
        font-size: 14px;
        color: #B6B8C5;
        margin: 0 0 4px 68px;
        width: calc(100% - 66px);
      }
      .up-progress {
        width: 80px;
        height: 4px;
        background: #D8E0EA;
        border-radius: 2px;
        margin-top: 5px;
        position: relative;
        span {
          width: 40%;
          height: 4px;
          background: #2859FF;
          border-radius: 2px;
          position: absolute;
          left: 0;
          top: 0;
          transition: width ease-in-out 0.3s;
        }
      }
    }
  }
}
</style>