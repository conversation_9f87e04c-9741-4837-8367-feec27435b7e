<script setup lang="ts">
import {nextTick, ref, watch} from "vue";
import * as echarts from "echarts";

interface Props {
  colors: string,
  width: string,
  height: string,
  data: any[],
  legend?: any,
}
const props = withDefaults(defineProps<Props>(), {
  colors: '#3583FB',
  width: '100%',
  height: '100%',
  data: () => ([]),
  legend: () => ({})
})

const barChartDom = ref(null)
const chart = ref<any>(null)

const initChart = () => {
  if (!barChartDom.value) return
  chart.value = echarts.init(barChartDom.value)
  const option = {
    grid: {
      left: '3%',
      right: '4%',
      top: '20px',
      bottom: props.legend.show ? '80px' : '40px',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(229, 231, 235, 1)',
        },
      },
      axisLabel: {
        color: 'rgba(102, 102, 102, 1)'
      },
      axisTick: {
        show: false,
      },
    },
    yAxis: {
      type: 'category',
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(229, 231, 235, 1)',
        },
      },
      axisLabel: {
        color: 'rgba(102, 102, 102, 1)'
      },
      axisTick: {
        show: false,
      }
    },
    dataset: {
      source: props.data,
    },
    series: {
      name: props.legend.data[0],
      type: 'bar',
      smooth: true,
      symbolSize: 0,
      itemStyle: {
        color: props.colors,
      },
      barWidth: 20
    },
    tooltip: {
      trigger: 'axis',
      show: true,
    },
    legend: {
      show: true,
      icon: 'rect',
      itemWidth: 8,
      itemHeight: 8,
      align: 'auto',
      bottom: 30,
      type: 'plain',
      itemGap: 50,
      ...props.legend
    },
  }
  chart.value.setOption(option)
}

watch(() => props.data, () => {
  nextTick(() => {
    if (chart.value) {
      chart.value.dispose();
      chart.value.clear()
    }
    console.log(props.legend, 'data');
    initChart()
  })
}, {immediate: true, deep: true})
</script>

<template>
  <div ref="barChartDom" :style="`width: ${width}; height: ${height};`"></div>
</template>

<style scoped lang="scss">

</style>
