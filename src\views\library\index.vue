<template>
  <div class="h100 flex-y">
    <page-menu v-if="navList.length" :list="navList" @click-item="navItemClick" :activeIndex="activeIndex" />
    <div class="flex-1">
      <component ref="currentComponentRef" :is="currentComponent" @changeNav="changeNav" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, defineAsyncComponent, computed } from 'vue'

const BucketView = defineAsyncComponent(() => import('./components/Bucket.vue'))
const Overview = defineAsyncComponent(() => import('./components/Overview.vue'))
const ShareManager = defineAsyncComponent(() => import('./components/ShareManager.vue'))

const components:any = {
  'bucket': BucketView,
  'overview': Overview,
  'share-manager': ShareManager
}

const currentComponent = computed(() => {
  return components[navList.value[activeIndex.value].key]
})

const navList = ref([
  { title: '素材', key: 'bucket' },
  { title: '总览', key: 'overview' },
  { title: '共享云经理', key: 'share-manager', hidden: true }
])

const activeIndex = ref(0)

const navItemClick = (item: any, index: number) => {
  console.log(item, index)
  activeIndex.value = index
}

const changeNav = (row: any) => {
  const index = navList.value.findIndex((item: any) => item.key === row.key)
  if (index !== -1) {
    activeIndex.value = index
  }
}

</script>

<style lang="scss" scoped>

</style>