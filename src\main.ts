import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'

// 引入全局样式
import "@/assets/iconfont/iconfont.js";
import '@/assets/iconfont/iconfont.css'
import '@/assets/css/app.scss'

const app = createApp(App)

// 引入element-plus
import ElementPlus from 'element-plus'
import zhCn from 'element-plus/es/locale/lang/zh-cn' // 汉化
import * as ElementPlusIconsVue from "@element-plus/icons-vue"; // 图标
// 引入element图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}
app.use(ElementPlus, {locale: zhCn})


//全局注册自定义按钮组件
import JztButton from "@/components/Global/JztButton.vue";
app.component("JztButton", JztButton);

// 页面三级菜单
import PageMenu from "@/components/Global/PageMenu.vue";
app.component("PageMenu", PageMenu);

// 页面主要内容
import ContentMain from "@/views/content/components/main.vue"
app.component("ContentMain", ContentMain);

app.use(router)
app.use(store)
app.mount('#app')
