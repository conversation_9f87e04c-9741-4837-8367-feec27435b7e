<script setup lang="ts">
import {getUpdateLogApi} from "@/api/system";
import {ref} from "vue";
import LogItem from "@/views/system/components/LogItem.vue";

const contentList = ref<any>([])
// 获取更新日志
const getUpdateLog = async () => {
  contentList.value = await getUpdateLogApi()
}
getUpdateLog();
</script>

<template>
<div class="wrap h100">
  <log-item v-for="(item, index) in contentList" :key="index" :item="item" />
</div>
</template>

<style scoped lang="scss">
.wrap{
  margin-top: 10px;
  overflow: auto;
}
</style>