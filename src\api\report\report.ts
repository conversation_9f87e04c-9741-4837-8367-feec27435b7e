import http from '@/utils/request'

// 获取素材库报告
export const getMaterialLibraryReport = (params: any) => {
  return http.request({
    url: '/WebsiteUsageReport/material',
    method: 'get',
    params
  })
}

// 获取访问数据报告
export const getVisitDataReport = (params: any) => {
  return http.request({
    url: '/WebsiteUsageReport/visit',
    method: 'get',
    params
  })
}

// 获取网站内容报告
export const getWebsiteContentReport = (params: any) => {
  return http.request({
    url: '/WebsiteUsageReport/webcontent',
    method: 'get',
    params
  })
}

// 获取媒体平台报告
export const getMediaPlatformReport = (params: any) => {
  return http.request({
    url: '/WebsiteUsageReport/media',
    method: 'get',
    params
  })
}

