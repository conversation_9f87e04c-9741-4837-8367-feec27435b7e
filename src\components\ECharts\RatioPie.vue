<script setup lang='ts'>
import { ref, watch, nextTick } from "vue";
import * as echarts from "echarts";

const props = defineProps({
  data: {
    type: Object,
    default: {}
  },
  QDColor: {
    type: Array,
    default: []
  },
  title: {
    type: String,
    default: ''
  },
  width: {
    type: String,
    default: '180px'
  },
  height: {
    type: String,
    default: '150px'
  }
})

// 初始化占比图表
const ratioRef = ref(null);
const spaceInit = data => {
  nextTick(() => {
    let chartDom = ratioRef.value
    var myChart = echarts.init(chartDom)
    var option;

    option = {
      color: props.QDColor,
      title: [{
        text: props.title,
        left: '44%',
        top: '42%',
        textAlign: 'center',
        textStyle: {
          fontSize: '12',
          fontWeight: '500',
          color: '#1C2B4B',
          textAlign: 'center',
        },
      }],
      series: [
        {
          name: props.title,
          type: 'pie',
          radius: ['60%', '80%'],
          center: ['45%', '48%'],
          // roseType: 'area',
          itemStyle: {
            borderRadius: 0
          },
          label: {
            show: false,
          },
          data: data
        }
      ]
    };

    option && myChart.setOption(option);
  });
}

watch(
  () => props.data,
  () => {
    spaceInit(props.data)
  }
)
</script>

<template>
  <div>
    <div ref="ratioRef" :style="`width: ${width}; height: ${height};`"></div>
  </div>
</template>

<style scoped lang='scss'></style>
