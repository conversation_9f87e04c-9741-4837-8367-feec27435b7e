<script setup lang='ts'>
  import { computed, onMounted, ref } from 'vue'
  import { uploadSslApi } from "@/api/upload"
  import store from '@/store';
  import { ElMessage } from 'element-plus';
  import { getSiteInfoApi } from '@/api/site';

  const currentSite = computed(() => store.getters.siteInfo)
  const siteId = computed(() => currentSite.value?.id)

  let uploadInput = ref<any>('uploadInput') // 上传文件ref
  let fileList: any = ref([]) // 当前上传文件
  let isChooseFile = false

  const password = ref('')

  // 监听上传
  const changeInput = (files: any) => {
    files = files.target.files[0]
    console.log(files)
    if (files) {
      if (files.type == 'application/x-pkcs12') {
        fileList.value = [files]
        isChooseFile = true
      } else {
        uploadInput.value = ''
        ElMessage.error("请上传pfx类型的文件");
      }
    }else{
      isChooseFile = false
      uploadInput.value = ''
    }
  }

  // 点击上传
  const submitUpload = () => {
    if (isChooseFile) {
      let formInfo = new FormData()
      formInfo.append('file', fileList.value[0])
      if (password.value) formInfo.append('password', password.value)
      uploadSslApi(formInfo)
        .then(({ data }: any) => {
          console.log(data)
          ElMessage.success('上传成功');
        })
    } else {
      uploadInput.value.click()
    }
  }

  const getSslInfo = () => {
    getSiteInfoApi().then((res: any) => {
      if (res.ssl_src) {
        fileList.value = [{ name: res.ssl_src }]
        isChooseFile = true
      } else {
        fileList.value = []
        isChooseFile = false
      }
      // 密码没存，不回显了
      /* if (res.password) {
        password.value = res.password
      } else {
        password.value = ''
      } */
    })
  };

  onMounted(() => {
    getSslInfo();
  });
</script>

<template>
  <div class="info-add content-box">
    <div class="flex upload-box">
      <label class="el-form-item__label">SSL证书文件（.pfx）：</label>
      <div style="width: 40%; margin-right: 10px;">
        <div class="check-file" id="checkFile" @click="fileList = []">
          {{ fileList[0]?.name ? fileList[0].name : '点击选择文件' }}
          <input type="file" name="file" @change="changeInput" class="input-file" ref="uploadInput" />
        </div>
        <div class="el-upload__tip">* 注：文件名称不能以中文命名。</div>
      </div>
      <div style="width: 240px; margin-right: 10px;">
        <el-input v-model="password" :placeholder="`请输入证书密码，没有请忽略`" type="text" />
      </div>
      <jzt-button name="上传" size="large" icon="icon-shangchuan2" @click="submitUpload"></jzt-button>
    </div>
  </div>
</template>

<style scoped lang='scss'>
  .info-add {
    margin-top: 10px;
    height: auto !important;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    &.content-box {
      background: rgba(255, 255, 255, 0.99);
      box-shadow: 0px 6px 12px 0px rgba(216, 224, 234, 0.2);
      padding: 20px 30px;
      width: 100%;
      box-sizing: border-box;
    }

    .upload-box {
      align-items: flex-start;

      .upload-demo {
        width: calc(100% - 180px);
        display: flex;

        :deep(.el-upload) {
          width: 60%;
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          margin-right: 10px;
        }
      }
    }
  }

  .check-file {
    width: 100%;
    color: #B5B7C4;
    font-size: 14px;
    line-height: 34px;
    height: 34px;
    background: #FFFFFF;
    border: 1px solid #E3E5EC;
    border-radius: 3px;
    padding: 0 13px;
    cursor: pointer;
    text-align: left;
    position: relative;

    .input-file {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      opacity: 0;
      cursor: pointer;
    }
  }
</style>
