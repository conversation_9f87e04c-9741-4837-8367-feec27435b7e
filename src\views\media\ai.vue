<template>
  <div class="ai-container h100">
    <iframe ref="chatgptRef" id="chatgpt" :src="`${baseUrl}chatgpt`"></iframe>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import store from '@/store'

const baseUrl = process.env.VUE_APP_GPT_BASE_URL
const chatgptRef = ref()

const isAgent = computed(() => store.getters.isAgent)

onMounted(() => {
  if (chatgptRef.value) {
    chatgptRef.value.onload = () => {
      console.log('chatgptRef loaded')
      chatgptRef.value.contentWindow && chatgptRef.value.contentWindow.postMessage(isAgent.value)
    }
  }
})
</script>

<style lang="scss" scoped>
.ai-container {
  padding: 20px;
  box-sizing: border-box;
  iframe {
    width: 100%;
    height: 100%;
    border: none;
  }
}
</style>