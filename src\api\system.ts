import http from '@/utils/request'

// 获取更新日志
export const getUpdateLogApi = (params?: any, config: any = {}) => {
  return http.request({
    url: 'SystemInfo/update_log',
    ...config,
    method: 'post',
    // data: params
  })
}

// 获取回收站列表
export const getRecycleListApi = (params?: any, config: any = {}) => {
  return http.request({
    url: '/Recycle/lst',
    ...config,
    method: 'get',
    params
  })
}

// 还原回收站
export const restoreRecycleApi = (params?: any, config: any = {}) => {
  return http.request({
    url: '/Recycle/recover',
    ...config,
    method: 'post',
    data: params
  })
}

// 彻底删除回收站
export const deleteRecycleApi = (params?: any, config: any = {}) => {
  return http.request({
    url: '/Recycle/del',
    ...config,
    method: 'post',
    data: params
  })
}
