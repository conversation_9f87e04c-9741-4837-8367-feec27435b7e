<template>
  <div class="info-add flex-y w100 h100">
    <content-tips class="mb10"></content-tips>
    <div class="flex-1 content-box h100">
      <div class="content-wrap flex-x w100 h100">
        <div class="left-form-box flex-1 flex-x">
          <content-view ref="contentViewRef" class="flex-1" :type="type" :form="rowData" :checkList="checkList" @success="success"></content-view>
          <ai-chat class="ml20"></ai-chat>
        </div>
        <div class="right-account-box ml20">
          <account-select :type="type" @save="saveInfo" @send="sendInfo" @cancel="cancelInfo" v-model:checkList="checkList"></account-select>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineAsyncComponent, ref } from 'vue'

const ContentTips = defineAsyncComponent(() => import('@/components/ContentTips.vue'))
const ContentView = defineAsyncComponent(() => import('./ContentView.vue'))
const AiChat = defineAsyncComponent(() => import('./AiChat.vue'))
const AccountSelect = defineAsyncComponent(() => import('./AccountSelect.vue'))

const contentViewRef = ref()

const props = defineProps({
  type: {
    type: String,
    default: 'video'
  }
})

const emit = defineEmits(['changeNav', 'success'])

const checkList = ref([])

// 取消
const cancelInfo = () => {
  emit('changeNav', { key: 'list' })
}

// 保存
const saveInfo = () => {
  contentViewRef.value.saveInfo()
}

// 发布
const sendInfo = (data:any) => {
  contentViewRef.value.sendInfo(data)
}

// 成功
const success = () => {
  emit('changeNav', { key: 'list' })
  emit('success')
}

const rowData = ref(null)
const setData = (e:any) => {
  console.log('设置数据', e)
  const { data } = e
  if (data) rowData.value = data
  else rowData.value = null
}

defineExpose({ setData })
</script>

<style scoped lang="scss">
.info-add {
  .content-box {
    overflow: auto;
    .content-wrap {
      overflow: auto;
      // .left-form-box {
        // min-width: 1110px;
      // }
    }
  }
}
</style>