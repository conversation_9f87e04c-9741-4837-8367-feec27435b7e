$mainColor: var(--jzt-color-main);
$borderColor: #A0B6FF;   //按钮边框颜色
.info-add {
  margin-top: 10px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .content-box {
    background: rgba(255, 255, 255, 0.99);
    box-shadow: 0px 6px 12px 0px rgba(216, 224, 234, 0.2);
    padding: 20px 30px;
    width: 100%;
    box-sizing: border-box;
  }
}

// 弹窗样式
.jzt-open-box {
  position: fixed;
  width: 100vw;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: 2001;

  .jzt-open-shade {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.2);
  }

  .jzt-open-main {
    width: 60%;
    height: 250px;
    padding: 20px 40px;
    background: #FFFFFF;
    border-radius: 6px;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    z-index: 102;
    box-sizing: border-box;

    .jzt-open-close {
      position: absolute;
      top: 20px;
      right: 20px;
      cursor: pointer;

      .icon-guanbi {
        color: #999;
        font-size: 14px;
      }
    }

    .jzt-open-title {
      font-size: 18px;
      font-family: PingFang SC;
      font-weight: bold;
      color: #1C2B4B;
      text-align: center;
      margin-top: 12px;
      margin-bottom: 24px;
    }
    .jzt-open-content {

      &>.btn-box {
        flex-direction: row;
        justify-content: center;
        margin-top: 30px;
        margin-left: 0;
      }
    }
  }
}