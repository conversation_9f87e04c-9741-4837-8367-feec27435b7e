<template>
  <edit-dialog ref="editDialogRef" v-model:dialogShow="dialogShow" v-bind="formConfig" @cancel="cancel" @sure="toAddImage">
    <!-- 切换 原图上传 图片处理 -->
    <div class="switch-wrap mb20">
        <el-radio-group v-model="switchType" class="switch-group" @change="changeType">
          <el-radio-button :value="1">原图上传</el-radio-button>
          <el-radio-button :value="2">图片处理</el-radio-button>
        </el-radio-group>
      </div>
    <div class="single-wrap flex-x">
      <div class="left-wrap flex-y">
        <div class="img-wrap flex-1 flex-y">
          <el-image v-if="switchType == 1" :src="imgUrl" class="w100 h100" fit="scale-down" />
          <image-cropper v-else :imgUrl="imgUrl" :file="dialogData" @changePreview="changePreview" @changePreviewFile="changePreviewFile" />
        </div>
      </div>
      <div class="right-wrap flex-1 flex-y">
        <p class="r-title mb10">预览</p>
        <div class="preview-wrap">
          <el-image v-if="switchType == 1" :src="imgPreview" class="w100 h100" fit="scale-down" />
          <el-image v-else :src="imgPreview" class="w100 h100" fit="scale-down" />
          <!-- <div v-else class="crop-preview-box" :style="previewStyle">
            <div ref="cropPreviewRef" class="crop-preview" :style="previews.div">
              <img :src="previews.url" :style="previews.img" />
            </div>
          </div> -->
        </div>
        <div class="flex mt20">
          <p class="r-title mr10">是否添加水印</p>
          <el-switch v-model="formData.watermark" />
        </div>
        <div v-if="formData.watermark" class="flex mt20">
          <p class="r-title mr10">水印内容</p>
          <el-input class="flex-1" v-model="formData.watermarkText" placeholder="请输入文字（不超过10个字符）" :maxLength="10" />
        </div>
        <!-- <div class="flex-x mt20">
          <p class="r-title mr10">智能分类推荐</p>
          <div v-loading="aiCateLoading" element-loading-background="rgba(255, 255, 255, 1)" class="cate-list">
            <template v-if="aiCateList.length">
              <div class="cate-item" v-for="(item, index) in aiCateList">
                {{ item.name }}
              </div>
            </template>
            <template v-else>
              <div class="r-title">暂无推荐</div>
            </template>
          </div>
        </div> -->
        <div class="flex-x mt20">
          <p class="r-title mr10">选择栏目分类</p>
          <div v-loading="siteLoading" element-loading-background="rgba(255, 255, 255, 1)" class="cate-list">
            <template v-if="siteNavList.length">
              <div :class="['cate-item', {'active': item.active}]" v-for="(item, index) in siteNavList" @click="siteNavCheck(index)">
                {{ item.title }}
              </div>
            </template>
            <template v-else>
              <div class="r-title">暂无推荐</div>
            </template>
          </div>
        </div>
        <div class="flex-x mt20">
          <p class="r-title mr10">选择其他分类</p>
          <div class="cate-list">
            <div class="cate-item active cate-del" v-for="(item, index) in siteCateList">
              <span v-html="item.title"></span>
              <i class="iconfont icon-shanchu" @click="delCate(index)" />
            </div>
            <div class="cate-add flex">
              <el-icon @click="addCate"><CirclePlus /></el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 全部分类 -->
    <all-cate ref="allCateRef" type="select" @selectCate="selectCate" />
  </edit-dialog>
</template>

<script setup lang="ts">
import { defineAsyncComponent, ref, watch } from 'vue'
import { CirclePlus } from '@element-plus/icons-vue'
import { ElLoading, ElMessage } from 'element-plus'
import { useDialog } from '@/hooks/useDialog'
import { uploadImgApi, getImgBaidu, getSiteNavAndCateApi } from '@/api/bucket'

const { EditDialog, editDialogRef, dialogShow, dialogData, openDialog, closeDialog } = useDialog()

const ImageCropper = defineAsyncComponent(() => import('./ImageCropper.vue'))
const AllCate = defineAsyncComponent(() => import('@/views/library/components/Bucket/AllCate.vue'))

const allCateRef = ref()

const props = defineProps({
  folder: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['cancel', 'success'])

/* 单图上传相关 */
const formConfig = ref({
  title: '图片处理',
  width: '80%',
  sureText: '添加',
  customForm: true,
})

const imgUrl:any = ref('')
watch(dialogData, (newVal) => {
  if (newVal) {
    // getAiCate(dialogData.value)
    getSiteNavAndCate(dialogData.value)
    var imgFile = new FileReader();
    imgFile.readAsDataURL(dialogData.value);
    imgFile.onload = function () {
      imgUrl.value = imgFile.result;
      imgPreview.value = imgFile.result;
    }
  }
})

const cancel = () => {
  imgUrl.value = ''
  imgPreview.value = ''
  dialogData.value = ''
  switchType.value = 1
  emit('cancel')
}

const switchType = ref(1)
const changeType = (val:any) => {
  if (val == 1) {
    imgPreview.value = imgUrl.value
  }
}

const imgPreview:any = ref('')
const imgPreviewFile:any = ref('')
const previews:any = ref({})
const previewStyle = ref({})
const changePreview = (val:any) => {
  imgPreview.value = val
  // previews.value = val
  // previewStyle.value = {
  //   width: val.w + "px",
  //   height: val.h + "px",
  //   overflow: "hidden",
  //   margin: "0",
  //   zoom: 128 / val.h
  // }
}
const changePreviewFile = (val:any) => {
  imgPreviewFile.value = val
}

const formData = ref({
  watermark: false,
  watermarkText: ''
})

const aiCateList:any = ref([])
const aiCateLoading = ref(false)
// 获取当前图片的智能分类（百度）
const getAiCate = (file: any) => {
  const formData = new FormData();
  formData.append('file', file);
  aiCateLoading.value = true
  getImgBaidu(formData).then((res:any) => {
    const { apiinfo } = res
    if (apiinfo) {
      let list = apiinfo.map((item:any) => {
        return {
          name: item,
          active: false
        }
      })
      aiCateList.value = list
    }
  }).finally(() => {
    aiCateLoading.value = false
  })
}

const siteNavList:any = ref([])
const siteCateList:any = ref([{ id: 0, title: '全部分类' }])
const siteLoading = ref(false)
// 获取站点分类
const getSiteNavAndCate = (file: any) => {
  siteLoading.value = true
  let formData = new FormData();
  formData.append('file', file);
  getSiteNavAndCateApi(formData).then((res:any) => {
    siteNavList.value = res.menus.map((item:any) => {
      return {
        ...item,
        active: false
      }
    })
  }).finally(() => {
    siteLoading.value = false
  })
}

// 选择栏目分类
const siteNavCheck = (index: number) => {
  siteNavList.value[index].active = !siteNavList.value[index].active
}

// 添加分类
const addCate = () => {
  allCateRef.value.openDialog()
}
// 选择栏目分类
const selectCate = (e: any) => {
  console.log(e, '选择的分类')
  const list = e.filter((item: any) => item.id != 0)
  siteCateList.value.push(...list)
  allCateRef.value.closeDialog()
}

// 删除分类
const delCate = (index:any) => {
  siteCateList.value.splice(index, 1)
}

// 确定添加
const toAddImage = () => {
  let formData = new FormData();
  if (switchType.value == 1) {
    formData.append('image', dialogData.value);
  } else {
    formData.append('image', imgPreviewFile.value);
  }
  formData.append('type', 'goods_image_file')
  formData.append('file_key', 'image');
  formData.append('f_id', props.folder + '');
  formData.append('aip_title', '');
  let menusList = siteNavList.value.filter((item:any) => item.active)
  if (menusList.length) {
    formData.append('column_title', menusList.map((item:any) => item.title).join(','));
  } else {
    formData.append('column_title', '');
  }
  if (siteCateList.value.length) {
    formData.append('classid', siteCateList.value.map((item:any) => item.id).join(','));
  } else {
    formData.append('classid', '');
  }
  let loading = ElLoading.service({
    lock: true,
    text: '上传中',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  uploadImgApi(formData).then((res:any) => {
    ElMessage.success('上传成功')
    emit('success')
    closeDialog()
  }).finally(() => {
    loading.close()
  })
}

defineExpose({ openDialog })
</script>

<style scoped lang="scss">
.single-wrap {
  height: 60vh;
  .left-wrap {
    width: 50%;
    // border: 1px solid #E3E5EC;
  }
  .right-wrap {
    margin-left: 30px;
    .r-title {
      font-size: 14px;
      width: 100px;
      line-height: 28px;
    }
    .preview-wrap {
      width: 130px;
      height: 130px;
      background: #f5f5f5;
      border: 1px solid #E3E5EC;
      border-radius: 3px;
    }
    .cate-list {
      display: flex;
      flex-wrap: wrap;
      .cate-item {
        height: 28px;
        line-height: 26px;
        background: #FFFFFF;
        border: 1px solid #E3E5EC;
        border-radius: 2px;
        box-sizing: border-box;
        padding: 0 17px;
        font-size: 14px;
        color: #696C7D;
        margin-right: 14px;
        margin-bottom: 10px;
        cursor: pointer;
        position: relative;
        &.active {
          color: #2859FF;
          border-color: #97BFFF;
        }
        &.cate-del {
          .icon-shanchu {
            position: absolute;
            right: -10px;
            top: -10px;
            color: rgba(0, 0, 0, 0.3);
            cursor: pointer;
          }
        }
      }
      .cate-add {
        height: 28px;
        line-height: 28px;
        font-size: 18px;
        color: #2859FF;
        cursor: pointer;
      }
      :deep(.el-loading-spinner) {
        width: auto;
        top: 0;
        bottom: 0;
        margin: auto;
        .circular {
          width: 20px;
          height: 20px;
        }
      }
    }
  }
}
</style>