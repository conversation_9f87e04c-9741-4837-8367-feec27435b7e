<script setup lang="ts">
import {ref, onMounted, watch, computed} from "vue";
import { ElMessage } from "element-plus";
import { saveAs } from "file-saver";
import { asBlob } from 'html-docx-js-typescript';//将html转为word
import {await<PERSON>andle} from "@/utils";
import {exportPDF} from "@/utils/htmlToPdf";
import VisitLine from "@/components/ECharts/VisitLine.vue";
import RatioPie from "@/components/ECharts/RatioPie.vue";
import {
  getMaterialLibraryReport,
  getMediaPlatformReport,
  getVisitDataReport,
  getWebsiteContentReport
} from "@/api/report/report";
import store from "@/store";
import PdfPage from "@/views/media/components/PdfPage.vue";

let QDColor = ["#0012BF", "#2859FF", "#46C4FF", "#B4A5FF", "#15FFFC"];
const siteInfo = computed(() => store.getters.siteInfo);
let dataInfo: any = ref({
  colorList: QDColor,
  btnList: [
    {
      name: "昨日统计",
      value: "day"
    },
    {
      name: "按月统计",
      value: "month"
    },
    {
      name: "按年统计",
      value: "year"
    }
  ],
  btnIndex: 0,
  siteId: null,
  siteInfo: {} as any,
  visitType: 'day',
  visitValue: "",
  rangeDate: "",
  nowVisitData: {} as any,
  libCate: [],
  contentCate: [],
  mediaCate: []
});
const libAll = ref<any>({});
const visitAll = ref<any>({});
const contentAll = ref<any>({});
const mediaAll = ref<any>({});
const apiList = {
  material: getMaterialLibraryReport,
  visit: getVisitDataReport,
  webContent: getWebsiteContentReport,
  media: getMediaPlatformReport
};

const getCanvasInfo = () => {
  /*素材库*/
  getCensusData("material");
  /*访问统计*/
  getCensusData("visit");
  /*网站内容*/
  getCensusData("webContent");
  /*媒体平台*/
  getCensusData("media");
};

const getCanvasInfoAsync = async () => {
  /*素材库*/
  await getCensusData("material");
  /*访问统计*/
  await getCensusData("visit");
  /*网站内容*/
  await getCensusData("webContent");
  /*媒体平台*/
  await getCensusData("media");
};

// 时间筛选
const changeData = (index: number, value: any) => {
  dataInfo.value.btnIndex = index;
  dataInfo.value.visitType = value;
  dataInfo.value.visitValue = "";
  dataInfo.value.rangeDate = "";
  getCanvasInfo();
};

// 获取页面数据
const getCensusData = async (url: "material" | "visit" | "webContent" | "media") => {
  let type = dataInfo.value.visitType,
      value = dataInfo.value.visitValue;
  if (type == 5) value = dataInfo.value.rangeDate[0] + " ~ " + dataInfo.value.rangeDate[1];
  let datas = {
    datetype: type,
    datedate: value
  };
  const res = await awaitHandle(apiList[url](datas));
  if (res[1]) {
    let data = res[1];
    console.log(data);
    switch (url) {
      case "material":
        libAll.value = data;
        dataInfo.value.libCate = data.data;
        break;
      case "visit":
        visitAll.value = {
          datetime: data.datetime,
          data: [
            { name: "浏览量", list: data.pvlist },
            { name: "访客量", list: data.iplist },
            // { name: "IP", list: data.iplist }
          ]
        };
        dataInfo.value.nowVisitData = data;
        break;
      case "webContent":
        contentAll.value = data;
        dataInfo.value.contentCate = data.data;
        break;
      case "media":
        mediaAll.value = data;
        dataInfo.value.mediaCate = data.data;
        break;
    }
  }
};

const disabledDate = (time: Date) => {
  return time.getTime() > Date.now();
};

// 时间段选择
const rangeChange = (val: string[]) => {
  if (val) {
    dataInfo.value.visitValue = "";
  }

  // console.log(val)
  const s = new Date(val[0]).getTime();
  const e = new Date(val[1]).getTime();
  //计算两个时间间隔天数
  const d = (e - s) / (1000 * 60 * 60 * 24);
  // console.log(d, 'd');
  if (d > 31) {
    // layer.msg('最多选择31天', { icon: 5 });
    ElMessage.error("最多选择31天");
  } else {
    dataInfo.value.rangeDate = val;
    dataInfo.value.btnIndex = 4;
    dataInfo.value.visitType = 5;
    getCanvasInfo();
  }
};

const changeValue = (val: string) => {
  if (val) {
    dataInfo.value.rangeDate = "";
  }
};

const exportData = ref({
  month: {
    libCate: [],
    libAll: {
      data: [],
      datetime: []
    },
    nowVisitData: {
      pv_count: 0,
      ip_count: 0
    },
    visitAll: {
      data: [],
      datetime: []
    },
    contentCate: [],
    contentAll: {
      data: [],
      datetime: []
    },
    mediaCate: [],
    mediaAll: {
      data: [],
      datetime: []
    }
  },
  year: {
    libCate: [],
    libAll: {
      data: [],
      datetime: []
    },
    nowVisitData: {
      pv_count: 0,
      ip_count: 0
    },
    visitAll: {
      data: [],
      datetime: []
    },
    contentCate: [],
    contentAll: {
      data: [],
      datetime: []
    },
    mediaCate: [],
    mediaAll: {
      data: [],
      datetime: []
    }
  },
})
const getExportData = async () => {
  dataInfo.value.visitType = 'month';
  await getCanvasInfoAsync()
  exportData.value.month = {
    contentAll: contentAll.value,
    contentCate: dataInfo.value.contentCate,
    libAll: libAll.value,
    mediaAll: mediaAll.value,
    mediaCate: dataInfo.value.mediaCate,
    libCate: dataInfo.value.libCate,
    nowVisitData: dataInfo.value.nowVisitData,
    visitAll: visitAll.value
  }
  dataInfo.value.visitType = 'year';
  await getCanvasInfoAsync()
  exportData.value.year = {
    contentAll: contentAll.value,
    contentCate: dataInfo.value.contentCate,
    libAll: libAll.value,
    mediaAll: mediaAll.value,
    mediaCate: dataInfo.value.mediaCate,
    libCate: dataInfo.value.libCate,
    nowVisitData: dataInfo.value.nowVisitData,
    visitAll: visitAll.value
  }
}

const exportType = ref<'pdf' | 'word'>('pdf');
const exportPdf = async () => {
  exportType.value = 'pdf';
  await getExportData();
  setTimeout(() => {
    exportPDF('网站使用报告', 'data-page')
  }, 1000);
};

const pdfWidth = "calc(841.89px * 1.5)";
const pdfHeight = "calc(595.28px * 1.5)";

const style: {pdf: any, word: any} = {
  pdf: {
    title: 'margin-top: -40px;text-align: center;',
    name: 'text-align: center;margin-bottom: 0',
    chartTitle: ''
  },
  word: {
    title: 'margin-top: 200px;text-align: center;',
    name: 'text-align: center;margin-bottom: 3000px;',
    chartTitle: 'line-height: 2pt;'
  }
}

const canvasToImg = () => {
  let parentEle = document.getElementById("data-page") as HTMLElement;
  // 找出parentEle中的canvas
  let canvas = parentEle.querySelectorAll("canvas");
  canvas.forEach((item: any) => {
    // 在imgDom添加一个父元素
    let imgDomParent = document.createElement("p");
    // 设置imgDomParent的行内样式
    imgDomParent.style.cssText = 'margin: 80px 0 200px;';
    let img = item.toDataURL("image/png");
    let imgDom = document.createElement("img");
    imgDom.src = img;
    imgDom.width = 900;
    // 把imgDom添加到imgDomParent
    imgDomParent.appendChild(imgDom);
    //   在item后添加imgDom
    item.after(imgDomParent);
  });
};
const exportWord = async () => {
  exportType.value = 'word';
  await getExportData();
  setTimeout(async () => {
    canvasToImg()
    let content = document.getElementById("data-page")?.innerHTML;
    console.log(content);
    var content_ = `<!DOCTYPE html>
    <html>
        <head>
            <meta charset="UTF-8">
        </head>
      <body>
          ${content}
      </body>
    </html>`;
    const converted: any = await asBlob(content_, {
      orientation: "landscape",
      margins: {top: 720, left: 720, right: 720, bottom: 720}
    });
    // 移除#data-page下所有的img
    let parentEle = document.getElementById("data-page") as HTMLElement;
    let imgs = parentEle.querySelectorAll("img");
    imgs.forEach((item: any) => {
      item.remove();
    });
    saveAs(converted, "网站使用报告.docx"); // 用 FielSaver.js里的保存方法 进行输出
  }, 100)
};

watch(
    () => dataInfo.value.visitValue,
    val => {
      if (val) {
        dataInfo.value.btnIndex = 3;
        dataInfo.value.visitType = 4;
        getCanvasInfo();
      }
    }
);

onMounted(() => {
  getCanvasInfo();
});
</script>

<template>
  <div class="h100" style="overflow: hidden">
    <div class="h100">
      <div class="statistics-top flex">
        <div class="btn-box flex">
          <template v-for="(item, index) in dataInfo.btnList" :key="index">
            <div
                :class="['item', { active: dataInfo.btnIndex == index }]"
                @click="changeData(index, item.value)"
            >
              {{ item.name }}
            </div>
          </template>
          <!-- <el-date-picker v-model="dataInfo.visitValue" prefix-icon="Calendar" type="date" placeholder="请选择日期" /> -->
          <el-date-picker
              style="width: 160px; margin-right: 10px"
              v-model="dataInfo.visitValue"
              prefix-icon="Calendar"
              format="YYYY-MM"
              value-format="YYYY-MM"
              type="month"
              placeholder="请选择日期"
              :disabled-date="disabledDate"
              @change="changeValue"
          />
          <el-date-picker
              style="width: 240px"
              v-model="dataInfo.rangeDate"
              prefix-icon="Calendar"
              type="daterange"
              is-range
              range-separator="~"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              @change="rangeChange"
              :disabled-date="disabledDate"
          />
        </div>
        <div class="flex">
          <jzt-button
              :name="`导出当前页pdf报告`"
              style="margin-right: 10px"
              @click="exportPdf"
          ></jzt-button>
           <jzt-button :name="`导出当前页word报告`" @click="exportWord()"></jzt-button>
        </div>
      </div>
      <div class="visits-box" id="main-dom">
        <!--素材库-->
        <div class="visit-box flex" id="daochu1one">
          <div class="visit-info lib">
            <div class="visit-title">素材库</div>
            <div class="e-box cake-box">
              <ratio-pie
                  title="素材库占比"
                  :QDColor="QDColor"
                  :data="dataInfo.libCate"
                  height="150px"
              ></ratio-pie>
            </div>
            <div class="visit-part01">
              <div
                  class="item flex"
                  v-for="(item, index) in dataInfo.libCate"
                  :key="index"
              >
                <i
                    :style="'background: ' + dataInfo.colorList[index] + ';'"
                ></i>
                <span class="key">{{ item.name }}</span>
                <span
                    class="value"
                    :style="'color: ' + dataInfo.colorList[index] + ';'"
                >{{ item.value }}M</span
                >
              </div>
            </div>
          </div>
          <div class="e-box">
            <visit-line
                :data="libAll"
                width="100%"
                height="300px"
                :isShowTitle="false"
                from="report"
            ></visit-line>
          </div>
        </div>
        <!--访问统计-->
        <div class="visit-box flex" id="daochu1two">
          <div class="visit-info">
            <div class="visit-title">访问数据</div>
            <div class="visit-part01">
              <div class="item flex">
                <span class="key">浏览量(pv)</span
                ><span class="value">{{ dataInfo.nowVisitData.pv_count }}</span>
              </div>
              <div class="item flex">
                <span class="key">访客量(uv)</span
                ><span class="value" style="color: #b3a3ff">{{
                  dataInfo.nowVisitData.ip_count
                }}</span>
              </div>
              <!-- <div class="item flex">
                <span class="key">IP数</span
                ><span class="value" style="color: #12cef0">{{
                  dataInfo.nowVisitData.ip_count
                }}</span>
              </div> -->
            </div>
          </div>
          <div class="e-box">
            <visit-line
                :data="visitAll"
                width="100%"
                height="300px"
                :isShowTitle="false"
                from="report"
            ></visit-line>
          </div>
        </div>
        <!--网站内容-->
        <div class="visit-box flex" id="daochu1three">
          <div class="visit-info lib">
            <div class="visit-title">网站内容</div>
            <div class="e-box cake-box">
              <ratio-pie
                  title="内容占比"
                  :QDColor="QDColor"
                  :data="dataInfo.contentCate"
                  height="150px"
              ></ratio-pie>
            </div>
            <div class="visit-part01">
              <div
                  class="item flex"
                  v-for="(item, index) in dataInfo.contentCate"
                  :key="index"
              >
                <i
                    :style="'background: ' + dataInfo.colorList[index] + ';'"
                ></i>
                <span class="key">{{ item.name }}</span>
                <span
                    class="value"
                    :style="'color: ' + dataInfo.colorList[index] + ';'"
                >{{ item.value }}</span
                >
              </div>
            </div>
          </div>
          <div class="e-box">
            <visit-line
                :data="contentAll"
                width="100%"
                height="320px"
                :isShowTitle="false"
                from="report"
            ></visit-line>
          </div>
        </div>
        <!--媒体平台-->
        <div class="visit-box flex" id="daochu1four">
          <div class="visit-info lib">
            <div class="visit-title">媒体平台</div>
            <div class="e-box cake-box">
              <ratio-pie
                  title="发布占比"
                  :QDColor="QDColor"
                  :data="dataInfo.mediaCate"
                  height="150px"
              ></ratio-pie>
            </div>
            <div class="visit-part01">
              <div
                  class="item flex"
                  v-for="(item, index) in dataInfo.mediaCate"
                  :key="index"
              >
                <i
                    :style="'background: ' + dataInfo.colorList[index] + ';'"
                ></i>
                <span class="key">{{ item.name }}</span>
                <span
                    class="value"
                    :style="'color: ' + dataInfo.colorList[index] + ';'"
                >{{ item.value }}</span
                >
              </div>
            </div>
          </div>
          <div class="e-box">
            <visit-line
                :data="mediaAll"
                width="100%"
                height="320px"
                :isShowTitle="false"
                from="report"
            ></visit-line>
          </div>
        </div>
      </div>
    </div>
    <!--PDF 导出-->
    <div
        id="data-page"
        :style="`background: #fff;width: ${pdfWidth}; margin-top: 200px;`"
    >
      <div
          class="page-1"
          :style="`width: ${pdfWidth};height: ${pdfHeight};display: flex;flex-direction: column;align-items: center;justify-content: center;page-break-after: always;`"
      >
        <h1 class="title" :style="`font-weight: bold; font-size: 52px; ${style[exportType].title}`">
          网站使用报告
        </h1>
        <h2 :style="`font-weight: bold; margin-top: 20px; font-size: 26px; ${style[exportType].name}`">
          网站名称：{{ siteInfo.web_name }}
        </h2>
      </div>
      <template v-for="(data, key) in exportData" :key="key">
        <pdf-page>
          <template #textInfo>
            <br>
            <p :style="`font-size: 18px; padding-bottom: 20px;${style[exportType].chartTitle}`">
              素材库
            </p>
            <div
                style="font-size: 18px; padding-bottom: 10px"
                v-for="(item, index) in data.libCate"
                :key="index"
            >
              {{ item.name }}：{{ item.value }}
            </div>
          </template>
          <template #canvas>
            <visit-line
                :data="data.libAll"
                width="calc(100% - 30px)"
                height="300px"
                :isShowTitle="false"
                from="report"
            ></visit-line>
          </template>
        </pdf-page>
        <pdf-page>
          <template #textInfo>
            <p :style="`font-size: 18px; padding-bottom: 20px;${style[exportType].chartTitle}`">访问数据</p>
            <div style="font-size: 18px; padding-bottom: 10px">
              浏览量(pv)：{{ data.nowVisitData.pv_count }}
            </div>
            <div style="font-size: 18px; padding-bottom: 20px">
              访客量(uv)：{{ data.nowVisitData.ip_count }}
            </div>
            <!-- <div style="font-size: 18px; padding-bottom: 20px">
              IP数：{{ dataInfo.nowVisitData.ip_count }}
            </div> -->
          </template>
          <template #canvas>
            <visit-line
                :data="data.visitAll"
                width="calc(100% - 30px)"
                height="300px"
                :isShowTitle="false"
                from="report"
                style="page-break-after: always;"
            ></visit-line>
          </template>
        </pdf-page>
        <pdf-page>
          <template #textInfo>
            <p style="font-size: 18px; padding-bottom: 20px">网站内容</p>
            <div
                style="font-size: 18px; padding-bottom: 10px"
                v-for="(item, index) in data.contentCate"
                :key="index"
            >
              {{ item.name }}：{{ item.value }}
            </div>
          </template>
          <template #canvas>
            <visit-line
                :data="data.contentAll"
                width="calc(100% - 30px)"
                height="300px"
                :isShowTitle="false"
                from="report"
            ></visit-line>
          </template>
        </pdf-page>
        <pdf-page>
          <template #textInfo>
            <p style="font-size: 18px; padding-bottom: 20px">媒体平台</p>
            <div
                style="font-size: 18px; padding-bottom: 10px"
                v-for="(item, index) in data.mediaCate"
                :key="index"
            >
              {{ item.name }}：{{ item.value }}
            </div>
          </template>
          <template #canvas>
            <visit-line
                :data="data.mediaAll"
                width="calc(100% - 30px)"
                height="300px"
                :isShowTitle="false"
                from="report"
            ></visit-line>
          </template>
        </pdf-page>
      </template>
    </div>
  </div>
</template>

<style scoped lang="scss">
:deep(.el-input__wrapper) {
  border-radius: 30px;
}
.visits-box {
  background: rgba(255, 255, 255, 0.99);
  box-shadow: 0px 6px 12px 0px rgb(216 224 234 / 20%);
  padding: 20px 30px;
  width: 100%;
  box-sizing: border-box;
  overflow-y: auto;
  height: calc(100% - 60px);
}
.statistics-top {
  width: 100%;
  justify-content: space-between;
  padding: 13px 22px;
  box-sizing: border-box;
}
.e-box {
  /*width: calc(100% - 260px);*/
  height: 300px;
  flex: 1;
}
.btn-box .item {
  padding: 0 13px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 32px;
  background: #ffffff;
  border: 1px solid #e3e5ec;
  border-radius: 14px;
  font-size: 12px;
  font-family: PingFang SC;
  font-weight: 500;
  color: #7f8294;
  transition: all ease-in-out 0.3s;
  cursor: pointer;
  margin-right: 10px;
}
.btn-box .item.active {
  background: #2859ff;
  border-color: #2859ff;
  box-shadow: 0px 4px 10px 0px rgba(40, 89, 255, 0.2);
  color: #ffffff;
}
.btn-box .item:hover {
  border-color: #a0b6ff;
  color: #2859ff;
}
.btn-box .item.active:hover {
  background: #2859ff;
  box-shadow: 0px 4px 10px 0px rgba(40, 89, 255, 0.2);
  color: #ffffff;
}
.visit-part01 .item {
  font-size: 12px;
  font-family: PingFang SC;
  font-weight: 400;
  color: #696c7d;
  margin-bottom: 30px;
}
.visit-part01 .item i {
  display: inline-block;
  width: 4px;
  height: 16px;
  background: #3765ff;
  border-radius: 1px;
  margin-right: 5px;
}
.visit-part01 .item .key {
  font-size: 14px;
  font-family: PingFang SC;
  font-weight: 500;
  color: #696c7d;
  width: 80px;
  display: inline-block;
}
.visit-part01 .item .value {
  margin-left: 10px;
  font-size: 16px;
  font-family: PingFang SC;
  font-weight: 800;
  color: #3765ff;
}
.btn-box .layui-input {
  background: none;
  border: none;
  height: 100%;
  line-height: 100%;
  width: 80px;
}
.visit-box {
  align-items: flex-start;
  padding-bottom: 22px;
  margin-bottom: 22px;
  border-bottom: #e3e5ec solid 1px;
}
.visit-box:last-child {
  border-bottom: none;
  margin-bottom: 0;
}
.visit-box .visit-title {
  font-size: 16px;
  font-family: PingFang SC;
  font-weight: bold;
  color: #1c2b4b;
  margin-bottom: 20px;
}
.visit-box .visit-info {
  width: 180px;
}
.cake-box {
  height: 150px;
}
.lib .visit-part01 {
  display: flex;
  flex-wrap: wrap;
}
.visit-box:first-child .lib .visit-part01 {
  flex-direction: column;
  align-items: center;
}
.lib .visit-part01 .item {
  width: 50%;
  margin-bottom: 20px;
}
.visit-box:first-child .lib .visit-part01 .item {
  width: 100%;
}
.lib .visit-part01 .item .key {
  width: auto;
}
/* 关于pdf 样式 */
.pdf-page-box {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  // padding-top: 30px;
  // padding-bottom: 30px;
  box-sizing: border-box;
}
</style>
