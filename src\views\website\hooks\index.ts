import { ref, computed } from 'vue'
import store from '@/store'
import { ElMessage } from 'element-plus';

export const usePreview = () => {

  const siteInfo = computed(() => store.getters.siteInfo)
  
  const baseUrl = ref('');
  const previewUrl = ref('');
  const useDevLinkList = ['localhost', 'jzt_dev_1.china9.cn'];
  if (useDevLinkList.includes(window.location.hostname)) {
    baseUrl.value = 'https://jzt_dev_1.china9.cn/';
    previewUrl.value = 'http://jzt_dev_2.china9.cn/';
  } else {
    baseUrl.value = 'https://zhjzt.china9.cn/';
    previewUrl.value = 'https://ijzt.china9.cn/';
  }

  // 打开编辑页
  const openEdit = (item: any) => {
    const { id: site_id, company_id } = siteInfo.value
    const { jwp_id, layout_id } = item
    if (!site_id || !company_id || !jwp_id || !layout_id) return ElMessage.error('缺少必要参数')
    let url = `${previewUrl.value}index.php/component/jwpagefactory/?view=form&tmpl=component&layout=edit`
    url += `&company_id=${company_id}&site_id=${site_id}`
    url += `&id=${jwp_id}&layout_id=${layout_id}`
    window.open(url, '_blank')
  }

  // 打开预览页
  const openPreview = (item: any) => {
    const { id: site_id, company_id } = siteInfo.value
    const { jwp_id, layout_id } = item
    if (!site_id || !company_id || !jwp_id || !layout_id) return ElMessage.error('缺少必要参数')
    // 转base64 jwp_id
    const base64JwpId = btoa(jwp_id)
    let url = `${previewUrl.value}index.php/component/jwpagefactory/?view=page`
    url += `&company_id=${company_id}&site_id=${site_id}`
    url += `&id=${base64JwpId}&layout_id=${layout_id}`
    window.open(url, '_blank')
  }

  return {
    baseUrl,
    previewUrl,
    openEdit,
    openPreview
  }
}
