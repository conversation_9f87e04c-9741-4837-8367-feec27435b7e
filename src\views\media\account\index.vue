<template>
  <div class="h100 flex-y">
    <page-menu v-if="navList.length" :list="navList" @click-item="navItemClick" :activeIndex="activeIndex" />
    <div class="flex-1">
      <!-- <component ref="currentComponentRef" :is="currentComponent" :accountTypeList="accountTypeList" @changeNav="changeNav" /> -->
      <list-view v-show="activeIndex === 0" ref="listViewRef" :account-type-list="accountTypeList" @changeNav="changeNav" />
      <add-account v-show="activeIndex === 1" ref="addAccountRef" :account-type-list="accountTypeList" @changeNav="changeNav" @success="refreshList" />
      <account-show v-show="activeIndex === 2" ref="accountShowRef" :account-type-list="accountTypeList" @changeNav="changeNav" @delAuth="delAuth" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineAsyncComponent, markRaw, onMounted } from 'vue'
import { useDataList } from '@/hooks/useDataList'
import { accountTypeApi } from '@/api/media/account'

const { getList } = useDataList()

const ListView = markRaw(defineAsyncComponent(() => import('./components/List.vue')))
const AddAccount = markRaw(defineAsyncComponent(() => import('./components/AddAccount.vue')))
const AccountShow = markRaw(defineAsyncComponent(() => import('./components/Show.vue')))

const listViewRef = ref()
const addAccountRef = ref()
const accountShowRef = ref()

const navList = ref([
  { title: '账号列表', key: 'list', component: ListView },
  { title: '添加账号', key: 'add', component: AddAccount },
  { title: '账号详情', key: 'show', component: AccountShow, hidden: true }
])
const activeIndex = ref(0)

const navItemClick = (item: any, index: number, data: any) => {
  activeIndex.value = index
}

const rowData = ref({})
const changeNav = (data:any) => {
  const { key, val } = data
  if (val) rowData.value = val
  if (key === 'show') {
    accountShowRef.value.setData(val)
  }
  navList.value.forEach((item:any, index:number) => {
    if (item.key === key) {
      activeIndex.value = index
    }
  })
}

// 删除授权
const delAuth = (id: string) => {
  listViewRef.value.delAuth(id)
}

// 刷新列表
const refreshList = () => {
  listViewRef.value.refresh()
}

// 获取平台类型
const accountTypeList: any = ref([])
const getAccountTypeList = async () => {
  accountTypeList.value = await getList(accountTypeApi, {})
}

onMounted(() => {
  getAccountTypeList()
})
</script>

<style scoped lang="scss">

</style>