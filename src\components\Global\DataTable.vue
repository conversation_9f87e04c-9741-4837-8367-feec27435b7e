<template>
  <div class="h100 flex-y">
    <div class="flex-1">
      <el-table v-loading="loading" :data="data" row-key="id" :row-style="{ height: '68px' }" :highlight-current-row="isSingle"
        :height="`calc(100% - 0px)`" style="width: 100%;" ref="multipleTableRef"
        @selection-change="handleSelectionChange" :tree-props="{ children: childrenKey, hasChildren: 'hasChildren' }"
        @sort-change="sortChange" @current-change="handleCurrentChange">
        <el-table-column v-if="showMultiple" type="selection" width="55" />
        <el-table-column v-if="showIndex" label="ID" type="index" width="65" />
        <el-table-column v-for="(item, index) in columns" :label="item.label" :prop="item.prop" :align="item.align"
          :width="item.width || 'auto'" :sortable="item.sortable" :show-overflow-tooltip="item.showOverflowTooltip" :class-name="item.sortable ? 'col-sort': ''">
          <template #default="{row}">
            <template v-if="item.type === 'iconfont'">
              <i v-if="row[item.prop]" :class="['iconfont', row[item.prop]]"></i>
            </template>
            <!-- 图片 -->
            <template v-else-if="item.type === 'image'">
              <el-image :src="row[item.prop]" fit="cover" style="width: 60px; height: 60px" />
            </template>
            <template v-else-if="item.type === 'slot'">
              <slot :name="item.prop" :row="row"></slot>
            </template>
            <template v-else-if="item.type === 'tag'">
              <el-tag :type="item.values[row[item.prop]].type" effect="dark" size="small">{{
                item.values[row[item.prop]].text }}</el-tag>
            </template>
            <template v-else-if="item.type === 'switch'">
              <el-switch v-model="row[item.prop]" :active-value="1" :inactive-value="0"
                @change="handleSwitchChange(row, item.prop)" />
            </template>
            <template v-else-if="item.type === 'state'">
              {{ row[item.prop] == 1 ? '已上线' : '已下线' }}
            </template>
            <template v-else>{{ row[item.prop] !== undefined && row[item.prop] !== null && row[item.prop] !== '' ? row[item.prop] : '--' }}</template>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页 -->
    <div v-if="showPage && total > 0" class="pagination">
      <data-page v-model:page="page" :total="total" :totalPage="lPage" @pagination="pageChange"></data-page>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref, watch, onMounted, defineAsyncComponent, inject, computed} from 'vue';
import Sortable from 'sortablejs'
import { contentDragApi } from '@/api/content'

const DataPage = defineAsyncComponent(() => import('./DataPage.vue'))

const props = defineProps({
  tableData: {
    type: Array,
    default: () => []
  },
  columns: {
    type: Array as any,
    default: () => []
  },
  showIndex: {
    type: Boolean,
    default: true
  },
  showMultiple: {
    type: Boolean,
    default: true
  },
  // 单选
  isSingle: {
    type: Boolean,
    default: false
  },
  childrenKey: {
    type: String,
    default: 'children'
  },
  showPage: {
    type: Boolean,
    default: true
  },
  api: {
    type: Function,
    default: null
  },
  apiType: {
    type: String,
    default: ''
  },
  cateType: {
    type: String,
    default: ''
  },
  params: {
    type: Object,
    default: () => { }
  },
  limit: {
    type: Number,
    default: 10
  },
  allResult: {
    type: Boolean,
    default: false
  },
  maxNumber: {
    type: Number,
    default: 0
  },
  dragSort: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['changeMultiple', 'changeMaxNumber', 'changeCurrent'])

const loading = ref(false)

// 表格多选
const multipleTableRef = ref(null)
const multipleSelection = ref([])
const handleSelectionChange = (val: never[]) => {
  multipleSelection.value = val
  emit('changeMultiple', val)
}

// 分页相关
const page = ref(1)
const total = ref(0)
const lPage = ref(1)

const handleSwitchChange = (row: any, item: any) => {
  console.log(row)
}

// 单选
const handleCurrentChange = (val: any) => {
  emit('changeCurrent', val)
}

const curPage = inject<{[key: string]: any, value: number}>('curPage', {value: 1})

// 获取表格数据
const data:any = ref(props.tableData)
const getTableData = async (params = {}) => {
  if (props.api) {
    try {
      // console.log(holdCurPage.value, curPage.value, 'curPage');
      /*if (holdCurPage) */page.value = curPage.value;
      loading.value = true
      let option = { ...props.params, page: page.value, limit: props.limit, ...params }
      let result
      if(props.apiType) result = await props.api(props.apiType, option, { allResult: props.allResult })
      else result = await props.api(option)
      if (props.allResult) emit('changeMaxNumber', result.maxnum), result = result.data
      if (props.showPage) {
        console.log(result, '数据列表-显示分页')
        data.value = result.data
        total.value = result.total
        lPage.value = result.last_page
      } else {
        console.log(result, '数据列表-不显示分页')
        data.value = result
      }
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  } else {
    console.log(props.api)
    data.value = props.tableData
  }
}

const pageChange = () => {
  curPage.value = page.value
  // holdCurPage.value = false
  getTableData()
}

// 点击表格上方排序
const sortChange = (e: any) => {
  // console.log(e)
  let { column, prop, order } = e
  page.value = 1
  curPage.value = page.value
  if (order == 'ascending') order = 'asc'
  else order = 'desc'
  getTableData({ prop, order })
}

// 刷新列表数据
const refresh = () => {
  page.value = 1
  curPage.value = page.value
  data.value = []
  getTableData()
}

// 行拖拽
const postLoading = ref(false)
const rowDrop = () => {
  let tbody: any = document.querySelector('.el-table__body-wrapper tbody')
  Sortable.create(tbody, {
    // or { name: "...", pull: [true, false, 'clone', array], put: [true, false, array] }
    group: {
      name: 'words',
      pull: true,
      put: true
    },
    handle: ".col-sort",
    animation: 150, // ms, number 单位：ms，定义排序动画的时间
    onEnd({ newIndex, oldIndex }: any) { // 结束拖拽
      // 找到 columns 中对应的排序字段
      let sortColumn = props.columns.find((item: any) => item.sortable)?.prop

      let orders = data.value.map((item: any) => item[sortColumn])
      let newOrders = data.value.map((item: any) => item.id)
      // console.log('newIndex:', newIndex)
      // console.log('oldIndex:', oldIndex)
      if (oldIndex != newIndex) {
        let oldId = newOrders[oldIndex]
        let newId = newOrders[newIndex]
        newOrders.splice(oldIndex, 1)
        newOrders.splice(newIndex, 0, oldId)
        // console.log('orders', orders)
        // console.log('newOrders', newOrders)
        postLoading.value = true
        contentDragApi(props.cateType, { orders: orders.toString(), ids: newOrders.toString() })
        .then(() => {
          getTableData()
        }).finally(() => {
          postLoading.value = false
        })
      }
    }
  })
}

watch(
  () => props.params,
  () => {
    page.value = 1
    curPage.value = page.value
    data.value = []
    getTableData()
  },
  { deep: true }
)

watch(
  () => props.tableData,
  () => {
    data.value = props.tableData
  },
  { deep: true }
)

onMounted(async () => {
  getTableData()
  if (props.dragSort) rowDrop()
})

defineExpose({ refresh, page })
</script>

<style scoped lang="scss">
.col-sort {
  cursor: move;
}
</style>
