<template>
  <div class="h100">
    <data-list ref="dataListRef" :tableConfig="tableConfig" @clickBtn="dataAction" v-model:multipleSelection="tableConfig.multipleSelection">
      <template #img="{ row }">
        <div class="cover-box width64 height40 mt8 mb8">
          <el-image class="w100 h100" :src="handleCover(row)" fit="cover" :preview-src-list="[handleCover(row)]" :preview-teleported="true">
          </el-image>
        </div>
      </template>
      <template #type="{ row }">
        <div class="flex-c-center">
          <span>{{ row.type == 1 ? '媒体报道' : (row.type == 2 ? '企业新闻' : '--') }}</span>
        </div>
      </template>
      <template #status="{ row }">
        <span>{{ row.status == 0 ? '未发布' : (row.status == 1 ? '已发布' : '发布失败') }}</span>
      </template>
      <template #action="{ row }">
        <div class="table-action flex">
          <div class="item" @click="editInfo(row)">
            <i class="iconfont icon-bianji"></i> <span>编辑</span>
          </div>
          <div v-if="row.status != 1" class="item" @click="publishInfo(row.id)">
            <i class="iconfont icon-fabu"></i> <span>发布</span>
          </div>
          <div class="item" @click="deleteAction(row.id, delNewsApi)">
            <i class="iconfont icon-shanchu1"></i> <span>删除</span>
          </div>
        </div>
      </template>
    </data-list>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import { useDataList } from '@/hooks/useDataList'
import { getNewsListApi, delNewsApi, publishNewsApi } from '@/api/media'

const { DataList, dataListRef, handleCover, deleteAction, refresh } = useDataList()

const emit = defineEmits(['changeNav'])

const tableConfig = ref({
  api: getNewsListApi,
  columns: [
    { label: '封面', prop: 'img', width: 90, type: 'slot' },
    { label: '发布类型', prop: 'type', width: 100, type: 'slot' },
    { label: '新闻来源', prop: 'source', width: 100 },
    { label: '新闻标题', prop: 'title', showOverflowTooltip: true },
    { label: '新闻外链', prop: 'exterior_url', showOverflowTooltip: true },
    { label: '添加时间', prop: 'create_time', width: 180 },
    { label: '状态', prop: 'status', width: 100, type: 'slot' },
    { label: '操作', prop: 'action', align: 'center', width: 240, type: 'slot' }
  ],
  multipleSelection: []
})

const dataAction = (data: any) => {
  console.log(data, 'data')
  const { key } = data
  let list = tableConfig.value.multipleSelection
  const ids = list.map((item: any) => item.id).join(',')
  switch (key) {
    case 'add':
      emit('changeNav', { key: 'add' })
      break;
    case 'delete':
      if (list.length == 0) return ElMessage.warning('请选择要删除的数据')
      deleteAction(ids, delNewsApi)
      break;
  }
}

const editInfo = (row: any) => {
  console.log(row)
  emit('changeNav', { key: 'add', val: row })
}

const publishInfo = (id: any) => {
  console.log(id)
  let loading = ElLoading.service({
    lock: true,
    text: '发布中...',
    background: 'rgba(0, 0, 0, 0.7)'
  })
  publishNewsApi({id}).then(() => {
    ElMessage.success('发布成功')
    refresh()
  }).finally(() => {
    loading.close()
  })
}

defineExpose({ refresh })

</script>

<style scoped lang="scss">
.cover-box {
  border-radius: 4px;
  overflow: hidden;
}
</style>