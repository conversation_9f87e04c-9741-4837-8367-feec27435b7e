import http from '@/utils/request'
const { VUE_APP_PROXY_DOMAIN_REAL2 } = process.env;

// 上传图片
export const uploadImgApi = (data: any) => {
  return http.request({
    url: '/Goodsnew/tpajax',
    method: 'post',
    data
  })
}

// 媒体管理 上传图片
export const mediaUploadImgApi = (data: any) => {
  return http.request({
    url: '/upfile/uploadImage',
    method: 'post',
    data
  })
}

// 媒体管理 上传视频
export const mediaUploadVideoApi = (data: any) => {
  return http.request({
    url: '/upfile/uploadVideo',
    method: 'post',
    data
  })
}

// 上传ssl证书
export const uploadSslApi = (data: any) => {
  return http.request({
    url: '/ChangeSite/upload_sslnew',
    method: 'post',
    data
  })
}

// 检查素材空间 文件url
export const checkSpaceCountApi = (data: any) => {
  return http.request({
    url: '/CheckCount/checkCounts',
    method: 'post',
    data
  })
}

// 检查素材空间 文件
export const checkSpaceFileApi = (data: any) => {
  return http.request({
    url: '/CheckCount/checkcount',
    method: 'post',
    data
  })
}

// 获取群晖oss配置
export const getSynologyOssConfigApi = (params: any) => {
  return http.request({
    url: '/QunhuiOss/getInfo',
    method: 'get',
    params
  })
}

// 解析zip
export const parseZipApi = (data: any) => {
  return http.request({
    baseURL: 'https://oss.lcweb01.cn',
    url: '/zip',
    allResult: true,
    method: 'post',
    data
  } as any)
}

// 上传压缩包文件
export const uploadZipApi = (data: any) => {
  return http.request({
    url: '/BucketNew/zipFiles',
    method: 'post',
    data
  })
}

// 素材库 批量上传图片 单张上传
export const uploadBatchImgApi = (data: any) => {
  return http.request({
    url: '/BucketNew/setuploadFile',
    method: 'post',
    data
  })
}

// 素材库 批量上传图片 sort
export const uploadBatchImgSortApi = (data: any) => {
  return http.request({
    url: '/BucketNew/uploadUpdateSort',
    method: 'post',
    data
  })
}
