<template>
  <div class="ai-content">
    <p class="title">AI智能营销策划中心</p>
    <div class="ai-box">
      <p class="box-title">发布内容没想法？ 您可以试试向我提问，我来帮您策划创作</p>
      <div class="input-box flex">
        <el-input v-model="aiKeywords" placeholder="请输入您的创作方向"></el-input>
        <jzt-button class="active" name="一键创作" @click="toAI"></jzt-button>
      </div>
      <p class="tips-title">例 : 请帮我写一些XX产品的短视频标题/简介</p>
      <div class="flex" style="margin-top: 10px;justify-content: space-between;">
        <!-- <el-radio-group v-model="aiType" size="small">
          <el-radio-button v-for="(item, index) in aiTypeList" :label="index" :key="index">
          </el-radio-button>
        </el-radio-group> -->
        <!-- <p v-if="!isAgent">您还可免费使用{{useNum}}次</p> -->
      </div>
      <div class="answer-list">
        <div v-html="aiContent"></div>
        <!-- <el-radio-group v-model="aiWord">
          <el-radio v-for="item in aiList" :label="item">{{ item }}</el-radio>
        </el-radio-group> -->
      </div>
      <!-- <p class="tips">建站通智能营销策划，帮助用户在发布网站内容、媒体视频及图文的时候以提问回答的方式，提供创意策划，可直接引用或作为一些灵感进行二次创作</p>
      <div class="btn-box flex">
        <div class="material-upload" @click="copy('title')">复制到标题</div>
        <div class="material-upload" @click="copy('desc')">复制到简介</div>
      </div> -->
    </div>
    <!-- 遮罩 -->
    <div class="frosted-glass" v-if="!isAgent">
      <div class="glass-content-wrap float-wrap">
        <div class="to-wenxin">
          <a href="https://yiyan.baidu.com" target="_blank">
            <img src="@/assets/images/media/wenxin1.png" alt="文心一言">
            <p>跳转到文心一言</p>
          </a>
        </div>
        <div class="to-upgrade" @click="toggleScale">
          <img src="@/assets/images/media/jzt2.png" alt="升级到AI版">
          <p>联系您的客服升级至建站通AI版</p>
          <div class="tel">
            官方电话：<a href="tel:************" :class="{ scale: scale }">************</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import store from '@/store';
import { aiCreateWenxin, aiCreateGPT, getAINumApi } from '@/api/media'

const isAgent = computed(() => store.getters.isAgent)

const aiTypeList: any = {'文心一言': { api: aiCreateWenxin }, 'ChatGPT': { api: aiCreateGPT }}  // AI类型
const aiType = ref('文心一言')  //当前选择创作类型
const aiKeywords = ref('')  // 关键词
const aiList = [
  '你需要知道的网站建设技巧，都在这个短视频里！',
  '这个短视频将告诉你，为什么一个优秀的网站是你成功的关键！',
  '如何用简单的方式创建一个专业的网站?看这个短视频!',
  '这个短视频将带你了解网站建设的未来发展趋势!',
  '不要再错过这个短视频，它将教你如何创建一个高效的网站!'
] // 结果列表
const aiWord = ref('')  // 选中的值
const aiContent = ref('')  // 接口回来的值
const useNum = ref(0)  // 剩余免费次数
const scale = ref(false)

// 升级电话加动画
const toggleScale = () => {
  scale.value = !scale.value;
  setTimeout(() => {
    scale.value = !scale.value;
  }, 1500);
}

// 获取免费使用次数
const getAINum = () => {
  getAINumApi().then((res: any) => {
    useNum.value = res
  }).catch(() => {
    useNum.value = 0
  })
}

const toAI = () => {
  if (!isAgent.value) return ElMessage.error('请联系客服升级AI版使用！')
  // if(useNum.value <= 0) {
  //   return ElMessage.error('您的免费体验次数已用完！')
  // }
  if (aiKeywords.value == '') {
    return ElMessage.error('输入关键词，我才能帮到您！')
  }
  aiContent.value = '思考中...'
  let data = {inputWord: JSON.stringify([{role: "user", content: aiKeywords.value}])}
  aiTypeList[aiType.value].api(data).then((res: any) => {
    aiContent.value = res
  }).catch(() => {
    aiContent.value = ''
  }).finally(() => {
    // getAINum()
  })
}


</script>

<style scoped lang="scss">
/* AI创作 */
.ai-content {
  position: relative;
  width: 446px;
  .title {
    text-align: center;
    margin-bottom: 10px;
    color: #2859ff;
    font-weight: 700;
  }
  .ai-box {
    border: 1px solid #E3E5EC;
    border-radius: 3px;
    height: 570px;
    padding: 10px 20px;
    box-sizing: border-box;
    .btn-box {
      margin-left: 0;
    }
  }
  .box-title {
    line-height: 1.5;
    margin-bottom: 8px;
    font-size: 14px;
    color: #333333;
  }
  .input-box {
    .material-upload.active {
      padding: 0;
      width: 80px;
      text-align: center;
      line-height: 38px;
      margin-left: 5px;
      font-size: 13px;
      box-shadow: none;
      border: none;
    }
  }
  .tips-title {
    line-height: 1.5;
    color: #7F8294;
  }
  .answer-list {
    border: 1px solid #E3E5EC;
    border-radius: 3px;
    margin: 10px auto;
    padding: 10px;
    height: calc(100vh - 550px);
    overflow: hidden;
    overflow-y: auto;
    white-space: pre-wrap;
    line-height: 1.5;
    :deep(.el-radio-group) {
      display: flex;
      flex-direction: column;
      .el-radio {
        display: flex;
        margin-bottom: 10px;
        margin-right: 0;
        &:last-child {
          margin-bottom: 0;
        }
        .el-radio__input {
          margin-top: 3px;
        }
        .el-radio__label {
          white-space: break-spaces;
          line-height: 1.5;
          color: #202020;
        }
      }
    }
  }
}
</style>
