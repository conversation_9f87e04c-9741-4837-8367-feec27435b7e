<template>
  <div class="upload-img-wrap">
    <div v-loading="loading" :class="['upload-container', { 'flex': position == 'right' }, { 'flex-x': position == 'right-start'} ]">
      <div v-if="multiple" class="img-multiple-wrap flex">
        <div class="img-box mr10" :style="`width: ${width || '100%'};height: ${height};`" v-for="(item, index) in imageList" :key="index">
          <img :src="item" class="cover-img" />
          <div v-if="delBtn" class="del flex-c-center" @click.stop="delImg(index)"><i class="iconfont icon-guanbi"></i></div>
        </div>
      </div>
      <template v-if="!multiple || (currentMax > 0)">
        <div class="img-box" :style="`width: ${width || '100%'};height: ${height};`" @click="choiceImg">
          <div class="cover-img upload" v-if="!props.imageIntro || multiple">
            <i class="iconfont icon-tupiantianjia"></i>
          </div>
          <template v-else>
            <video v-if="type == 'video'" :src="props.imageIntro" controls class="cover-img" />
            <img v-else :src="props.imageIntro" class="cover-img" />
            <div v-if="delBtn" class="del flex-c-center" @click.stop="delImg()"><i class="iconfont icon-guanbi"></i></div>
          </template>
        </div>
        <div :class="['btn-box', {'flex-c-center mt10' : position == 'bottom'}, { 'ml10': position == 'right' || position == 'right-start'}]" v-if="uploadBtn">
          <jzt-button :name="localBtnText" @click="choiceImg" :class="[{ 'mr10': position == 'bottom' }, { 'mb10': position == 'right' || position == 'right-start' }]"></jzt-button>
          <jzt-button :name="libraryBtnText" @click="choiceLibrary" class="active"></jzt-button>
          <slot name="button"></slot>
        </div>
      </template>
    </div>
    <input ref="uploadInput" type="file" name="file" :accept="fileObj.accept" class="input-file" :multiple="multiple" @change="uploadFile" :maxlength="currentMax" />
    <!-- 素材库选择弹窗 -->
    <edit-dialog ref="editDialogRef" v-model:dialog-show="dialogShow" title="从素材库导入" sure-text="导入" width="810px" :custom-form="true" @sure="importImg">
      <library ref="libraryRef" v-model:library-img="libraryImg" :search-name="searchName" :isRadio="!multiple" :max="currentMax" :limit="10" :file-type="fileObj.type"></library>
    </edit-dialog>
  </div>
</template>

<script setup lang='ts'>
import { ref, defineAsyncComponent, computed, watch } from 'vue'
import { ElMessage } from "element-plus"
import { useDialog } from '@/hooks/useDialog';
import { uploadImgApi, mediaUploadImgApi, mediaUploadVideoApi } from '@/api/upload'
import { uploadVideoApi } from '@/api/bucket'
import {autoFileSize} from "@/utils";

const Library = defineAsyncComponent(() => import('@/components/Uploads/Library.vue'))

const { EditDialog, editDialogRef, dialogShow } = useDialog()

let props = defineProps({
  imageIntro: {
    type: String,
    value: ''
  },
  imgSrc: {
    type: String,
    value: ''
  },
  width: {
    type: String,
    value: ''
  },
  height: {
    type: String,
    value: ''
  },
  uploadBtn: {
    type: Boolean,
    default: false
  },
  delBtn: {
    type: Boolean,
    default: true
  },
  maxSize: {
    type: Number,
    default: 5
  },
  from: {
    type: String,
    default: 'content'
  },
  type: {
    type: String,
    default: 'image'
  },
  position: {
    type: String,
    default: 'bottom'
  },
  multiple: {
    type: Boolean,
    default: false
  },
  limit: {
    type: Number,
    default: 6
  },
  // 本地选择按钮文字
  localBtnText: {
    type: String,
    default: '本地选择'
  },
  // 素材库选择按钮文字
  libraryBtnText: {
    type: String,
    default: '素材库选择'
  },
})

const emits = defineEmits(['imgOk', 'update:imageIntro', 'update:imgSrc', 'setImgInfo'])

let uploadInput = ref<any>('uploadInput')
let libraryImg = ref<any>('')

const fileObj = computed(() => {
  const { from, type } = props
  let obj:any = {
    'image': { text: '图片', type: 1, accept: 'image/*', maxSize: 5, api: from === 'media' ? mediaUploadImgApi : uploadImgApi, formDataFile: from === 'media' ? 'image' : 'file' },
    'video': { text: '视频', type: 2, accept: 'video/*', maxSize: from === 'media' ? 50 : 100, api: from === 'media' ? mediaUploadVideoApi : uploadVideoApi, formDataFile: from === 'media' ? 'video' : 'video' }
  }
  return obj[type]
})

const imageList:any = ref([])

/* 素材库组件 */
const libraryRef = ref()
// 搜索
const searchName = ref("");

// 计算当前可传图片个数  用于多图
const currentMax = computed(() => props.limit - imageList.value.length)

// 监听图片上传
const loading = ref(false)
const uploadFile = async (files: any) => {
  files = files.target.files
  if (props.multiple) {
    if (files.length > currentMax.value) {
      return ElMessage.error(`最多还可上传${currentMax.value}张图片`)
    }
    loading.value = true
    if (files.length > 0) {
      for (let i = 0; i < files.length; i++) {
        try {
          const result = await uploadImg(files[i])
          if (result) imageList.value.push(result)
        } catch (error) {
          console.log(error, 'error')
        } finally {
          if (i == files.length - 1) loading.value = false
        }
      }
      emits("update:imageIntro", imageList.value.join(','))
    } else {
      loading.value = false
    }
  } else {
    loading.value = true
    try {
      const result = await uploadImg(files[0])
      if (result) emits("update:imageIntro", result)
    } catch (error) {
      console.log(error, 'error')
    } finally {
      loading.value = false
    }
  }
}

// 上传图片api
const uploadImg = async (file: any) => {
  return new Promise((resolve, reject) => {
    if (!file) return resolve('')
    // 判断文件格式
    console.log(file.type, fileObj.value.accept, 'file.type, fileObj.value.accept')
    if (!file.type.includes(fileObj.value.accept.split('/')[0])) {
      ElMessage.error(`请选择${fileObj.value.text}格式`)
      return resolve('')
    }
    const maxSize = props.maxSize || fileObj.value.maxSize
    if (file.size > 1024 * 1024 * maxSize) {
      // ElMessage.error(`${fileObj.value.text}大小不能超过${maxSize}M`)
      ElMessage.error(`${fileObj.value.text}大小不能超过${autoFileSize(1024 * 1024 * maxSize)}`)
      return resolve('')
    }
    let formInfo = new FormData()
    formInfo.append(fileObj.value.formDataFile, file)
    if (fileObj.value.formDataFile == 'video') {
      formInfo.append('title', file.name)
      formInfo.append('f_id', '0')
    }
    emits('setImgInfo', file)
    fileObj.value.api(formInfo).then((res: any) => {
      resolve(res)
    }).catch((err: any) => {
      reject(err)
    })
  })
}

// 选择图片
const choiceImg = () => {
  uploadInput.value.click()
}

// 删除图片
const delImg = (index = 0) => {
  // emits("imgOk", '')
  let list = JSON.parse(JSON.stringify(imageList.value))
  if (props.multiple) {
    list.splice(index, 1)
    imageList.value = list
    emits("update:imageIntro", list.join(','))
  } else {
    emits("update:imageIntro", '')
  }
  uploadInput.value.value = ''
}

// 素材库选择
const choiceLibrary = () => {
  dialogShow.value = true
}

// 素材库导入点击
const importImg = () => {
  if (libraryImg.value) {
    dialogShow.value = false
    console.log(libraryImg.value, 'libraryImg.value')
    if (props.multiple) {
      let list = libraryImg.value.map((item:any) => item.url)
      imageList.value = [...imageList.value, ...list]
      emits("update:imageIntro", imageList.value.join(','))
    }
    else emits("update:imageIntro", libraryImg.value)
  } else {
    ElMessage.error(`请选择${fileObj.value.text}`)
  }
}

watch(() => props.imageIntro, (newVal, oldVal) => {
  if (newVal) {
    if (props.multiple) {
      if(newVal) imageList.value = newVal.split(',')
    } else {
      emits("update:imageIntro", newVal)
    }
  } else {
    imageList.value = []
    emits("update:imageIntro", '')
  }
}, { immediate: true })

</script>

<style scoped lang='scss'>
.btn-box {
  // justify-content: space-between;
  // margin-top: 10px;
  flex-shrink: 0;
}
.img-box {
  width: 100%;
  height: 172px;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
  .cover-img {
    border: 1px solid var(--el-border-color);
    border-radius: var(--el-border-radius-base);
    width: 100%;
    height: 100%;
    object-fit: contain;
    &img {
      object-fit: contain;
    }
    &.upload {
      display: flex;
      align-items: center;
      justify-content: center;
      .icon-tupiantianjia {
        color: #c5ccde;
        font-size: 26px;
      }
    }
  }
  .del {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 14px;
    height: 14px;
    line-height: 14px;
    text-align: center;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 2px;
    z-index: 3;
    cursor: pointer;
    padding: 4px;
    box-sizing: content-box;
    .iconfont {
      font-size: 6px;
      color: #FFFFFF;
    }
  }
}
input {
  width: 10px;
  height: 10px;
  opacity: 0;
  position: absolute;
  left: -1000px;
  top: 0;
}
</style>
