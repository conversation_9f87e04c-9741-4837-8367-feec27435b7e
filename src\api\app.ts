import http from '@/utils/request'
const { VUE_APP_PROXY_DOMAIN_REAL2 } = process.env;

// 获取菜单
export function getMenuApi(data?: any) {
  return http.request({
    // baseURL: VUE_APP_PROXY_DOMAIN_REAL2,
    url: '/newmenu/getMenu',
    method: 'POST',
    data
  })
}

// 添加/修改菜单
export function editMenuApi(data: any) {
  return http.request({
    // baseURL: VUE_APP_PROXY_DOMAIN_REAL2,
    url: '/newmenu/editMenu',
    method: 'POST',
    data
  })
}

// 删除菜单
export function delMenuApi(data: any) {
  return http.request({
    // baseURL: VUE_APP_PROXY_DOMAIN_REAL2,
    url: '/newmenu/delMenu',
    method: 'POST',
    data
  })
}

// 全局搜索
export function getSearchApi(data: any) {
  return http.request({
    url: '/Module/search',
    method: 'POST',
    data,
    allResult: true
  } as any)
}

// 获取用户IP
export function getUserIpApi() {
  return http.request({
    baseURL: 'https://baota.baina666.com',
    url: '/api/ip/ip',
    method: 'GET'
  })
}

// 统计最近访问记录
export const setRecentAccessApi = (data: any) => {
  return http.request({
    url: 'SiteMenuLog/addStat',
    method: 'post',
    data
  })
}
