import { computed } from "vue";
import { ElMessage, ElLoading } from "element-plus";
import { uploadImgApi } from "@/api/upload";
import { uploadVideoApi } from "@/api/bucket";
import store from "@/store";
import { useSiteStore } from "@/store/modules/site";

const siteInfo = computed(() => store.getter.siteInfo);

/**
 * tinymce 工具函数
 * 无状态函数、可移植
 */
export const uuid = prefix => {
  return prefix + "_" + (Date.now() + Math.floor(Math.random() * 1000000));
};

export const getTinymce = () => {
  const root = typeof window !== "undefined" ? window : global;
  return root && "tinymce" in root ? root.tinymce : null;
};

export function getContent(editor) {
  if (!editor) return "";
  return editor.getContent();
}

export function setContent(val, editor) {
  if (!editor) return;
  editor.setContent(val);
}

export function resetContent(val, editor) {
  if (!editor) return;
  if (editor.resetContent) return editor.resetContent(val);
  // 若无 reset fun，则手动 reset
  editor.setContent(val);
  editor.setDirty(false); // 恢复初始状态
  editor.undoManager.clear();
}

export function setModeDisabled(editor, disabled = true) {
  if (!editor) return;
  editor.mode.set(disabled ? "readonly" : "design");
}

// custom images upload handler
export async function imageUploadHandler(setting, blobInfo, success, failure) {
  let { custom_images_upload_callback } = setting || {};

  let formData = new FormData();

  console.log(blobInfo, "blobInfo");

  let file = blobInfo.file || blobInfo.blob();
  let name = file ? file.name : blobInfo.filename();

  if (navigator.userAgent.indexOf("Firefox") >= 0) {
    console.log(siteInfo, "siteInfo7777777777777777777");
    if (siteInfo.oss) {
      file = blobInfo.file ? new Blob([blobInfo.file]) : blobInfo.blob();
    }
  }

  formData.append("file", file, name);
  formData.append("class_id", 0);
  console.log(name, "name");
  if (name) {
    formData.append("file_name", name);
  }

  console.log(formData instanceof File);

  const loading = ElLoading.service({
    lock: true,
    text: "0"
  });

  try {
    uploadImgApi(formData, {
      headers: { "content-type": "application/x-www-form-urlencoded" },
      onUploadProgress: progressEvent => {
        let persent = ((progressEvent.loaded / progressEvent.total) * 100) | 0; //上传进度百分比
        "" + persent
          ? loading.setText("已上传" + persent + "%")
          : loading.setText("上传中...");
      }
    })
      .then(res => {
        console.log(res, "res");
        // ElMessage.success(name + "上传成功");
        ElMessage.success("上传成功");
        let backImgUrl =
          typeof custom_images_upload_callback === "function"
            ? custom_images_upload_callback(res)
            : res;

        success(backImgUrl);
      })
      .catch(e => {
        success("");
        // ElMessage.error("上传失败！");
        console.log(e);
      })
      .finally(() => {
        loading.close();
      });
  } catch (e) {
    loading.close();
  }
}

// 视频
export function videoUploadHandler(setting, callback, value, meta) {
  let { upload_callback, custom_video_upload_callback } = setting || {};

  if (setting.file_picker_types === "media") {
    let input = document.createElement("input");
    input.setAttribute("type", "file");
    input.onchange = function () {
      let file = this.files[0]; //只选取第一个文件。如果要选取全部，后面注意做修改

      if (!file.type.includes("video") && !file.type.includes("audio")) {
        return ElMessage.error("请选择视频文件");
      }

      let formData = new FormData();
      let name = file.filename;
      console.log(file, "file");

      formData.append("video", file);
      formData.append("f_id", 0);
      formData.append("title", file.name);

      const loading = ElLoading.service({
        lock: true,
        text: "0"
      });

      try {
        uploadVideoApi(formData, {
          headers: { "content-type": "application/x-www-form-urlencoded" },
          onUploadProgress: progressEvent => {
            let persent =
              ((progressEvent.loaded / progressEvent.total) * 100) | 0; //上传进度百分比
            "" + persent
              ? loading.setText("已上传" + persent + "%")
              : loading.setText("上传中...");
          }
        })
          .then(res => {
            console.log(upload_callback);
            console.log(custom_video_upload_callback);
            console.log(res, "res");
            ElMessage.success("上传成功");
            let backUrl =
              typeof custom_video_upload_callback === "function"
                ? custom_video_upload_callback(res)
                : res;

            console.log("backUrl", backUrl);

            callback(backUrl);
          })
          .catch(e => {
            success("");
            // ElMessage.error("上传失败！");
            console.log(e);
          })
          .finally(() => {
            loading.close();
          });
      } catch (e) {
        loading.close();
      }
    };
    //触发点击
    input.click();
  }
}
