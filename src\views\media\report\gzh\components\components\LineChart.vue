<script setup lang="ts">
import {nextTick, ref, watch} from "vue";
import * as echarts from "echarts";

interface Props {
  colors: string[],
  width: string,
  height: string,
  data: any[],
  xAxisData: any[]
}
const props = defineProps<Props>()

const lineChartDom = ref(null)
const chart = ref<any>(null)

const initChart = () => {
  if (!lineChartDom.value) return
  chart.value = echarts.init(lineChartDom.value)
  const series = props.data.map((v: any, i: number) => {
    return {
      name: v.name,
      type: 'line',
      data: v.list,
      smooth: true,
      symbolSize: 0,
      lineStyle: {
        color: props.colors[i],
        width: 2
      },
      itemStyle: {
        color: props.colors[i],
        width: 2
      }
    }
  })
  const option = {
    grid: {
      left: 30,
      right: 10,
      bottom: '25%'
    },
    xAxis: {
      type: 'category',
      data: props.xAxisData,
      axisLabel: {
        color: 'rgba(102, 102, 102, 1)',
        fontSize: 12
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(229, 231, 235, 1)',
          width: 1
        }
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        color: 'rgba(167, 171, 187, 1)',
        fontSize: 12
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(240, 242, 245, 1)',
          width: 1
        }
      },
      axisTick: {
        show: false
      }
    },
    series: series,
    legend: {
      icon: 'rect',
      itemWidth: 8,
      itemHeight: 8,
      align: 'auto',
      bottom: 40,
      type: 'plain',
      itemGap: 50,
    },
    tooltip: {
      trigger: 'axis',
      show: true,
    }
  }
  chart.value.setOption(option)
}

watch(() => props.data, () => {
  nextTick(() => {
    if (chart.value) {
      chart.value.dispose();
      chart.value.clear()
    }
    initChart()
  })
}, {immediate: true, deep: true})
</script>

<template>
  <div ref="lineChartDom" :style="`width: ${width}; height: ${height};`"></div>
</template>

<style scoped lang="scss">

</style>
