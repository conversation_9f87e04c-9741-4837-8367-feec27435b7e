<script lang="ts" setup>
import {computed, ref, watch} from "vue";
import {getCode<PERSON>pi, saveCode<PERSON><PERSON>} from "@/api/interaction";
import {ElMessage, FormInstance} from "element-plus";
import TopTips from "@/components/TopTips.vue";

const props = defineProps({
  cateType: {
    type: String,
    default: '',
  }
})
const typeMap: any = {
  commercialBridge: {
    label: '百度商桥',
    type: 3,
    tips: `生成的代码。`,
    contentType: 'code',
    errorTips: '请填写代码',
    formWidth: '100%',
    buttonDisplay: 'end',
    btns: [],
    btnWidth: '120px'
  },
  merchantLink: {
    label: '商户通',
    type: 4,
    tips: `生成的代码。`,
    contentType: 'code',
    errorTips: '请填写代码',
    formWidth: '100%',
    buttonDisplay: 'end',
    btns: [],
    btnWidth: '120px'
  },
  qq: {
    label: 'QQ',
    type: 5,
    tips: `号，将会采用此QQ号进行对话。`,
    contentType: 'list',
    errorTips: '请填写QQ号',
    formWidth: '373px',
    buttonDisplay: 'space-between',
    btns: [
      {
        label: '添加',
        type: '',
        onClick: () => {
          form.value.contentList.push({No: ''})
        }
      },
    ],
    btnWidth: '100px'
  },
}
const {
  formWidth = '100%',
  buttonDisplay = 'end',
  label = '',
  tips = '',
  contentType = '',
  btns = [],
  btnWidth = '120px',
  errorTips = ''
} = typeMap[props.cateType] || {};
const form = ref({content: '', type: typeMap[props.cateType].type, contentList: [{No: ''}]})

const formRef = ref()
const submit = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.validate(async (valid) => {
    if (valid) {
      const data: any = JSON.parse(JSON.stringify(form.value))
      if (contentType === 'list') {
        data.content = data.contentList.map((item:any) => item.No).join(',')
        delete data.contentList
        if(data.content === ''){
          ElMessage.error(errorTips)
          return
        }
      }
      await saveCodeApi(data, {headers: {'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'}})
      ElMessage.success('保存成功');
    } else {
      console.log('error submit!')
    }
  })
}

const removeDomain = (item: any) => {
  form.value.contentList.splice(form.value.contentList.indexOf(item), 1)
}

const loading = ref(false)
const getCode = async () => {
  loading.value = true
  try{
    const res:any = await getCodeApi({type: typeMap[props.cateType].type})
    form.value.content = res;
    if (contentType === 'list') {
      form.value.contentList = res.split(',').map((item: any) => ({No: item}))
    }
  }catch (e) {
    console.log(e);
  }finally {
    loading.value = false
  }
}

const rules = ref({
  content: [
    {required: true, message: errorTips, trigger: 'change'}
  ]
})

watch(() => props.cateType, (value) => {
  getCode()
}, {immediate: true})

const tipText = computed(() => {
  return `如需在网页中使用${ label }功能与访客互动，则需在编辑页中加入商桥插件，并在此页添加${ label } ${ tips }`
})
</script>

<template>
  <div class="wrap h100">
    <top-tips :text="tipText"/>

    <el-form :style="`width: ${formWidth};`" v-loading="loading" ref="formRef" :model="form" label-width="120px" :rules="rules">
      <el-form-item :label="`${label}代码`" prop="content" v-if="contentType === 'code'">
        <el-input type="textarea" :rows="16" v-model="form.content"/>
      </el-form-item>

      <template v-else>
        <el-form-item
            v-for="(item, index) in form.contentList"
            :key="index"
            :label="index === 0 ? `${label}号码` : ''"
            :prop="'item.' + index + '.No'"
        >
          <div class="flex">
            <el-input v-model="item.No" style="width: 300px"/>
            <el-button v-if="form.contentList.length > 1" circle icon="Close" @click.prevent="removeDomain(item)" class="del-no"/>
          </div>
        </el-form-item>
      </template>

      <el-form-item>
        <div class="w100 flex" :style="`justify-content: ${buttonDisplay}`">
          <jzt-button v-for="(btn, index) in btns" :key="index" icon="icon-tianjia" :style="`width: ${btnWidth};`" @click="btn.onClick">
            {{ btn.label }}</jzt-button>
          <jzt-button class="active" size="large" :style="`width: ${btnWidth};`" @click="submit(formRef)">提交</jzt-button>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<style scoped lang="scss">
.wrap {
  background: #fff;
  margin-top: 10px;
  padding: 20px 30px;
  overflow: auto;
  .del-no {
    background: #E2E6ED;
    border-color: #E2E6ED;
    width: 20px;
    height: 20px;
    margin-left: 10px;
    :deep(.el-icon) {
      font-size: 16px;
      color: #fff;
    }
  }
}
</style>
