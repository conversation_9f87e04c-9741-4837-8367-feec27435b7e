<template>
  <div class="w100">
    <vue3-tinymce
        v-model="state.content"
        :setting="state.setting"
        @change="changeContent"
    />

    <edit-dialog
        ref="editDialogRef"
        title="从素材库导入"
        sure-text="导入"
        v-model:dialog-show="dialogShow"
        :customForm="true"
        width="810px"
        @sure="importImg"
    >
      <library
          ref="libraryDialog"
          v-model:library-img="editorImgs"
          :isRadio="false"
          :file-type="importType"
          @searchByName="searchByName"
      ></library>
    </edit-dialog>
  </div>
</template>

<script setup>
import {watch, ref, defineAsyncComponent} from "vue";
import {ElMessage} from "element-plus";

import {useDialog} from '@/hooks/useDialog';

const {EditDialog, editDialogRef, dialogShow} = useDialog()

const Vue3Tinymce = defineAsyncComponent(() => import("@/components/Global/Vue3Tinymce/main.vue"));
const Library = defineAsyncComponent(() => import("@/components/Uploads/Library.vue"));

const props = defineProps({
  content: {
    type: String,
    default: ""
  },
  height: {
    type: Number,
    default: 1000
  }
});

const emits = defineEmits(["update:content"]);

watch(
    () => props.content,
    newValue => {
      state.value.content = newValue;
    }
);
const {VITE_PROXY_DOMAIN, VITE_PROXY_DOMAIN_REAL} = process.env;
const BaseUrlEditor =
    process.env.NODE_ENV === "production"
        ? VITE_PROXY_DOMAIN_REAL
        : VITE_PROXY_DOMAIN;

const state = ref({
  content: props.content,
  // editor 配置项
  setting: {
    language: "zh_CN",
    height: props.height, // editor 高度
    menubar: "file edit insert view format tools help importfrombucket",
    menu: {
      importfrombucket: {
        title: "从素材库导入",
        items: "importfrombucket importfrombucketVideo"
      }
    },
    toolbar:
        "undo redo | fullscreen | formatselect alignleft aligncenter alignright alignjustify | link unlink | numlist bullist | image axupimgs media table | fontsizeselect forecolor backcolor | bold italic underline strikethrough | indent indent2em outdent | superscript subscript | removeformat | tpLayout formatpainter",
    toolbar_drawer: "sliding",
    quickbars_selection_toolbar:
        "removeformat | bold italic underline strikethrough | fontsizeselect forecolor backcolor | code",
    plugins:
        "link image media table lists fullscreen quickbars code axupimgs importfrombucket importfrombucketVideo indent2em tpLayout formatpainter",
    font_formats:
        "微软雅黑=Microsoft YaHei,Helvetica Neue,sans-serif;Microsoft YaHei,sans-serif;宋体=simsun,serif;仿宋体=FangSong,serif;楷体;黑体=SimHei,sans-serif;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;",
    fontsize_formats:
        "12px 14px 16px 18px 20px 22px 24px 26px 28px 30px 32px 34px 36px 38px 40px 42px 44px 46px 48px 50px",
    lineheight_formats: "1 1.5 2 2.5 3 3.5 4 4.5 5",
    default_link_target: "_blank",
    link_title: false,
    nonbreaking_force_tab: true,
    // 以中文简体为例
    language_url: "./tinymce/langs/zh-Hans.js",
    custom_images_upload: true,
    images_upload_url: BaseUrlEditor + "material/uploadImg",
    custom_images_upload_callback: response => response,

    custom_video_upload: true,
    file_picker_types: "media",
    custom_upload_url: BaseUrlEditor + "material/uploadVideo",
    custom_video_upload_callback: response => {
      console.log(response);
      return response;
    },

    // 粘贴图片后自动上传
    urlconverter_callback: function (url, node, on_save, name) {
      console.log(url, node, on_save, name);
      if (node === "img" && url.startsWith("blob:")) {
        tinymce.activeEditor && tinymce.activeEditor.uploadImages();
      }
      console.log(url, "url");
      return url;
    },

    // 从素材库导入
    import_from_bucket: true,
    import_from_bucket_callback: function (editor, type) {
      // emits("changeShowImportFromBucket", {editor, type});
      changeShowImportFromBucket({editor, type});
    },
    formats: {
      borderstyle: {selector: 'td,th',
        styles: {
          borderTopStyle: 'solid',
          borderRightStyle: 'solid',
          borderBottomStyle: 'solid',
          borderLeftStyle: 'solid',
        },
        remove_similar: true
      },
      bordercolor: {selector: 'td,th',
        styles: {
          borderTopColor: '#32CD32',
          borderRightColor: '#32CD32',
          borderBottomColor: '#32CD32',
          borderLeftColor: '#32CD32'
        },
        remove_similar: true
      },
      backgroundcolor: {selector: 'td,th', styles: {backgroundColor: '#006400'}, remove_similar: true},
      formatpainter_removeformat: [
        {
          selector: 'b,strong,em,i,font,u,strike,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins',
          remove: 'all',
          split: true,
          expand: false,
          block_expand: true,
          deep: true
        },
        {
          selector: 'span',
          attributes: ['style', 'class'],
          remove: 'empty',
          split: true,
          expand: false,
          deep: true
        },
        {
          selector: '*:not(tr,td,th,table)',
          attributes: ['style', 'class'],
          split: false,
          expand: false,
          deep: true
        }
      ]
    },
    custom_elements: 'style'
  }
});

const changeContent = content => {
  emits("update:content", content);
};

const Editor = ref(null);

const importType = ref(1);

const changeShowImportFromBucket = data => {
  importType.value = data.type;
  dialogShow.value = true;
  Editor.value = data.editor;
};

const editorImgs = ref([]);

// 素材库导入点击
const imgStr = ref("");
const importImg = () => {
  if (editorImgs.value) {
    dialogShow.value = false;
    imgStr.value = "";
    if (importType.value === 1) {
      for (let i = 0; i < editorImgs.value.length; i++) {
        if (
            Object.prototype.toString.call(editorImgs.value[i]) ===
            "[object Object]"
        ) {
          imgStr.value += "<img src='" + editorImgs.value[i].url + "'>";
        } else {
          imgStr.value += "<img src='" + editorImgs.value[i] + "'>";
        }
      }
    } else if (importType.value === 2) {
      for (let i = 0; i < editorImgs.value.length; i++) {
        if (
            Object.prototype.toString.call(editorImgs.value[i]) ===
            "[object Object]"
        ) {
          imgStr.value +=
              "<video src='" + editorImgs.value[i].url + "' controls></video>";
        } else {
          imgStr.value += "<video src='" + editorImgs.value[i] + "'></video>";
        }
      }
    }
    Editor.value.insertContent(imgStr.value);
  } else {
    ElMessage.error("请选择图片");
  }
};

// 搜索
const searchName = ref("");
const libraryDialog = ref(null);
const searchByName = () => {
  libraryDialog.value.getLibraryList(1);
};
</script>
