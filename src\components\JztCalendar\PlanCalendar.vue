<template>
  <div class="calendar-container">
    <div class="top-content flex">
      <div class="type-box flex">
        <span :class="['type-item', {'active': calendarType === 'week'}]" @click="calendarType = 'week'">周</span>
        <span :class="['type-item', {'active': calendarType === 'month'}]" @click="calendarType = 'month'">月</span>
      </div>
      <div class="date-box flex">
        <div class="prev-box flex" @click="changeMonth('left')">
          <i class="iconfont icon-zuofanyezuohua"></i>
        </div>
        <div class="currentDay">{{calendarTitle}}</div>
        <div class="prev-box flex" @click="changeMonth('right')">
          <i class="iconfont icon-youfanyeyouhua"></i>
        </div>
      </div>
      <div class="today-btn flex" @click="toToday">今天</div>
    </div>
    <slot name="calendar-top" />
    <div class="calendar-box">
      <div class="week-box flex">
        <div :class="['week-item', 'flex', 
          {'active': (calendarType === 'week' && dayInfo.length && dayInfo[index].year == todayTime.year && dayInfo[index].month == todayTime.month && dayInfo[index].date == todayTime.date) }]"
          v-for="(item, index) in weekInfo"
        >
          <p class="title">周{{item.label}}</p>
          <template v-if="calendarType === 'week' && dayInfo.length">
            <p class="date">{{handleDate(dayInfo[index], 'yyyy/M/d')}}</p>
            <div v-if="getDateStatus(dayInfo[index], index)" :class="['day-tag', getDateStatus(dayInfo[index], index).class]">{{ getDateStatus(dayInfo[index], index).label }}</div>
          </template>
        </div>
      </div>
      <div v-if="calendarType === 'week'" class="day-box flex-wrap" style="height: max-content;align-items: flex-start;min-height: auto;">
        <template v-for="(item, index) in dayInfo">
          <div :class="[item.class, 'day-item']" style="height: max-content;min-height: auto;">
            <!-- 数据列表 -->
            <div class="data-list-box">
              <template v-for="(v, i) in _filterData(item)">
                <div class="data-item flex" :style="{ backgroundColor: v.color + '20' }" @click="toAI(v)">
                  <span class="t-line" :style="{ backgroundColor: v.color }"></span>
                  <span class="data-label" :style="{ color: v.color }">{{ v.title }}</span>
                </div>
              </template>
            </div>
          </div>
        </template>
      </div>
      <div v-if="calendarType === 'month'" class="day-box flex-wrap">
        <template v-for="(item, index) in dayInfo">
          <div :class="[item.class, 'day-item', { 'active': todayTime.date == item.date && item.class != 'before' && item.class != 'after' }]">
            <div class="number-box flex">
              <div :class="['day-tag', getDateStatus(item, index).class]">{{ getDateStatus(item, index).label }}</div>
              <div :class="['num', item.class]"><span>{{item.date}}</span></div>
            </div>
            <!-- 数据列表 -->
            <div v-if="_filterData(item)" class="data-list-box">
              <template v-for="(v, i) in _filterData(item)">
                <div v-if="i < 3" class="data-item flex" :style="{ backgroundColor: v.color + '20' }" @click="toAI(v)">
                  <span class="t-line" :style="{ backgroundColor: v.color }"></span>
                  <span class="data-label one-line" :style="{ color: v.color }" :title="v.title">{{ v.title }}</span>
                </div>
              </template>
              <div v-if="_filterData(item).length > 3" class="data-item flex item-more">
                <span class="t-line"></span>
                <span class="data-label">还有{{ _filterData(item).length - 3 }}项</span>
                <!-- 更多数据 -->
                <div class="data-list-more-box">
                  <template v-for="(v, i) in _filterData(item)">
                    <div class="data-item flex" :style="{ backgroundColor: v.color + '20' }" @click="toAI(v)">
                      <span class="t-line" :style="{ backgroundColor: v.color }"></span>
                      <span class="data-label" :style="{ color: v.color }">{{ v.title }}</span>
                    </div>
                  </template>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import { useCalendar} from '@/hooks/useCalendar'

const { 
  calendarType,
  todayTime,
  weekInfo,
  dayInfo,
  calendarTitle,
  handleDate,
  getDateStatus,
  filterData,
  changeMonth,
  toToday,
  calendarData
} = useCalendar()

const emits = defineEmits(['changeDayInfo', 'toAI'])

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  }
})

// 数据筛选
const _filterData = (item: any) => {
  return filterData(item, props.data, 'date')
}

// 跳转AI
const toAI = (item: any) => {
  emits('toAI', item)
}

watch(
  () => dayInfo.value,
  (newVal) => {
    emits('changeDayInfo', newVal)
  }
)

watch(
  () => props.data,
  (newVal) => {
    calendarData.value = newVal
  }
)

defineExpose({ handleDate })
</script>

<style lang="scss" scoped>
.calendar-container {
  // 文字显示一行超出...
  .one-line {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .top-content {
    justify-content: space-between;
    margin-bottom: 20px;
    .type-box {
      width: 90px;
      height: 35px;
      .type-item {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        color: #999999;
        border: 1px solid #d9d9d9;
        box-sizing: border-box;
        cursor: pointer;
        &:first-child {
          border-radius: 4px 0 0px 4px;
          border-right-width: 0;
        }
        &:last-child {
          border-radius: 0px 4px 4px 0px;
          border-left-width: 0;
        }
        &.active {
          border: 1px solid #2859ff;
          color: #2859ff;
        }
      }
    }
    .date-box {
      .prev-box {
        width: 38px;
        height: 38px;
        border: 1px solid #d9d9d9;
        justify-content: center;
        box-sizing: border-box;
        border-radius: 4px;
        cursor: pointer;
        .iconfont {
          font-size: 24px;
          color: #999;
        }
      }
      .currentDay {
        font-size: 16px;
        color: #1c2b4b;
        margin: 0 20px;
      }
    }
    .today-btn {
      width: 70px;
      height: 35px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      justify-content: center;
      box-sizing: border-box;
      font-size: 16px;
      color: #999;
      cursor: pointer;
    }
  }
  .calendar-box {
    width: 1100px;
    margin: auto;
    border: 1px solid #f3f3f3;
    border-right-width: 0;
    .week-box {
      background: #f3f3f3;
      height: 50px;
      .week-item {
        width: calc(100% / 7);
        justify-content: center;
        flex-direction: column;
        position: relative;
        .title {
          font-size: 16px;
          color: #3f423f;
        }
        .date {
          font-size: 12px;
          color: #999;
        }
        .day-tag {
          width: 16px;
          height: 16px;
          line-height: 16px;
          font-size: 12px;
          color: #ffffff;
          text-align: center;
          position: absolute;
          right: 40px;
          top: 3px;
          &.holiday {
            background: #ff9494;
          }
          &.work {
            background: #8e8e8e;
          }
        }
        &.active {
          .title {
            color: #2859ff;
          }
        }
      }
    }
    .day-box {
      min-height: 775px;
      .day-item {
        width: calc(100% / 7);
        height: 155px;
        box-sizing: border-box;
        border: 1px solid #f3f3f3;
        border-left-width: 0;
        border-bottom-width: 0;
        position: relative;
        .number-box {
          justify-content: space-between;
          height: 34px;
          padding: 0 10px;
          .day-tag {
            width: 16px;
            height: 16px;
            line-height: 16px;
            font-size: 12px;
            color: #ffffff;
            text-align: center;
            &.holiday {
              background: #ff9494;
            }
            &.work {
              background: #8e8e8e;
            }
          }
          .num {
            text-align: right;
            span {
              font-size: 13px;
              color: #515151;
              width: 24px;
              height: 24px;
              border-radius: 50%;
              display: inline-flex;
              align-items: center;
              justify-content: center;
            }
            &.before,
            &.after {
              span {
                color: #c3c3c3;
              }
            }
            &.today {
              span {
                background: #0059ff;
                color: #ffffff;
              }
            }
          }
        }
        .data-list-box {
          .data-item {
            // min-height: 28px;
            cursor: pointer;
            margin-bottom: 4px;
            padding: 5px 0;
            position: relative;
            .t-line {
              display: inline-block;
              width: 2px;
              height: 100%;
              position: absolute;
              top: 0;
              left: 0;
            }
            .data-label {
              display: inline-block;
              font-size: 12px;
              margin: 0 10px;
              line-height: 1.5;
            }
            &.item-more {
              justify-content: center;
              margin-bottom: 0;
              color: #d0d0d0;
              height: 20px;
              margin-left: 0;
              position: inherit;
              padding: 0;
              .data-list-more-box {
                position: absolute;
                top: -14px;
                left: -13px;
                width: calc(100% + 26px);
                padding: 10px;
                box-shadow: 2px 2px 12px 0px rgba(58, 59, 62, 0.1);
                border-radius: 4px;
                border: 1px solid rgba(222, 226, 235, 1);
                background: #fff;
                display: none;
                z-index: 4;
              }
              &:hover {
                .data-list-more-box {
                  display: block;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>