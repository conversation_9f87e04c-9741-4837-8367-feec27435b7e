<template>
  <div class="search-view flex">
    <type-select class="width180 mr10" v-model="search.catid" :apiType="apiType" :cateType="cateType"></type-select>
    <el-input v-if="showTitle" class="width300" v-model="search.title" placeholder="请输入标题" clearable></el-input>
  </div>
</template>

<script setup lang="ts">
import { computed, defineAsyncComponent, ref } from 'vue'

const TypeSelect = defineAsyncComponent(() => import('./TypeSelect.vue'))

const props = defineProps({
  cateType: {
    type: String,
    default: ''
  },
  searchData: {
    type: Object,
    default: () => {}
  },
  apiType: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['refresh', 'update:searchData'])

const showTitle = computed(() => {
  switch (props.cateType) {
    case 'content':
    case 'goods':
      return true
    default:
      return false
  }
})

const search = computed({
  get: () => props.searchData,
  set: (val) => {
    emit('update:searchData', val)
  }
})

/*const title = ref('')

const getTableData = () => {
  // props.searchData.title = title.value
  emit('refresh')
}*/

</script>

<style scoped lang="scss">

</style>
