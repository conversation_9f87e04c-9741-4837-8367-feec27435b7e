<template>
  <edit-dialog ref="editDialogRef" v-loading="uploading" v-model:dialog-show="dialogShow" v-bind="dialogConfig" @sure="sure">
    <div class="material-wrap height500 flex-y">
      <div class="material-link flex">
        <el-input v-model="link" placeholder="设置网址链接（如有多个链接请用“,”隔开）" />
        <jzt-button name="开始获取" class="active flex-shrink ml10" @click="getMaterial" />
      </div>
      <div class="file-list flex-1 flex-y mt10">
        <div class="check-all">
          <el-checkbox v-model="checkAll" label="全选" @change="checkAllChange" />
        </div>
        <div id="library-list-box" class="library-list-box flex-1" v-loading="loading">
          <template v-if="materialList.length > 0">
            <div class="library-list flex" id="library-list">
              <template v-for="(item, index) in materialList" :key="index">
                <div :class="['box_item', { active: item.isChecked }]" @click="checkItem(index, $event)">
                  <div class="item-info-box">
                    <div class="item-check-box">
                      <el-checkbox name="type" v-model="item.isChecked" />
                    </div>
                    <div class="img-title img video-box">
                      <img :src="item.url" @error="imgError($event, 'img')" />
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </template>
          <div class="none-data h100 flex-c-center" v-else>{{ filterText }}</div>
        </div>
      </div>
    </div>
  </edit-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useDialog } from '@/hooks/useDialog'
import { checkSpaceCountApi } from '@/api/upload'
import { getPickUpListApi, uploadImgByAiApi } from '@/api/bucket'
import { ElMessage } from 'element-plus'
import { useLibrary } from '@/hooks/useLibrary'

const { EditDialog, editDialogRef, dialogData, dialogShow, openDialog } = useDialog()
const { imgError } = useLibrary()

const props = defineProps({
  type: {
    type: Number,
    default: 1
  },
  classid: {
    type: Number,
    default: 0
  }
})

const dialogConfig = ref({
  title: '智能获取',
  width: '800px',
  customForm: true,
  sureText: '确认上传',
})

const link = ref('')

// 智能获取
const loading = ref(false)
const isLoaded = ref(false)
const getMaterial = () => {
  if (loading.value) return
  if (!link.value) return ElMessage.warning('请输入链接')
  link.value = link.value.replace(/，/g, ',')
  let list = link.value.split(',')
  // 判断链接是否以http或者https开头 没有加上
  list = list.map(item => item.startsWith('http://') || item.startsWith('https://') ? item : 'http://' + item)
  console.log(list, 'list')
  loading.value = true
  isLoaded.value = false
  checkAll.value = false
  materialList.value = []
  getPickUpListApi({
    linkurllist: list.join(','),
    type: props.type
  }).then((res: any) => {
    console.log(res, 'getMaterial')
    isLoaded.value = true
    let imgList = res || []
    imgList = imgList.map((item: any) => {
      return {
        url: item,
        isChecked: false
      }
    })
    materialList.value = imgList
  }).finally(() => {
    loading.value = false
  })
}

const filterText = computed(() => {
  return link.value && isLoaded.value ? '未获取到图片' : '请输入链接获取'
})

const checkAll = ref(false)
const materialList: any = ref([])
const checkAllChange = () => {
  materialList.value.forEach((item: any) => {
    item.isChecked = checkAll.value
  })
}

const checkItem = (index: number, event: any) => {
  materialList.value[index].isChecked = !materialList.value[index].isChecked
}

// 确认上传
const uploading = ref(false)
const sure = async () => {
  console.log(materialList.value, 'sure')
  if (uploading.value) return
  if (materialList.value.length === 0) return ElMessage.warning('请获取图片')
  let list = materialList.value.filter((item: any) => item.isChecked)
  if (list.length === 0) return ElMessage.warning('请选择图片')
  uploading.value = true
  for (let i = 0; i < list.length; i++) {
    await uploadImg(list[i].url, i)
  }
  uploading.value = false
  dialogShow.value = false
}

// 上传图片
const uploadImg = async (url: string, index: number) => {
  return new Promise(async (resolve, reject) => {
    // 检测空间容量 
    let formData = new FormData()
    formData.append('url', url)
    checkSpaceCountApi(formData).then((res: any) => {
      console.log(res, 'res')
      uploadImgByAiApi({
        type: props.type,
        classid: props.classid,
        fileurl: url
      }).then((res: any) => {
        console.log(res, 'res')
        ElMessage.success(`第${index + 1}张图片上传成功`)
        resolve(true)
      }).catch(() => {
        ElMessage.error(`第${index + 1}张图片上传失败`)
        resolve(false)
      })
    }).catch(() => {
      ElMessage.error(`第${index + 1}张图片上传失败`)
      resolve(false)
    })
  })
}

defineExpose({ openDialog })
</script>

<style scoped lang="scss">
@import '@/assets/css/bucket.scss';

.material-wrap {
  .file-list {
    .check-all {
      padding: 0 10px;
    }
    .library-list-box {
      .library-list {
        .box_item {
          height: auto;
          margin-bottom: 10px;
        }
      }
    }
  }
}
</style>
