.wrap{
  overflow: auto;
  .content-box {
    background: rgba(255, 255, 255, 0.99);
    box-shadow: 0px 6px 12px 0px rgba(216, 224, 234, 0.2);
    padding: 20px 30px;
    width: 100%;
    box-sizing: border-box;
    margin-top: 10px;
    .t-box{
      padding-top: 24px;
      padding-bottom: 24px;
      .t-title{
        font-size: 16px;
        font-family: <PERSON><PERSON>ang;
        font-weight: bold;
        color: #333333;
        line-height: 1.5;
      }
      .t-time {
        font-size: 14px;
        font-family: <PERSON><PERSON>ang;
        font-weight: 500;
        color: #999999;
        margin-top: 10px;
      }
    }
    .border{
      border-bottom: 1px solid #E3E5EC;
    }
    .p-title {
      font-size: 14px;
      color: #333;
    }
    .mt-20 {
      margin-top: 20px;
    }
    .px-20{
      padding: 0 20px;
    }
    .filter-box{
      :deep(.el-date-editor--daterange){
        border-radius: 34px;
        width: 100%;
      }
      .tips{
        font-size: 14px;
        font-family: <PERSON><PERSON><PERSON>;
        font-weight: 500;
        color: #999999;
        margin-left: 15px;
        line-height: 34px;
      }
    }
  }
}
