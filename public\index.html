<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <title><%= htmlWebpackPlugin.options.title %></title>
  </head>
  <body>
    <noscript>
      <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app">
      <style>
        html,
        body,
        #app {
          width: 100%;
          height: 100%;
          display: flex;
          position: relative;
          justify-content: center;
          align-items: center;
          overflow: hidden;
        }
      
        .loader,
        .loader:before,
        .loader:after {
          border-radius: 50%;
          width: 2.5em;
          height: 2.5em;
          -webkit-animation-fill-mode: both;
          animation-fill-mode: both;
          -webkit-animation: loadAnimation 1.8s infinite ease-in-out;
          animation: loadAnimation 1.8s infinite ease-in-out;
        }
      
        .loader {
          color: #2859FF;
          font-size: 10px;
          margin: 80px auto;
          position: relative;
          text-indent: -9999em;
          -webkit-transform: translateZ(0);
          -ms-transform: translateZ(0);
          transform: translateZ(0);
          -webkit-animation-delay: -0.16s;
          animation-delay: -0.16s;
          top: 0;
          transform: translate(-50%, 0);
        }
      
        .loader:before,
        .loader:after {
          content: "";
          position: absolute;
          top: 0;
        }
      
        .loader:before {
          left: -3.5em;
          -webkit-animation-delay: -0.32s;
          animation-delay: -0.32s;
        }
      
        .loader:after {
          left: 3.5em;
        }
      
        @-webkit-keyframes loadAnimation {
      
          0%,
          80%,
          100% {
            box-shadow: 0 2.5em 0 -1.3em;
          }
      
          40% {
            box-shadow: 0 2.5em 0 0;
          }
        }
      
        @keyframes loadAnimation {
      
          0%,
          80%,
          100% {
            box-shadow: 0 2.5em 0 -1.3em;
          }
      
          40% {
            box-shadow: 0 2.5em 0 0;
          }
        }
      </style>
      <div class="loader"></div>
    </div>
    <!-- built files will be auto injected -->
  </body>
</html>
