<script lang="ts" setup>
import {defineAsyncComponent} from "vue";
import {consultDeleteApi, consultListApi} from "@/api/interaction";
import {ElMessage} from "element-plus";
import {useInteraction} from "@/views/content/consult/hooks/useInteraction";

const DataList = defineAsyncComponent(() => import('@/components/Global/DataList.vue'))

const {
  dataListRef,
  tableConfig,
  actionList,
  dataAction,
  del
} = useInteraction(
    {
      api: consultListApi,
      columns: [
        {label: '手机号', prop: 'mobile'},
        {label: '咨询时间', prop: 'create_time', sortable: 'custom'},
        {label: '操作', prop: 'action', type: 'slot', align: 'center', width: 280}
      ]
    },
    [],
    consultDeleteApi,
    '/newapi/ContentList/zx_export'
)

/**
 * 复制（只复制手机号）
 * @param row
 */
const copy = (row: any) => {
  const text = row.mobile
  // 复制手机号到剪贴板，兼容不同浏览器
  if (!navigator.clipboard) {
    // 如果浏览器不支持 navigator.clipboard API
    // 则使用 document.execCommand('copy') 方法
    const textarea = document.createElement('textarea');
    textarea.value = text;
    textarea.style.position = 'fixed';
    document.body.appendChild(textarea);
    textarea.focus();
    textarea.select();
    try {
      const successful = document.execCommand('copy');
      if (successful) {
        ElMessage.success('复制成功');
      } else {
        ElMessage.error('复制失败');
      }
    } catch (err) {
      ElMessage.error('复制失败');
      console.error('复制失败:', err);
    } finally {
      document.body.removeChild(textarea);
    }
  } else {
    // 如果浏览器支持 navigator.clipboard API
    navigator.clipboard.writeText(text).then(() => {
      ElMessage.success('复制成功');
    }).catch(err => {
      ElMessage.error('复制失败:', err);
    });
  }
}
</script>

<template>
  <div class="consult h100">
    <data-list ref="dataListRef" :tableConfig="tableConfig" :action-default="false" @clickBtn="dataAction"
               :actionList="actionList" v-model:multipleSelection="tableConfig.multipleSelection">
      <template #action="{ row }">
        <div class="table-action flex">
          <div class="item" @click="copy(row)">
            <i class="iconfont icon-fuzhi1"></i> <span>复制</span>
          </div>
          <div class="item" @click="del(row)">
            <i class="iconfont icon-shanchu1"></i> <span>删除</span>
          </div>
        </div>
      </template>
    </data-list>
  </div>
</template>

<style scoped lang="scss">

</style>
