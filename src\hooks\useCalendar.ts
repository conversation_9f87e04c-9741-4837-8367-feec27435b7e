import { ref, reactive, watch, onMounted } from "vue"
import { getDateObj } from "@/utils"
import axios from "axios"

export const useCalendar = () => {

  const calendarType = ref('week') // 日历类型 周历 月历
  const dayInfo:any = ref([])  // 日历数字
  const calendarTitle = ref('')  // 日历标题
  const holidayData:any = ref({})  // 节假日数据
  const calendarData:any = ref([])  // 日历数据
  const weekInfo = ref([
    { week: 1, label: '一', active: false },
    { week: 2, label: '二', active: false },
    { week: 3, label: '三', active: false },
    { week: 4, label: '四', active: false },
    { week: 5, label: '五', active: false },
    { week: 6, label: '六', active: false },
    { week: 0, label: '日', active: false }
  ])

  let calendarDate = ref(getDateObj())  // 日历日期
  const todayTime = reactive(getDateObj())  // 今天的日期
  const nowDate:any = ref(null)  // 当前选中几号

  const createCalendar = () => {
    let { year, month, date, week, days, beforeDays, firstDayWeek } = calendarDate.value
    let { year: todayYear, month: todayMonth, date: todayDate } = todayTime
    nowDate.value = date
    // console.log('今天是' + year + '年' + month + '月' + date + '日，星期' + week)
    // console.log(firstDayWeek, '本月第一天星期几')
    if (firstDayWeek == 0) firstDayWeek = 7
    // 判断星期
    weekInfo.value.forEach(element => {
      if (element.week == week) element.active = true
      else element.active = false
    });
    switch (calendarType.value) {
      case 'week':
        createWeekCalendar({ year, month, date, week, days, beforeDays, firstDayWeek, todayYear, todayMonth, todayDate })
        break;
      case 'month':
        createMonthCalendar({ year, month, days, beforeDays, firstDayWeek, todayYear, todayMonth, todayDate })
        break;
    }
  }

  // 生成月历
  const createMonthCalendar = (obj:any) => {
    let { year, month, days, beforeDays, firstDayWeek, todayYear, todayMonth, todayDate } = obj
    //生成日历
    calendarTitle.value = year + '年' + month + '月'
    //生成月历
    let dayList = [], beforeList = [], afterList = [], count = 42, after = 1
    for (let i = 1; i <= days; i++) {
      if (i == todayDate && year == todayYear && month == todayMonth) dayList.push({ year, month, date: i, class: 'today'})
      else dayList.push({ year, month, date: i, class: ''})
    }
    let beforeDay = beforeDays
    for (let j = 0; j < firstDayWeek - 1; j ++) {
      var { year: _year, month: _month} = handleDateChange({year, month}, 'prev')
      beforeList.unshift({ date: beforeDay--, month: _month, year: _year, class: 'before'})
    }
    dayList = beforeList.concat(dayList)
    // console.log(dayList, 'dayList')
    if (count - dayList.length >= 7) {
      count = 35
    }
    for (let e = 0; e < count - dayList.length; e ++) {
      var { year: _year, month: _month} = handleDateChange({year, month}, 'next')
      afterList.push({ date: after++, month: _month, year: _year, class: 'after'})
    }
    dayList = dayList.concat(afterList)
    dayInfo.value = dayList
  }

  // 生成周历
  const createWeekCalendar = (obj:any) => {
    let { year, month, date, week, days, beforeDays, firstDayWeek, todayYear, todayMonth, todayDate } = obj
    // 生成周历
    let weekList:any = [], weekBefore:any = [], weekAfter:any = []
    for (let i = 1; i < week; i++) {
      if (date - i < 1) {
        var { year: _year, month: _month} = handleDateChange({year, month}, 'prev')
        weekBefore.unshift({ date: date - i + beforeDays, month: _month, year: _year, class: ''})
      } else weekBefore.unshift({ date: date - i, month, year, class: ''})
    }
    weekList = weekList.concat(weekBefore)
    weekList.push({ date, month, year, class: ''})
    for (let i = 1; i <= 7 - week; i++) {
      if (date + i > days) {
        var { year: _year, month: _month} = handleDateChange({year, month}, 'next')
        weekAfter.push({ date: date + i - days, month: _month, year: _year, class: ''})
      } else weekAfter.push({ date: date + i, month, year, class: ''})
    }
    weekList = weekList.concat(weekAfter)
    let title  = ''
    title += handleDate(weekList[0], 'yyyy年M月d日')
    title += ' ~ '
    title += handleDate(weekList[6], 'yyyy年M月d日')
    title += '(第' + getWeeksOfYear(year + '-' + month + '-' + date) + '周)'
    calendarTitle.value = title
    dayInfo.value = weekList
  }

  // 日历月份切换
  const changeMonth = (key:any) => {
    let { year, month, date, days, beforeDays } = JSON.parse(JSON.stringify(calendarDate.value))
    let toYear = year, toMonth = month, toDate = date
    switch (key) {
      case 'left':
        if (calendarType.value == 'week') {
          if (toDate - 7 < 1) {
            toMonth = toMonth - 1
            if (toMonth == 0) toYear = toYear - 1, toMonth = 12
            toDate = toDate - 7 + beforeDays
          }
          else toDate = toDate - 7
        } else if (calendarType.value == 'month') {
          if (toMonth == 1) toYear = year - 1, toMonth = 12
          else toMonth --
          toDate = 1
        }
        break;
      case 'right':
        if (calendarType.value == 'week') {
          if (toDate + 7 > days) {
            toMonth = toMonth + 1
            if (toMonth == 13) toYear = year + 1, toMonth = 1
            toDate = toDate + 7 - days
          }
          else toDate = toDate + 7
        } else if (calendarType.value == 'month') {
          if (toMonth == 12) toYear = year + 1, toMonth = 1
          else toMonth ++
          toDate = 1
        }
        break;
    }
    // console.log(`${toYear}-${toMonth}-${date}`, '日历日期')
    calendarDate.value = getDateObj(toYear + '-' + toMonth + '-' + toDate)
    // console.log(calendarDate.value, 'calendarDate.value')
    createCalendar()
  }

  // 点击日历
  const calendarClick = (number:any) => {
    let { year, month } = JSON.parse(JSON.stringify(calendarDate.value))
    nowDate.value = number
  }

  // 过滤当前日期的数据
  const filterData = ({ year, month, date }:any, list: any = [], key = 'datetime') => {
    let monthStr = month.toString().padStart(2, '0')
    let dateStr = date.toString().padStart(2, '0')
    let time = key == 'datetime' ? `${year}-${monthStr}-${dateStr}` : `${monthStr}-${dateStr}`
    let info: any = {}
    if (!list) return
    let newArr:any = list.filter((item:any) => item[key] == time)
    if (newArr.length > 0) info = newArr[0]
    if (key == 'date') return info.list
    return info
  }

  // 处理年月日的切换
  const handleDateChange = ({ year, month }:any, key:any) => {
    if (key == 'prev') {
      if (month == 1) {
        month = 12, year = year - 1
      } else month = month - 1
    } else if (key == 'next') {
      if (month == 12) {
        month = 1, year = year + 1
      } else month = month + 1
    }
    return { year, month }
  }

  // 跳转当前
  const toToday = () => {
    let { year, month, date } = JSON.parse(JSON.stringify(todayTime))
    calendarDate.value = getDateObj(year + '-' + month + '-' + date)
    createCalendar()
  }

  // 获取当前日期是本年的第几周
  const getWeeksOfYear = (date:any) => {
    let dateTimeNow:any = new Date(date);
    const year = dateTimeNow.getFullYear();
    let dateTime:any = new Date(year, 0, 1, 0, 0, 0);
    let ad_justed;
    if (dateTime.getDay() === 0) {
      ad_justed = 1;
    } else {
      ad_justed = 8 - dateTime.getDay();
    }
    dateTime = dateTime.setDate(dateTime.getDate() + ad_justed); // 获取元旦后第一个周一0点的时间
    dateTime = new Date(dateTime);
    dateTimeNow = dateTimeNow.setDate(dateTimeNow.getDate());
    // 毫秒，所以要多除1000
    let s_week:any = parseInt(`${(dateTimeNow - dateTime) / 1000 / (86400 * 7)}`, 10) + 2;
    if (s_week < 10) {
      s_week = `0${s_week}`;
    }
    return s_week;
  }

  // 处理日期 分隔符
  const handleDate = (e:any, str = 'yyyy-M-d') => {
    let { year, month, date } = e
    // 替换
    return `${str.replace('yyyy', year).replace('M', month).replace('d', date)}`
  }

  // 获取当前日期状态 休息 上班
  const getDateStatus = (e:any, index:any) => {
    let { year, month, date } = e
    var list = calendarData.value
    if (!list.length) return
    // 当前日期
    var dateInfo = ''
    // 小于10 补0
    if (month < 10) dateInfo += '0' + month
    else dateInfo += month
    dateInfo += '-'
    if (date < 10) dateInfo += '0' + date
    else dateInfo += date
    var dateStatus = {
      class: '',
      label: ''
    }
    var obj:any = list.filter((item:any) => item.date == dateInfo)
    if (obj.length) {
      var info = obj[0]
      if (info.holiday == 1) dateStatus.class = 'holiday', dateStatus.label = '休'
      else if (info.holiday == 2) dateStatus.class = 'work', dateStatus.label = '班'
    }
    return dateStatus
  }

  // 获取节假日
  const getHoliday = (year:any) => {
    return new Promise(async (resolve:any, reject:any) => {
      if (holidayData.value[year]) {
        resolve(holidayData.value[year])
      } else {
        // 获取节假日
        axios.get(`https://timor.tech/api/holiday/year/${year}`).then(res => {
          console.log(res, 'res-holiday')
          if (res.data.code == 0) {
            holidayData.value[year] = res.data.holiday
            resolve(res.data.holiday)
          }
        })
      }
    })
  }

  watch(
    () => calendarType.value,
    () => {
      createCalendar()
    }
  )

  onMounted(() => {
    createCalendar()
  })

  return {
    calendarType,
    todayTime,
    weekInfo,
    dayInfo,
    calendarTitle,
    handleDate,
    getDateStatus,
    filterData,
    changeMonth,
    toToday,
    calendarData
  }

}