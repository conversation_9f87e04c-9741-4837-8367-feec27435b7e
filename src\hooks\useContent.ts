import { ref, defineAsyncComponent, computed } from 'vue'
import { cateTypeDic, cateNavList } from '@/utils/contentType'

export const useContent = (type: string) => {

  const ListView = defineAsyncComponent(() => import('@/views/content/components/list.vue'))
  const InfoAddView = defineAsyncComponent(() => import('@/views/content/components/infoAdd.vue'))
  const ListTypeView = defineAsyncComponent(() => import('@/views/content/components/ListType.vue'))
  const LibraryAddView = defineAsyncComponent(() => import('@/views/content/components/LibraryAdd.vue'))
  const ResumeListView = defineAsyncComponent(() => import('@/views/content/components/ResumeList.vue'))
  const WeChatSyncView = defineAsyncComponent(() => import('@/views/content/components/WeChatSync.vue'))


  const columnType = ref(cateTypeDic[type])
  const navList = ref(cateNavList(type))

  const cateName = computed(() => columnType.value && columnType.value.name || '内容')

  const components:any = {
    'list': ListView,
    'add': InfoAddView,
    'list-type': ListTypeView,
    'library-add': LibraryAddView,
    'resume-list': ResumeListView,
    'we-chat-sync': WeChatSyncView
  }

  return { columnType, cateName, navList, components }

}