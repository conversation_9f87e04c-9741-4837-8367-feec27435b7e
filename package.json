{"name": "jzt1.1-vue3", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@types/lodash": "^4.17.13", "axios": "^1.7.2", "core-js": "^3.8.3", "echarts": "^5.5.1", "element-plus": "^2.8.7", "file-saver": "^2.0.5", "html-docx-js-typescript": "^0.1.5", "html2canvas": "^1.4.1", "js-cookie": "^3.0.5", "jspdf": "^2.5.2", "nprogress": "^0.2.0", "qrcode.vue": "^3.6.0", "sortablejs": "^1.15.3", "swiper": "^11.1.14", "vue": "^3.2.13", "vue-class-component": "^8.0.0-0", "vue-cropper": "^1.1.4", "vue-router": "^4.0.3", "vuex": "^4.0.0"}, "devDependencies": {"@types/file-saver": "^2.0.7", "@types/html-docx-js": "^0.3.4", "@types/jquery": "^3.5.32", "@types/js-cookie": "^3.0.6", "@types/nprogress": "^0.2.3", "@types/sortablejs": "^1.15.8", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-typescript": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "sass": "^1.32.7", "sass-loader": "^12.0.0", "typescript": "^5.4.4"}, "volta": {"node": "16.17.0"}}