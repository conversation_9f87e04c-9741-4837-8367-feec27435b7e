<script setup lang='ts'>
const pdfWidth = 'calc(841.89px * 1.5)';
const pdfHeight = 'calc(595.28px * 1.5)';
</script>

<template>
  <div class="pdf-page-box" :style="`width: ${pdfWidth};height: ${pdfHeight};`">
    <div style="padding: 80px;">
      <slot name="textInfo"></slot>
      <slot name="canvas"></slot>
    </div>
    <p style="text-align: center;margin-bottom: 80px;font-size: 18px;">
      <span>本报告由资海云提供技术支持</span>
    </p>
  </div>
</template>

<style scoped lang='scss'>
/* 关于pdf 样式 */
.pdf-page-box {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  // padding-top: 30px;
  // padding-bottom: 30px;
  box-sizing: border-box;
}
</style>
