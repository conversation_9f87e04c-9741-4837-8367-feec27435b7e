<!-- 子菜单 -->
<template>
  <div
    :class="['item', { 'is-top': isTop }, { 'is-parent': isParent }, { 'show-line': isParent && isOpen }, {'is-iframe': isIframe}]"
    :data-url="item.path" :id="item.id" :title="item.title || item.meta.title" @click="toPath(item)">
    <p :class="['m-title', 'flex-align-center', { selected: itemActive(item) }]">
      <span class="flex-1 ellipsis-1">{{ item.title || item.meta.title }}</span>
      <i v-if="isParent" :class="['iconfont', 'ml6', isOpen ? 'icon-shangjiantou' : 'icon-arrow-down']" @click.stop="showSubmenu(item)" />
    </p>
    <!-- 子级，支持无限递归 -->
    <!-- 动画 -->
    <el-collapse-transition>
      <div v-if="isParent" v-show="isOpen" :class="['submenu', `level-${level}`]">
        <template v-for="child in validChildren" :key="child.id">
          <!-- 递归调用MenuTree组件处理每个子项 -->
          <menu-tree
            :data="child"
            :index="child.id"
            :level="level + 1"
            @toPath="handleChildToPath"
          />
        </template>
      </div>
    </el-collapse-transition>
  </div>
</template>

<script lang="ts" setup>
import { computed, defineAsyncComponent } from 'vue';
import { ElMessageBox } from 'element-plus';
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

const MenuTree = defineAsyncComponent(() => import('./MenuTree.vue'))

const props = defineProps({
  data: {
    type: Object,
    default: () => {}
  },
  index: {
    type: Number,
    default: 0
  },
  isLast: {
    type: Boolean,
    default: false
  },
  level: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['toPath'])

const item = computed(() => props.data)

const isOpen = computed(() => {
  if (item.value.isOpen === undefined) {
    // 顶级菜单默认展开，子菜单默认折叠
    item.value.isOpen = props.level === 0
  }
  return item.value.isOpen
})

const isParent = computed(() => {
  if (props.isLast && item.value.pid === 0) return false
  if (item.value.children && item.value.children.length > 0) {
    let sidebar = item.value.children.filter((v: { status: any; meta: any; }) => v.status === 1 || (v.meta && v.meta.isNav))
    if (sidebar.length) return true
  }
  return false
})

const isTop = computed(() => +item.value.pid === 0)

// 获取有效的子菜单项
const validChildren = computed(() => {
  if (!item.value.children || !item.value.children.length) return []
  return item.value.children.filter((v: { status: any; meta: any; }) => v.status === 1 || (v.meta && v.meta.isNav))
})

// 递归子集 是否有选中
const itemActive = (v: any) => {
  if (v.type) {
    return route.query.id == v.id
  } else {
    return route.path === v.path
  }
}

const showSubmenu = (v: any) => {
  v.isOpen = !v.isOpen
}

// 处理子组件的toPath事件
const handleChildToPath = (v: any) => {
  emit('toPath', v)
}

const toPath = (v: any) => {
  // 是否外链
  if (v.meta && v.meta.noJump) {
    if (isParent.value) v.isOpen = !v.isOpen
    return
  }
  //  || (v.type == 2 && v.url && v.url !== 'javascript:;)'
  let isExternal = v.redirect && v.redirect.includes('http')
  if (isExternal) window.open(v.redirect || v.url, '_blank')
  else if (v.meta && v.meta.tips) {
    ElMessageBox.confirm('直接编辑网站页面将有可能导致页面错乱等风险，建议您联系我们专业的设计师进行页面修改！', '是否确认继续编辑网站页面？', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      router.push(v.path)
    }).catch(() => {
      return
    })
  }
  else if(v.path) router.push(v.path)
  else if (v.type) router.push({ path: '/page', query: { jwp_id: v.jwp_id, id: v.id, title: v.title, type: v.type } })
  emit('toPath', v)
}

const isIframe = computed(() => {
  return true
  // return window.self !== window.top
})
</script>

<style lang='scss' scoped>
.item {
  cursor: pointer;
  position: relative;
  overflow: hidden;

  &.is-iframe {
    &.is-top {
      line-height: 36px;
      margin: 0 7px 0 7px;
      &.show-line{
        margin-bottom: 10px;
        &::after {
          left: 11px;
          right: 11px;
        }
      }
      & + .is-top {
        margin-top: 0;
      }
    }
    .m-title {
      font-weight: bold;
      font-size: 15px;
      color: #111111;
      padding-left: 11px;
      padding-right: 11px;
      .iconfont {
        font-weight: 600;
        font-size: 14px;
        color: #666;
      }
    }
    &.is-parent {
      .m-title {
        padding-right: 5px;
      }
    }
    .submenu{
       padding-left: 0;
       padding-right: 0;
      .m-title{
        color: #666666;
        padding-left: 16px;
        font-weight: normal;
        font-size: 14px;
      }
      .item{
        height: 34px;
        line-height: 34px;
      }
    }
  }

  &.selected {
    background: #f5f9ff;

    .m-title {
      color: #2859ff !important;
    }
  }

  &.is-top {
    line-height: 40px;
    cursor: pointer;
    position: relative;


    &.show-line {
      padding-bottom: 10px;

      &::after {
        content: "";
        display: block;
        position: absolute;
        bottom: 0;
        left: 26px;
        right: 26px;
        height: 1px;
        background: #e8ebee;
      }
    }

    &+.is-top {
      margin-top: 10px;
    }
  }

  .icon-box {
    width: 80px;
    height: 20px;
    justify-content: center;
    display: none;
  }

  .active-icon {
    display: none;
  }

  .m-title {
    font-size: 14px;
    font-weight: 500;
    color: #696c7d;
    transition: all ease-in-out .3s;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    &.selected {
      background: #f5f9ff;
      color: #2859ff;
    }
  }
}

.is-top {
  .m-title {
    font-weight: bold;
    font-size: 16px;
    color: #222222;
    padding-left: 16px;
    padding-right: 16px;
    box-sizing: border-box;

    .iconfont {
      transform: translateY(-2px);
    }
  }
}

.is-parent {
  .m-title {
    justify-content: space-between;
  }
}

.submenu {
  padding-left: 16px;
  padding-right: 16px;

  .m-title {
    font-weight: 500;
    font-size: 14px;
    color: #666666;
    padding-left: 16px;
    overflow: visible;
  }

  .item {
    height: 36px;
    line-height: 36px;
  }

  // 多层级缩进支持
  &.level-1 {
    padding-left: 16px;
    .m-title {
      padding-left: 16px;
    }
  }

  &.level-2 {
    padding-left: 16px;
    .m-title {
      padding-left: 32px;
    }
  }

  &.level-3 {
    padding-left: 16px;
    .m-title {
      padding-left: 48px;
    }
  }

  &.level-4 {
    padding-left: 16px;
    .m-title {
      padding-left: 64px;
    }
  }

  // 通用层级样式，支持更深层级
  @for $i from 5 through 10 {
    &.level-#{$i} {
      padding-left: 16px;
      .m-title {
        padding-left: #{16 + ($i * 16)}px;
      }
    }
  }
}
</style>
