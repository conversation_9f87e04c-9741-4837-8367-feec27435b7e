<!-- 递归菜单组件 -->
<template>
  <div :class="menuItemClass">
    <!-- 菜单标题区域 -->
    <div
      :class="menuTitleClass"
      :style="menuTitleStyle"
      @click="handleMenuClick"
    >
      <!-- 层级缩进指示器 -->
      <div v-if="level > 0" :class="['level-indicator', `level-${level}`]"></div>

      <!-- 菜单图标（如果有） -->
      <i v-if="menuData.icon" :class="['menu-icon', 'iconfont', menuData.icon]"></i>

      <!-- 菜单标题 -->
      <span :class="['menu-text', { 'has-children': hasChildren }]">
        {{ menuData.title || menuData.meta?.title }}
      </span>

      <!-- 展开/折叠图标 -->
      <i
        v-if="hasChildren"
        :class="expandIconClass"
        @click.stop="toggleExpand"
      ></i>
    </div>

    <!-- 子菜单容器 -->
    <el-collapse-transition>
      <div v-if="hasChildren && isExpanded" :class="submenuClass">
        <menu-tree
          v-for="child in validChildren"
          :key="child.id"
          :menu-data="child"
          :level="level + 1"
          :max-level="maxLevel"
          @menu-click="handleChildMenuClick"
        />
      </div>
    </el-collapse-transition>
  </div>
</template>

<script lang="ts" setup>
import { computed, defineAsyncComponent, ref } from 'vue';
import { ElMessageBox } from 'element-plus';
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

const MenuTree = defineAsyncComponent(() => import('./MenuTree.vue'))

// Props定义
const props = defineProps({
  menuData: {
    type: Object,
    required: true,
    default: () => ({})
  },
  level: {
    type: Number,
    default: 0
  },
  maxLevel: {
    type: Number,
    default: 2 // 默认最多显示2层
  }
})

// Events定义
const emit = defineEmits(['menu-click'])

// 响应式数据
const isExpanded = ref(props.level === 0) // 顶级菜单默认展开，子菜单默认折叠

// 计算属性
const menuData = computed(() => props.menuData)

// 是否有子菜单（考虑最大层级限制）
const hasChildren = computed(() => {
  // 如果当前层级已达到最大层级，则不显示子菜单
  if (props.level >= props.maxLevel) {
    return false
  }
  return validChildren.value.length > 0
})

// 获取有效的子菜单项
const validChildren = computed(() => {
  if (!menuData.value.children || !menuData.value.children.length) return []
  return menuData.value.children.filter((v: any) =>
    v.status === 1 || (v.meta && v.meta.isNav)
  )
})

// 是否有更多子项（用于层级限制提示）
const hasMoreChildren = computed(() => {
  return validChildren.value.length > 0
})

// 隐藏的子项数量
const hiddenChildrenCount = computed(() => {
  if (props.level < props.maxLevel) return 0
  return validChildren.value.length
})

// 菜单项样式类
const menuItemClass = computed(() => [
  'menu-item',
  `menu-level-${props.level}`,
  {
    'is-top-level': props.level === 0,
    'has-children': hasChildren.value,
    'is-expanded': isExpanded.value,
    'is-active': isMenuActive.value
  }
])

// 菜单标题样式类
const menuTitleClass = computed(() => [
  'menu-title',
  {
    'is-selected': isMenuActive.value,
    'has-children': hasChildren.value
  }
])

// 菜单标题样式
const menuTitleStyle = computed(() => {
  let left = '11px'
  if (props.level == 2) left = '16px'
  return {
    paddingLeft: left
  }
})

// 子菜单容器样式类
const submenuClass = computed(() => [
  'submenu-container',
  `submenu-level-${props.level + 1}`
])

// 展开图标样式类
const expandIconClass = computed(() => [
  'expand-icon',
  'iconfont',
  isExpanded.value ? 'icon-shangjiantou' : 'icon-arrow-down'
])

// 判断菜单是否激活
const isMenuActive = computed(() => {
  if (menuData.value.type) {
    return route.query.id == menuData.value.id
  } else {
    return route.path === menuData.value.path
  }
})

// 方法定义
// 切换展开/折叠状态
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value
}

// 处理菜单点击
const handleMenuClick = () => {
  const item = menuData.value

  // 如果有子菜单且设置了noJump，只切换展开状态
  if (item.meta && item.meta.noJump) {
    if (hasChildren.value) {
      toggleExpand()
    }
    return
  }

  // 执行路由跳转逻辑
  navigateToMenu(item)
}

// 处理子菜单点击事件
const handleChildMenuClick = (menuItem: any) => {
  emit('menu-click', menuItem)
}

// 路由导航逻辑
const navigateToMenu = (item: any) => {
  // 是否外链
  const isExternal = item.redirect && item.redirect.includes('http')
  if (isExternal) {
    window.open(item.redirect || item.url, '_blank')
    return
  }

  // 是否需要确认提示
  if (item.meta && item.meta.tips) {
    ElMessageBox.confirm(
      '直接编辑网站页面将有可能导致页面错乱等风险，建议您联系我们专业的设计师进行页面修改！',
      '是否确认继续编辑网站页面？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      router.push(item.path)
    }).catch(() => {
      return
    })
    return
  }

  // 普通路由跳转
  if (item.path) {
    router.push(item.path)
  } else if (item.type) {
    router.push({
      path: '/page',
      query: {
        jwp_id: item.jwp_id,
        id: item.id,
        title: item.title,
        type: item.type
      }
    })
  }

  // 触发事件
  emit('menu-click', item)
}

const isIframe = computed(() => {
  return true
  // return window.self !== window.top
})
</script>

<style lang='scss' scoped>
// 菜单项基础样式
.menu-item {
  position: relative;
  margin-bottom: 2px;

  // 顶级菜单样式
  &.is-top-level {
    margin-bottom: 8px;

    .menu-title {
      height: 36px;
      line-height: 36px;
      font-weight: bold;
      font-size: 15px;
      color: #111111;
      background: transparent;
      border-radius: 2px;
      transition: all 0.3s ease;

      &:hover {
        background: transparent;
      }

      &.is-selected {
        background: #F1F6FD;
        color: var(--jzt-color-main);
      }
    }

    // 顶级菜单分隔线
    &::after {
      content: '';
      position: absolute;
      bottom: -4px;
      left: 11px;
      right: 11px;
      height: 1px;
      background: #e8ebee;
    }

    &:last-child::after {
      display: none;
    }
  }

  // 一级子菜单样式
  &.menu-level-1 {
    .menu-title {
      height: 34px;
      line-height: 34px;
      font-weight: 500;
      font-size: 14px;
      color: #6D717A;
      border-radius: 2px;

      &:hover {
        background: transparent;
      }

      &.is-selected {
        background: #F1F6FD;
        color: var(--jzt-color-main);
      }
    }
  }

  // 二级子菜单样式
  &.menu-level-2 {
    .menu-title {
      height: 34px;
      line-height: 34px;
      font-weight: 400;
      font-size: 14px;
      color: #797d86;
      border-radius: 2px;

      &:hover {
        background: transparent;
      }

      &.is-selected {
        background: #F1F6FD;
        color: var(--jzt-color-main);
      }
    }
  }

  // 三级及以上子菜单样式
  &.menu-level-3,
  &.menu-level-4,
  &.menu-level-5,
  &.menu-level-6,
  &.menu-level-7,
  &.menu-level-8,
  &.menu-level-9,
  &.menu-level-10 {
    .menu-title {
      height: 32px;
      line-height: 32px;
      font-weight: 400;
      font-size: 12px;
      color: #888888;
      border-radius: 4px;

      &:hover {
        background: #f0f0f0;
      }

      &.is-selected {
        background: #e6f3ff;
        color: #1890ff;
      }
    }
  }
}

// 菜单标题样式
.menu-title {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding-right: 12px;
  transition: all 0.2s ease;
  user-select: none;

  // 层级指示器
  // .level-indicator {
  //   width: 2px;
  //   height: 16px;
  //   margin-right: 8px;
  //   border-radius: 1px;

  //   &.level-1 { background: #52c41a; }
  //   &.level-2 { background: #1890ff; }
  //   &.level-3 { background: #722ed1; }
  //   &.level-4 { background: #eb2f96; }
  //   &.level-5 { background: #fa8c16; }
  //   &.level-6 { background: #13c2c2; }
  //   &.level-7 { background: #f5222d; }
  //   &.level-8 { background: #a0d911; }
  //   &.level-9 { background: #fadb14; }
  //   &.level-10 { background: #2f54eb; }
  // }

  // 菜单图标
  .menu-icon {
    margin-right: 8px;
    font-size: 16px;
    color: inherit;
  }

  // 菜单文本
  .menu-text {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  // 展开图标
  .expand-icon {
    margin-left: 8px;
    font-size: 12px;
    color: #999999;
    transition: transform 0.2s ease;

    &.icon-shangjiantou {
      transform: rotate(0deg);
    }

    &.icon-arrow-down {
      transform: rotate(0deg);
    }
  }
}

// 子菜单容器
.submenu-container {
  overflow: hidden;

  // &.submenu-level-1 {
  //   padding-left: 8px;
  //   border-left: 2px solid #f0f0f0;
  //   margin-left: 16px;
  // }

  // &.submenu-level-2 {
  //   padding-left: 8px;
  //   border-left: 1px solid #f5f5f5;
  //   margin-left: 20px;
  // }

  // &.submenu-level-3,
  // &.submenu-level-4,
  // &.submenu-level-5,
  // &.submenu-level-6,
  // &.submenu-level-7,
  // &.submenu-level-8,
  // &.submenu-level-9,
  // &.submenu-level-10,
  // &.submenu-level-11 {
  //   padding-left: 8px;
  //   border-left: 1px dotted #e8e8e8;
  //   margin-left: 20px;
  // }
}

// 层级限制提示样式
.level-limit-tip {
  display: flex;
  align-items: center;
  height: 28px;
  line-height: 28px;
  font-size: 11px;
  color: #999999;
  background: #f8f9fa;
  border-radius: 4px;
  margin: 4px 0;
  padding-right: 12px;
  border-left: 2px solid #ffa940;

  .iconfont {
    margin-right: 6px;
    font-size: 12px;
    color: #ffa940;
  }

  span {
    font-style: italic;
  }

  &:hover {
    background: #f0f0f0;
    color: #666666;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .menu-title {
    padding-right: 8px;

    .menu-text {
      font-size: 14px;
    }
  }

  .submenu-container {
    margin-left: 12px;
    padding-left: 6px;
  }

  .level-limit-tip {
    font-size: 10px;
    height: 24px;
    line-height: 24px;
    margin: 2px 0;
  }
}
</style>
