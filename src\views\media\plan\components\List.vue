<template>
  <div class="plan-list-app">
    <!-- 近期重要营销日期推荐 -->
    <div class="content-box market-container">
      <p class="new-title">近期重要营销日期推荐</p>
      <div class="market-list flex-wrap">
        <template v-for="(item, index) in list">
          <div class="market-box" :style="{backgroundImage: 'url(' + item.bgimg + ')'}" @click="toAI(item)">
            <div class="market-main flex">
              <div class="time-info">
                <p class="title one-line" :title="item.title">{{item.title}}</p>
                <p class="date">{{item.date}} {{item.Weekday}}</p>
              </div>
              <div class="day-box flex">
                <span class="number">{{item.days}}</span>
                <span class="text">天后</span>
              </div>
            </div>
          </div>
        </template>
        <!-- 添加 -->
        <div class="market-box self-item" @click="toAdd">
          <div class="icon-box">
            <img src="@/assets/images/media/icon03.png" alt="">
          </div>
          <div class="tips">自定义企业营销日期</div>
        </div>
      </div>
    </div>
    <!-- 日历部分 -->
    <div class="content-box calendar-container">
      <!-- 日历 -->
      <jzt-calendar ref="calendarRef" :data="calendarData" @changeDayInfo="getData" @toAI="toAI">
        <!-- 数据类型 -->
        <!-- 插槽 -->
        <template #calendar-top>
          <div class="data-type-box flex">
            <template v-for="(item, index) in calendarDataType">
              <div class="data-type-item flex">
                <span class="c-box" :style="{ backgroundColor: item.color }"></span>
                <span class="text">{{item.title}}</span>
              </div>
            </template>
          </div>
        </template>
      </jzt-calendar>
      <!---->
    </div>
    <!-- 添加弹窗 -->
    <add-list-view ref="addListRef" @refresh="getRecommendList"></add-list-view>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, defineAsyncComponent } from 'vue'
import { getListApi, getFestivalTypeApi, getFestivalListApi } from "@/api/media/plan"

const AddListView = defineAsyncComponent(() => import('@/views/media/plan/components/AddList.vue'))
const JztCalendar = defineAsyncComponent(() => import('@/components/JztCalendar/PlanCalendar.vue'))

const emits = defineEmits(['changeNav'])

const calendarRef = ref()
const addListRef = ref()

const list:any = ref([])
// 获取推荐列表
const getRecommendList = async () => {
  const res = await getListApi()
  list.value = res
}
// 添加营销日期
const toAdd = () => {
  addListRef.value.openDialog()
}

// 节日类型
const calendarDataType:any = ref([])
// 获取节日类型
const getFestivalType = async () => {
  const res:any = await getFestivalTypeApi()
  calendarDataType.value = res
}

// 获取节日数据
const calendarData = ref([])
const queryHoliday = ref({
  startTime: '',
  endTime: '',
})
// 获取节日数据
const getData = (list:any) => {
  return new Promise(async (resolve, reject) => {
    let startTime = calendarRef.value.handleDate(list[0], 'yyyy-M-d')
    let endTime = calendarRef.value.handleDate(list[list.length - 1], 'yyyy-M-d')
    queryHoliday.value.startTime = startTime
    queryHoliday.value.endTime = endTime
    const res:any = await getFestivalListApi(queryHoliday.value)
    calendarData.value = res
    resolve(res)
  })
}

// 跳转AI
const toAI = (item:any) => {
  emits('changeNav', {
    key: 'ai', val: item
  })
}

onMounted(() => {
  getRecommendList()
  getFestivalType()
})

</script>

<style lang="scss" scoped>
.plan-list-app {
  padding: 20px;
  box-sizing: border-box;
  padding-bottom: 0;
  height: calc(100% - 37px);
  overflow-y: auto;
  .content-box {
    background: rgba(255, 255, 255, 0.99);
    box-shadow: 0px 6px 12px 0px rgb(216 224 234 / 20%);
    padding: 20px 30px;
    width: 100%;
    box-sizing: border-box;
    margin-bottom: 20px;
  }
  .new-title {
    font-size: 16px;
    font-weight: bold;
    color: #1c2b4b;
  }
  // 文字超出一行..
  .one-line {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .two-line {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
  }
  .market-list {
    .market-box {
      width: 236px;
      height: 130px;
      overflow: hidden;
      background-size: cover;
      margin-top: 12px;
      margin-right: 12px;
      min-width: 240px;
      border-radius: 10px;
      cursor: pointer;
      .market-main {
        background-color: rgba(0, 0, 0, 0.5);
        padding: 0 20px;
        height: 100%;
        box-sizing: border-box;
        justify-content: space-between;
        .time-info {
          width: 60%;
          .title {
            font-size: 20px;
            color: #ffffff;
            position: relative;
            padding-left: 10px;
            &::before {
              content: "";
              position: absolute;
              left: 0;
              top: 50%;
              transform: translateY(-50%);
              width: 4px;
              height: 16px;
              background: #ffffff;
            }
          }
          .date {
            font-size: 14px;
            color: #ffffff;
          }
        }
        .day-box {
          min-width: 66px;
          height: 60px;
          background: rgba(248, 250, 255, 0.9);
          border-radius: 10px;
          border: 1px solid #2859ff;
          justify-content: center;
          flex-shrink: 0;
          margin-left: 5px;
          // padding: 0 10px;
          .number {
            font-weight: 800;
            font-size: 30px;
            color: #2859ff;
          }
          .text {
            width: 12px;
            font-weight: 500;
            font-size: 12px;
            color: #2859ff;
            line-height: 14px;
            margin-left: 6px;
          }
        }
      }
      // &:nth-child(4n) {
      //   margin-right: 0;
      // }
      &.self-item {
        background: #f0f2f6;
        border: 1px solid #e5ecff;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        box-sizing: border-box;
        .tips {
          font-size: 12px;
          color: #999999;
          margin-top: 10px;
        }
      }
    }
  }
  /* 日历相关 */
  .calendar-container {
    // 数据类型
    .data-type-box {
      margin: 50px 0;
      justify-content: center;
      .data-type-item {
        margin: 0 16px;
        .c-box {
          width: 10px;
          height: 10px;
          background: #0059ff;
          border-radius: 2px;
        }
        .text {
          font-size: 14px;
          color: #1c2b4b;
          margin-left: 4px;
        }
      }
    }
    // .calendar-box {
    //   width: 1100px;
    //   margin: auto;
    //   border: 1px solid #f3f3f3;
    //   border-right-width: 0;
    //   .week-box {
    //     background: #f3f3f3;
    //     height: 50px;
    //     .week-item {
    //       width: calc(100% / 7);
    //       justify-content: center;
    //       flex-direction: column;
    //       position: relative;
    //       .title {
    //         font-size: 16px;
    //         color: #3f423f;
    //       }
    //       .date {
    //         font-size: 12px;
    //         color: #999;
    //       }
    //       .day-tag {
    //         width: 16px;
    //         height: 16px;
    //         line-height: 16px;
    //         font-size: 12px;
    //         color: #ffffff;
    //         text-align: center;
    //         position: absolute;
    //         right: 40px;
    //         top: 3px;
    //         &.holiday {
    //           background: #ff9494;
    //         }
    //         &.work {
    //           background: #8e8e8e;
    //         }
    //       }
    //       &.active {
    //         .title {
    //           color: #2859ff;
    //         }
    //       }
    //     }
    //   }
    //   .day-box {
    //     min-height: 775px;
    //     .day-item {
    //       width: calc(100% / 7);
    //       height: 155px;
    //       box-sizing: border-box;
    //       border: 1px solid #f3f3f3;
    //       border-left-width: 0;
    //       border-bottom-width: 0;
    //       position: relative;
    //       .number-box {
    //         justify-content: space-between;
    //         height: 34px;
    //         padding: 0 10px;
    //         .day-tag {
    //           width: 16px;
    //           height: 16px;
    //           line-height: 16px;
    //           font-size: 12px;
    //           color: #ffffff;
    //           text-align: center;
    //           &.holiday {
    //             background: #ff9494;
    //           }
    //           &.work {
    //             background: #8e8e8e;
    //           }
    //         }
    //         .num {
    //           text-align: right;
    //           span {
    //             font-size: 13px;
    //             color: #515151;
    //             width: 24px;
    //             height: 24px;
    //             border-radius: 50%;
    //             display: inline-flex;
    //             align-items: center;
    //             justify-content: center;
    //           }
    //           &.before,
    //           &.after {
    //             span {
    //               color: #c3c3c3;
    //             }
    //           }
    //           &.today {
    //             span {
    //               background: #0059ff;
    //               color: #ffffff;
    //             }
    //           }
    //         }
    //       }
    //       .data-list-box {
    //         .data-item {
    //           // min-height: 28px;
    //           cursor: pointer;
    //           margin-bottom: 4px;
    //           padding: 5px 0;
    //           position: relative;
    //           .t-line {
    //             display: inline-block;
    //             width: 2px;
    //             height: 100%;
    //             position: absolute;
    //             top: 0;
    //             left: 0;
    //           }
    //           .data-label {
    //             display: inline-block;
    //             font-size: 12px;
    //             margin: 0 10px;
    //             line-height: 1.5;
    //           }
    //           &.item-more {
    //             justify-content: center;
    //             margin-bottom: 0;
    //             color: #d0d0d0;
    //             height: 20px;
    //             margin-left: 0;
    //             position: inherit;
    //             padding: 0;
    //             .data-list-more-box {
    //               position: absolute;
    //               top: -14px;
    //               left: -13px;
    //               width: calc(100% + 26px);
    //               padding: 10px;
    //               box-shadow: 2px 2px 12px 0px rgba(58, 59, 62, 0.1);
    //               border-radius: 4px;
    //               border: 1px solid rgba(222, 226, 235, 1);
    //               background: #fff;
    //               display: none;
    //               z-index: 4;
    //             }
    //             &:hover {
    //               .data-list-more-box {
    //                 display: block;
    //               }
    //             }
    //           }
    //         }
    //       }
    //     }
    //   }
    // }
  }
}
</style>