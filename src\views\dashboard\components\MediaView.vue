<template>
  <!--媒体管理-->
  <div class="content-box media-container">
    <p class="new-title">{{ title }}</p>
    <div class="item-box flex-wrap" style="position: relative">
      <template v-for="(item, index) in mediaList">
        <div class="item flex" :style="{width: `calc(100% / ${props.column})`}">
          <img :src="require('@/assets/images/index/mt0' + (index + 1) + '.png')" alt="" />
          <span>{{ item.name }}</span>
          <div class="item-hover">
            <div class="h-item active" @click="mediaClick(2, item)">
              帐号管理
            </div>
            <div class="h-item" @click="mediaClick(3, item)">官网地址</div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted } from 'vue'
import useDashboard from '@/views/dashboard/hooks/useDashboard'

const { mediaList, mediaClick } = useDashboard()

const props = defineProps({
  title: {
    type: String,
    default: '企宣工具'
  },
  // 布局方式
  column: {
    type: Number,
    default: 1,
  }
})

</script>

<style lang="scss" scoped>
.media-container {
  .item-box {
    margin-top: 10px;
    .item {
      font-size: 14px;
      font-weight: 500;
      color: #696c7d;
      /* margin-top: 20px; */
      position: relative;
      width: 100%;
      margin-bottom: 10px;
      padding: 8px 0;
      overflow: hidden;
      cursor: pointer;
      .item-hover {
        position: absolute;
        width: 98%;
        height: 100%;
        left: 0;
        top: 0;
        /* 渐变背景 */
        background: linear-gradient(
          90deg,
          rgba(246, 248, 251, 0) 0%,
          rgba(246, 248, 251, 1) 70%
        );
        transition: all ease-in-out 0.3s;
        transform: translateX(0%);
        padding-right: 8px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-end;
        /* flex间隔 */
        /* gap: 6px; */
        border-radius: 10px;
        .h-item {
          width: 64px;
          height: 23px;
          border-radius: 12px;
          border: 1px solid #2859ff;
          font-weight: bold;
          font-size: 12px;
          color: #2859ff;
          display: flex;
          justify-content: center;
          align-items: center;
          &.active {
            background-color: #2859ff;
            color: #ffffff;
            margin-bottom: 6px;
          }
        }
      }
      &:hover {
        .item-hover {
          transform: translateX(0%);
        }
      }
      img {
        width: 48px;
        height: 48px;
        margin-right: 5px;
      }
    }
  }
}
</style>