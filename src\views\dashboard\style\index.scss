.hidden {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

p {
  margin: 0;
}
.new-title {
  font-size: 16px;
  font-weight: bold;
  color: #1c2b4b;
}
.content-box {
  background: rgba(255, 255, 255, 0.99);
  box-shadow: 0px 6px 12px 0px rgba(216, 224, 234, 0.2);
  padding: 20px;
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 20px;
  .swiper {
    flex: 1;
    height: calc(30px * 3);
    margin-top: 10px;
    .swiper-slide {
      a {
        // display: block;
        overflow: hidden;
        // white-space: nowrap;
        text-overflow: ellipsis;
        height: 30px;
        line-height: 30px;
        font-size: 14px !important;
        color: var(--jzt-color-main) !important;
        display: -webkit-box;
        line-clamp: 1;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }
    }
  }
  &.col-01 {
    width: 26%;
  }
  &.col-02 {
    width: 54%;
    margin: 0 20px;
  }
  &.col-03 {
    width: calc(20% - 40px);
  }
  &.col-04 {
    width: calc(74% - 20px);
    margin-left: 20px;
  }
  &.row-01 {
    height: 200px;
  }
  &.row-02 {
    height: 370px;
  }
}
.part01 {
  .item {
    margin-bottom: 16px;
    .key {
      font-size: 14px;
      font-weight: 400;
      color: #696c7d;
    }
    .value {
      font-size: 18px;
      font-weight: bold;
      color: #0e1626;
    }
  }
}
/*媒体管理*/
.part02 {
  .item-box {
    margin-top: 10px;
    .item {
      font-size: 14px;
      color: #696c7d;
      /*width: 148px;*/
      width: calc(100% / 4);
      // margin-top: 20px;
      position: relative;
      padding: 8px 0;
      overflow: hidden;
      cursor: pointer;
      img {
        width: 48px;
        height: 48px;
        margin-right: 5px;
        display: inline-block;
      }
      .item-hover {
        position: absolute;
        width: 98%;
        height: 100%;
        left: 0;
        top: 0;
        /* 渐变背景 */
        background: linear-gradient(
          90deg,
          rgba(246, 248, 251, 0) 0%,
          rgba(246, 248, 251, 1) 60%
        );
        transition: all ease-in-out 0.3s;
        transform: translateX(100%);
        padding-right: 8px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-end;
        /* flex间隔 */
        // gap: 6px;
        border-radius: 10px;
        .h-item {
          width: 64px;
          height: 23px;
          border-radius: 12px;
          border: 1px solid #2859ff;
          font-weight: bold;
          font-size: 12px;
          color: #2859ff;
          display: flex;
          justify-content: center;
          align-items: center;
          &.active {
            background-color: #2859ff;
            color: #ffffff;
            margin-bottom: 6px;
          }
        }
      }
      &:hover {
        .item-hover {
          transform: translateX(0);
        }
      }
    }
  }
}
/*最新使用记录*/
.part03 {
  .item-box {
    justify-content: space-between;
    .item {
      width: calc(100% / 2 - 5px);
      height: 34px;
      line-height: 34px;
      text-align: center;
      background: #ffffff;
      border: 1px solid #e3e5ec;
      border-radius: 2px;
      font-size: 12px;
      /*font-family: PingFang SC;*/
      font-weight: 400;
      color: #696c7d;
      margin-top: 10px;
    }
  }
}

/* 网站空间统计 */
.part06 {
  margin-bottom: 20px;
  justify-content: space-around;
  .right-text {
    display: flex;
    flex-direction: column;
    line-height: 1.6;
    padding-right: 20px;
    .t-title,
    .t-num {
      font-size: 16px;
      font-weight: bold;
      color: #1c2b4b;
    }
    .t-title {
      position: relative;
    }
    .t-p {
      font-size: 14px;
      font-weight: 500;
      color: #696c7d;
    }
  }
  .text-item + .text-item {
    margin-top: 16px;
  }
  .text-item {
    .t-title {
      &::before {
        content: "";
        display: block;
        width: 10px;
        height: 10px;
        border-radius: 5px;
        position: absolute;
        left: -17px;
        top: 0;
        bottom: 0;
        margin: auto;
      }
    }
    &:first-child {
      .t-title {
        &::before {
          background: #3765ff;
        }
      }
    }
    &:last-child {
      .t-title {
        &::before {
          background: #b3a3ff;
        }
      }
    }
  }
}
/* 访问统计 */
.part07 {
  margin-bottom: 20px;
  position: relative;
  .more {
    position: absolute;
    top: 20px;
    right: 30px;
    z-index: 9;
    font-size: 12px;
    /*font-family: PingFang SC;*/
    font-weight: 500;
    color: #7f8294;
  }
}
.flex-yx-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.total-wrap {
  margin-top: 30px;
  margin-bottom: 30px;

  img {
    width: 47px;
    height: 47px;
    margin-bottom: 14px;
  }

  .total {
    font-family: PangMenZhengDao;
    font-size: 40px;
    line-height: 1;
    color: #0E1626;
    margin-bottom: 14px;
  }

  .tips {
    font-size: 14px;
    color: #3C3C3C;
  }
}

/*媒体管理 适配*/
@media (max-width: 1700px) {
  .main-box .part02 .item-box .item {
    width: calc(100% / 4);
  }
}

@media (max-width: 1530px) {
  .main-box .part02 .item-box .item {
    width: calc(100% / 4);
  }
}

/*网站使用报告 适配*/
@media (max-width: 1400px) {
  .main-box .part04 .tabs-box .item {
    width: 90px;
  }
}

/* 各端二维码 适配 */
@media (max-width: 1700px) {
  .main-box .part05 .mut-box {
    max-width: 500px !important;
  }
}
