<script setup lang="ts">
import DataList from "@/components/Global/DataList.vue";
import {computed, ref} from "vue";
import {changeSmsStatusApi, delSmsApi, getSmsListApi} from "@/api/siteManage";
import {ElMessage, ElMessageBox} from "element-plus";
import {useDialog} from "@/hooks/useDialog";
import UpdateSms from "@/views/site/sms/components/UpdateSms.vue";

const tableConfig = ref({
  api: getSmsListApi,
  columns: [
    {
      prop: 'ordering',
      label: '排序',
      width: 80
    },
    {
      prop: 'code',
      label: '模板参数'
    },
    {
      prop: 'type',
      type: 'slot',
      label: '类型'
    },
    {
      prop: 'signature',
      label: '签名'
    },
    {
      prop: 'create_time',
      label: '添加时间',
      sortable: 'custom',
    },
    {
      prop: 'status',
      type: 'slot',
      label: '状态'
    },
    {
      prop: 'action',
      type: 'slot',
      label: '操作',
      align: 'center',
      width: 180
    },
  ],
  multipleSelection: []
})

const addSms = () => {
  openDialog({
    ordering: 0,
    parent_id: 1,
    accessKeyId: '',
    accessKeySecret: ''
  })
}

const delApi = (ids: string) => {
  ElMessageBox.confirm('真的要删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try{
      await delSmsApi({id: ids})
      ElMessage.success('操作成功')
      refresh()
    }catch (e){
      console.log(e);
    }
  }).catch(() => {
    data.open = data.open === 1? 0 : 1
    console.log(e);
  })
}

const delSms = (data: any) => {
  const ids = data.map((v: any) => v.id).join(',')
  delApi(ids)
}

const actionList = ref([{
  name: '添加',
  icon: 'icon-tianjia',
  key: 'add',
  type: 'action',
  checkSelected: false,
  action: addSms
}, {
  name: '删除',
  icon: 'icon-shanchu1',
  key: 'del',
  type: 'action',
  checkSelected: true,
  action: delSms
}])

/**
 * 按钮点击事件
 * @param button
 */
const dataAction = (button: any) => {
  if (button.checkSelected) {
    if (!tableConfig.value.multipleSelection.length) {
      ElMessage.warning('请先选择数据')
      return
    }
  }
  button.action(tableConfig.value.multipleSelection)
}

const dataListRef: any = ref(null)

const changeStatus = (row: any) => {
  ElMessageBox.confirm('真的要改变状态吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try{
      await changeSmsStatusApi({id: row.id})
      ElMessage.success('操作成功')
    }catch (e){
      row.open = row.open === 1 ? 0 : 1
      console.log(e);
    }
  }).catch(() => {
    row.open = row.open === 1 ? 0 : 1
  })
}

const edit = (row: any) => {
  if(!row) return
  const data = {
    ...row,
    parent_id: row.type
  }
  openDialog(data)
}
const del = (row: any) => {
  if(!row.id) return
  delApi(row.id)
}
const { EditDialog, dialogShow, dialogData, openDialog, closeDialog } = useDialog()

const dialogTitle = computed(() => {
  return dialogData && dialogData.id ? '编辑' : '添加'
})

const refresh = () => {
  dataListRef.value && dataListRef.value.refresh()
}
</script>

<template>
  <div class="wrap h100">
    <data-list ref="dataListRef" :tableConfig="tableConfig" :action-default="false" @clickBtn="dataAction"
               :actionList="actionList" v-model:multipleSelection="tableConfig.multipleSelection">
      <template #status="{row}">
        <el-switch v-if="row && Object.keys(row).length > 0" v-model="row.open" :active-value="1" :inactive-value="0" inline-prompt active-text="开启" inactive-text="关闭" @change="() => changeStatus(row)" />
      </template>
      <template #type="{row}">
        <span v-if="+row.type === 1">阿里云</span>
        <span v-else>未知</span>
      </template>
      <template #action="{row}">
        <div class="table-action flex">
          <div class="item" @click="edit(row)">
            <i class="iconfont icon-bianji"></i> <span>编辑</span>
          </div>
          <div class="item" @click="del(row)">
            <i class="iconfont icon-shanchu1"></i> <span>删除</span>
          </div>
        </div>
      </template>
    </data-list>

    <edit-dialog ref="editDialogRef" :dialog-show="dialogShow" :title="`${dialogTitle}短信模板`" :custom-form="true" sure-text="关闭" cancel-text="" @close="closeDialog" @sure="closeDialog" width="40%" :show-bottom-btn="false">
      <update-sms v-model:form="dialogData" @closeDialog="closeDialog" @refresh="refresh" />
    </edit-dialog>
  </div>
</template>

<style scoped lang="scss">

</style>
