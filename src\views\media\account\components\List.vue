<template>
  <data-list ref="dataListRef" :actionDefault="false" :tableConfig="tableConfig" @clickBtn="dataAction" :actionList="actionList"
    v-model:multipleSelection="tableConfig.multipleSelection">
    <template #top-right>
      <el-select class="width200" v-model="tableConfig.params.catid" placeholder="请选择平台" clearable>
        <el-option :value="0" label="全部平台"></el-option>
        <el-option v-for="item in accountTypeList" :key="item.id" :label="item.title" :value="item.title"></el-option>
      </el-select>
    </template>
    <template #type="{ row }">
      <svg class="svg-icon" aria-hidden="true" style="display: inline-block">
        <use :xlink:href="`#icon-${row.icon}`"></use>
      </svg>
      <span>{{ row.type }}</span>
    </template>
    <template #action="{ row }">
      <div class="table-action flex">
        <div class="item" @click="info(row)">
          <i class="iconfont icon-xiangqing"></i> <span>详情</span>
        </div>
        <div class="item" @click="deleteAction(row.id, deleteAccountApi)">
          <i class="iconfont icon-shanchu1"></i> <span>删除</span>
        </div>
      </div>
    </template>
  </data-list>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { useDataList } from '@/hooks/useDataList'
import { accountListApi, deleteAccountApi } from '@/api/media/account'

const { DataList, dataListRef, deleteAction, refresh } = useDataList()

const props = defineProps({
  accountTypeList: {
    type: Array as any,
    default: () => []
  }
})

const emit = defineEmits(['changeNav'])

const tableConfig = ref({
  api: accountListApi,
  showIndex: false,
  multipleSelection: [],
  columns: <any>[
    { label: '账号名称', prop: 'title' },
    { label: '平台', prop: 'type', width: 200, align: 'center', type: 'slot' },
    { label: '状态', prop: 'show_font', width: 160, align: 'center' },
    { label: '添加时间', prop: 'create_time', width: 200, sortable: 'custom' },
    { label: '操作', prop: 'action', width: 200, type: 'slot', align: 'center' }
  ],
  params: { catid: 0 },
})
const actionList = ref([
  { name: '删除', icon: 'icon-shanchu1', key: 'delete', type: 'action' }
])

const dataAction = (data: any) => {
  const { key } = data
  let selectList = tableConfig.value.multipleSelection
  const ids = selectList.map((item: any) => item.id).join(',')
  if (key == 'delete') {
    if (!selectList.length) return ElMessage.warning('请选择要操作的数据')
    deleteAction(ids, deleteAccountApi)
  }
}

const info = (row: any) => {
  emit('changeNav', { key: 'show', val: row })
}

const delAuth = async (id: string) => {
  await deleteAction(id, deleteAccountApi)
  emit('changeNav', { key: 'list' })
}

defineExpose({ delAuth, refresh })
</script>

<style scoped lang="scss">
.svg-icon {
  width: 18px;
  height: 18px;
  margin-right: 5px;
  vertical-align: middle;
  fill: currentColor;
  overflow: hidden;
}
</style>