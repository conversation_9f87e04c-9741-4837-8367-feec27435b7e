<template>
  <div v-loading="postLoading" class="library-wrap h100">
    <div class="library-top flex">
      <div class="top-left flex">
        <div class="flex mr10">
          <label class="label">从素材库：</label>
          <el-select v-model="libraryCateId" placeholder="请选择" filterable suffix-icon="CaretTop"
            class="width200">
            <el-option value="" label="全部素材分类"></el-option>
            <el-option v-for="(item, index) in libraryCate" :key="index" :value="item.id" :label="item.type_title">
              <span v-html="item.title"></span>
            </el-option>
          </el-select>
        </div>
        <div class="flex mr30">
          <el-input v-model="searchName" placeholder="请输入素材名称搜索" @keydown.enter="searchByName" @blur="searchByName">
            <template #suffix>
              <i class="iconfont icon-sousuo" style="cursor: pointer" @click="searchByName"></i>
            </template>
          </el-input>
        </div>
        <div class="flex">
          <label class="label">添加到：</label>
          <type-select class="width200 mr10" v-model:value="categoryId" :apiType="cateApiUrl(props.cateType).type" optionType="content-add"></type-select>
          <jzt-button name="添加分类" icon="icon-tianjia" @click="addTypeBtn"></jzt-button>
        </div>
      </div>
      <div class="top-right flex">
        <jzt-button name="取消" size="large" @click="cancel" style="margin-right: 10px;"></jzt-button>
        <jzt-button name="添加" size="large" class="active" @click="addGoods"></jzt-button>
      </div>
    </div>
    <div class="content-box">
      <library :isShowCate="false" :isRadio="false" :cate-id="libraryCateId" :showPage="true" :limit="24" :max="-1"
        v-model:library-img="libraryImg" ref="libraryRef" @cateListChange="libraryCateListChange" :search-name="searchName" class="h100">
      </library>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, defineAsyncComponent } from 'vue'
import { ElMessage } from 'element-plus'
import { cateApiUrl } from '@/utils/contentType';
import { contentAddMaterialApi } from '@/api/content'

const TypeSelect = defineAsyncComponent(() => import('./components/TypeSelect.vue'))
const Library = defineAsyncComponent(() => import('@/components/Uploads/Library.vue'))
const libraryRef = ref()

const props = defineProps({
  cateType: {
    type: String,
    default: 'content'
  }
})

const emit = defineEmits(['actionClick', 'success'])

const libraryCateId = ref('')
const libraryImg = ref([])
const libraryCate:any = ref([])
const searchName = ref('')
const categoryId = ref('')

const libraryCateListChange = (val: any) => {
  libraryCate.value = val
}

const searchByName = () => {
  libraryRef.value.getLibraryList(1);
}

// 添加
const postLoading = ref(false)
const addGoods = () => {
  console.log(libraryImg.value, 'libraryImg.value')
  console.log(categoryId.value, 'categoryId.value')
  if (!libraryImg.value.length) {
    ElMessage.error('请选择素材')
    return
  }
  if (!categoryId.value) {
    ElMessage.error('请选择添加到的分类')
    return
  }
  postLoading.value = true
  contentAddMaterialApi(cateApiUrl(props.cateType).list, {
    catid: categoryId.value,
    id: libraryImg.value.map((item: any) => item.id).join(',')
  }).then((res: any) => {
    ElMessage.success('添加成功')
    emit('success')
    emit('actionClick', 'list')
  }).finally(() => {
    postLoading.value = false
  })
}

// 取消
const cancel = () => {
  emit('actionClick', 'list')
}

// 添加分类
const addTypeBtn = () => {
  // emit('actionClick', 'addType')
}

</script>

<style lang="scss" scoped>
.library-top {
  justify-content: space-between;
  padding: 10px 22px;
  font-size: 14px;
  color: #555;
}
.content-box {
  background: rgba(255, 255, 255, 0.99);
  box-shadow: 0px 6px 12px 0px rgba(216, 224, 234, 0.2);
  padding: 0;
  width: 100%;
  box-sizing: border-box;
  height: calc(100% - 54px);
  :deep(.library-list-box) {
    height: calc(100% - 62px - 36px);
  }
}
</style>
