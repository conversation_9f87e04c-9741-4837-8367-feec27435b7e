<template>
  <div class="info-add h100 flex-y">
    <div class="content-box h100 flex-y mb20">
      <div class="flex-1 flex-x">
        <div class="info-left mr30">
          <div v-if="artList.length > 1" class="btn-box flex-between mb10">
            <div class="left-btn flex">
              <jzt-button class="mr6 height24" name="上移" size="small" icon="icon-tabshouqi" @click="upArt"></jzt-button>
              <jzt-button class="height24" name="下移" size="small" icon="icon-tabxiala" @click="downArt"></jzt-button>
            </div>
            <jzt-button class="height24" name="删除" size="small" icon="icon-shanchu1" @click="delArt"></jzt-button>
          </div>
          <div class="art-list">
            <template v-for="(item, index) in artList">
              <div :class="['item', { 'first': index == 0 }, { 'active': nowIndex == index }]" @click="changeNowData(index)">
                <template v-if="index == 0">
                  <div class="img-wrap">
                    <upload-image height="140px" :api="uploadCoverApi" v-model:imageIntro="item.local_url" @imgOk="(e:any) => { item.local_url = e.src, item.cover_img_curl = e.srcs }" />
                    <p class="w-title">{{ item.title || '未命名标题' }}</p>
                  </div>
                </template>
                <template v-else>
                  <p :class="['w-title', { 'node-data': !item.title }]">{{ item.title || '未命名标题' }}</p>
                  <div class="img-wrap">
                    <upload-image height="44px" :api="uploadCoverApi" v-model:imageIntro="item.local_url" @imgOk="(e:any) => { item.local_url = e.src, item.cover_img_curl = e.srcs }" />
                  </div>
                </template>
              </div>
            </template>
          </div>
          <div class="wx-add" v-if="artList.length < 8">
            <p class="wx-add-btn" @click="addList"><i class="iconfont icon-tianjia"></i></p>
          </div>
        </div>
        <div class="info-right flex-1">
          <el-form >
            <el-form-item label="标题" prop="title">
              <el-input v-model="artList[nowIndex].title" placeholder="请输入文章标题"></el-input>
            </el-form-item>
            <el-form-item label="作者" prop="author">
              <el-input v-model="artList[nowIndex].author" placeholder="请输入文章作者"></el-input>
            </el-form-item>
            <el-form-item label="摘要" prop="digest">
              <!-- 多行文本 -->
              <el-input type="textarea" :rows="3" v-model="artList[nowIndex].digest" placeholder="请输入文章摘要" resize="none"></el-input>
            </el-form-item>
            <el-form-item label="详情" prop="content">
              <tinymce-editor v-model:content="artList[nowIndex].content" :imgArr="[]" />
            </el-form-item>
          </el-form>
        </div>
      </div>
      <!-- 底部按钮-->
      <div class="bot-btn-box flex-between">
        <div></div>
        <div class="btn-box flex">
          <jzt-button name="取消" @click="cancel"></jzt-button>
          <jzt-button class="active ml10" name="保存" @click="save(1)"></jzt-button>
          <jzt-button class="active ml10" name="保存并推送" @click="save(2)"></jzt-button>
        </div>
      </div>
    </div>
    <!-- 文章列表 同步百家号 选择账号 -->
    <media-sync-account ref="mediaSyncAccountRef" @sure="toAsync" />
  </div>
</template>

<script setup lang="ts">
import { ref, defineAsyncComponent } from 'vue'
import { ElMessage, ElLoading } from 'element-plus';
import { useContent } from '../hooks';
import { saveWeChatApi, publishWeChatApi, uploadCoverApi } from '@/api/media'
import { nextTick } from 'vue';

const UploadImage = defineAsyncComponent(() => import('@/components/Uploads/uploadImg.vue'))
const TinymceEditor = defineAsyncComponent(() => import('@/components/Global/TinymceEditor.vue'))
const MediaSyncAccount = defineAsyncComponent(() => import('./components/MediaSyncAccount.vue'))

const mediaSyncAccountRef = ref()

const { configList, getConfigList } = useContent()

const emit = defineEmits(['actionClick', 'success', 'seletAccount'])

const defaultItem = {
  local_url: '',
  cover_img_curl: '',
  title: '',
  digest: '',
  author: '',
  content: ''
}

const contentId:any = ref(null)  // 内容id

const artList:any = ref(JSON.parse(JSON.stringify([defaultItem])))
const nowIndex = ref(0)

const upArt = () => {
  if (nowIndex.value == 0) return
  artList.value.splice(nowIndex.value - 1, 0, artList.value.splice(nowIndex.value, 1)[0])
  nowIndex.value--
}

const downArt = () => {
  if (nowIndex.value == artList.value.length - 1) return
  artList.value.splice(nowIndex.value + 1, 0, artList.value.splice(nowIndex.value, 1)[0])
  nowIndex.value++
}

const delArt = () => {
  if (artList.value.length == 1) return ElMessage.error('至少保留一个图文')
  artList.value.splice(nowIndex.value, 1)
  if (nowIndex.value == artList.value.length) nowIndex.value--
}

const addList = () => {
  artList.value.push(JSON.parse(JSON.stringify(defaultItem)))
}

const changeNowData = (index: number) => {
  nowIndex.value = index
}

const cancel = () => {
  emit('actionClick', 'list')
}

const save = (type: number) => {
  console.log(type)
  for (let i = 0; i < artList.value.length; i++) {
    let item = artList.value[i]
    if (!item.local_url) {
      return ElMessage.error(`请上传第${i + 1}条文章封面`)
    } else if (!item.title) {
      return ElMessage.error(`请输入第${i + 1}条文章标题`)
    } else if(!item.author) {
      return ElMessage.error(`请输入第${i + 1}条文章作者`)
    } else if (!item.digest) {
      return ElMessage.error(`请输入第${i + 1}条文章摘要`)
    } else if (!item.content) {
      return ElMessage.error(`请输入第${i + 1}条文章内容`)
    }
  }
  let params = { article_list: JSON.stringify(artList.value), content_id: contentId.value }
  let loading = ElLoading.service({
    lock: true,
    text: '保存中...',
    background: 'rgba(0, 0, 0, 0.7)'
  })
  saveWeChatApi(params).then((res:any) => {
    ElMessage.success('保存成功')
    loading.close()
    if (type == 1) {
      emit('actionClick', 'list')
      emit('success')
    }
    if (type == 2) {
      getConfigList(1).then(() => {
        if (!configList.value.length) return ElMessage.error('请先添加公众号配置信息')
        else if (configList.value.length > 1) {
          mediaSyncAccountRef.value && mediaSyncAccountRef.value.openDialog({
            list: configList.value,
            id: res.news_id
          })
        } else {
          publish(configList.value[0].id, res.news_id)
        }
      })
    }
  }).catch(err => {
    loading.close()
  })
}

// 选择账号 确定
const toAsync = (row: any) => {
  console.log(row)
  const { account, id } = row
  publish(account, id)
}

// 发布
const publish = async (account: number, id: number) => {
  let loading = ElLoading.service({
    lock: true,
    text: '发布中...',
    background: 'rgba(0, 0, 0, 0.7)'
  })
  publishWeChatApi({ id, config_id: account }).then((res:any) => {
    ElMessage.success('发布成功')
    emit('actionClick', 'list')
    emit('success')
  }).finally(() => {
    loading.close()
  })
}

const setData = (data: any) => {
  if (!data) return; // 如果数据为空，则直接返回
  let { id, image_intro, title, introtext, fulltext } = data
  contentId.value = id
  artList.value = [
    { local_url: image_intro, title, digest: introtext, content: fulltext, author: '', cover_img_curl: '' }
  ]
  nowIndex.value = 0
}

defineExpose({ setData, publish })
</script>

<style lang="scss" scoped>
.info-add {
  .info-left {
    width: 330px;
  }
  .info-right {
    overflow-y: auto;
  }
}
.art-list {
  .item {
    border: 1px solid #E3E5EC;
    border-radius: 2px;
    padding: 6px 6px 6px 20px;
    width: 100%;
    height: 56px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    cursor: pointer;
    .w-title {
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 500;
      color: #1C2B4B;
      width: calc(100% - 50px);
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      &.none-data {
        color: #7F8294;
      }
    }
    .img-wrap {
      width: 44px;
      height: 44px;
      position: relative;
      overflow: hidden;
      background-color: #f0f2f6;
      :deep(.img-box) {
        .cover-img {
          // object-fit: cover;
          .upload{
            border: none;
          }
        }
      }
    }
    &.active, &:hover {
      border-color: #A0B6FF;
    }
  }
  .first {
    width: 100%;
    height: 140px;
    padding: 0;
    .img-wrap {
      width: 100%;
      height: 100%;
      position: relative;
      overflow: hidden;
      background-color: transparent;
      .w-title {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        box-sizing: border-box;
        height: 42px;
        line-height: 42px;
        padding: 0 20px;
        background-color: rgba(0, 0, 0, 0);
        z-index: 10;
      }
    }
  }
}
.wx-add {
  display: flex;
  align-items: center;
  justify-content: center;
  .wx-add-btn {
    width: 34px;
    height: 34px;
    line-height: 34px;
    text-align: center;
    background: #E5EFFF;
    border-radius: 50%;
    cursor: pointer;
    .iconfont {
      font-size: 12px;
      color: #2859FF;
    }
  }
}

</style>
