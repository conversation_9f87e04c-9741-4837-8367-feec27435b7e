import http from '@/utils/request'

const { VUE_APP_PROXY_DOMAIN_REAL2 } = process.env

// 素材库分类
export const typeListAll = () => {
  return http.request({
    url: 'Bucket/batch_list',
    method: 'get'
  })
}

// 获取素材库
export const getChildFolder = (data: any) => {
  return http.request({
    url: 'Goodsnew/create_all',
    method: 'POST',
    data
  })
}

// 获取素材库空间分布
export const getBucketSpaceApi = () => {
  return http.request({
    url: 'Space/Bucketfb',
    method: 'POST'
  })
}

// 获取空间占比
export const getSpacePercentApi = () => {
  return http.request({
    url: 'Space/Proportion',
    method: 'POST'
  })
}


// 素材库 文件列表 BucketNew/index_list
export const getBucketListApi = (data: any) => {
  return http.request({
    // url: 'BucketNew/index_list',
    url: 'BucketNew/index_lists',
    method: 'POST',
    data
  })
}

// 素材库 新建分类
export const addBucketCateApi = (data: any) => {
  return http.request({
    url: 'BucketNew/add_folder',
    method: 'POST',
    data
  })
}

// 素材库 删除
export const deleteBucketApi = (data: any) => {
  return http.request({
    url: 'BucketNew/del_all',
    method: 'POST',
    data
  })
}

// 素材库 上线下线
export const editStatusApi = (data: any) => {
  return http.request({
    url: 'NewBucket/editStatus',
    method: 'POST',
    data
  })
}

// 素材库 修改标题
export const editBucketTitleApi = (data: any, isFile: number) => {
  let url = isFile == 1 ? 'BucketNew/update_type_title' : 'NewBucket/updateTitle'
  return http.request({
    url,
    method: 'POST',
    data
  })
}

// 素材库 智能获取 PickUp/getlist
export const getPickUpListApi = (data: any) => {
  return http.request({
    url: 'PickUp/getlist',
    method: 'POST',
    data
  })
}

// 素材库 智能获取 上传图片
export const uploadImgByAiApi = (data: any) => {
  return http.request({
    url: '/PickUp/upfile',
    method: 'post',
    data
  })
}

// 素材库 全部分类
export const getAllCateApi = () => {
  return http.request({
    url: 'BucketNew/all_type',
    method: 'GET'
  })
}

// 素材库 转移
export const transferBucketApi = (data: any) => {
  return http.request({
    url: 'BucketNew/transfer',
    method: 'POST',
    data
  })
}

// 素材库 编辑分类名称
export const editCateNameApi = (data: any) => {
  return http.request({
    url: 'BucketNew/update_type_title',
    method: 'POST',
    data
  })
}

// 素材库 下载
export const downloadBucketApi = (data: any) => {
  let domain = window.location.origin
  window.open(`${domain}/newclient/bucket/downImg/ids/${data.join(',')}`, '_blank')
}

// 素材库 检查空间大小
export const checkSpaceApi = (data: any) => {
  return http.request({
    url: 'BucketNew/checkCountnum',
    method: 'POST',
    data
  })
}

// 素材库 上传图片
export const uploadImgApi = (data: any) => {
  return http.request({
    url: 'bucket/picurl_create',
    method: 'POST',
    data
  })
}

// 素材库 上传视频
export const uploadVideoApi = (data: any) => {
  return http.request({
    url: 'NewBucket/uploadVideo',
    method: 'POST',
    data
  })
}

// 素材库 上传文件
export const uploadFileApi = (data: any) => {
  return http.request({
    url: 'NewBucket/createOffice',
    method: 'POST',
    data
  })
}

/* 共享云经理 */
// 获取公司列表
export const getCompanyListApi = () => {
  return http.request({
    url: 'Bucket/department',
    method: 'GET',
    params: {
      unique_id: '0971a11acea011ea9d6bfa163ea50a57'
    }
  })
}

// 获取部门
export const getDepartmentListApi = (id: string) => {
  return http.request({
    url: 'Bucket/getDepartment',
    method: 'GET',
    params: {
      id
    }
  })
}

// 共享 提交
export const shareSubmitApi = (data: any) => {
  return http.request({
    url: 'BucketNew/department',
    method: 'POST',
    data
  })
}

// 共享云经理 获取素材
export const getShareFileApi = (params: any) => {
  return http.request({
    url: 'BucketNew/child_list',
    method: 'GET',
    params
  })
}

// 百度智能获取
export const getImgBaidu = (data: any) => {
  return http.request({
    url: 'bucket/picaip_class',
    method: 'POST',
    data
  })
}

// 获取站点 导航及素材分类
export const getSiteNavAndCateApi = (data: any) => {
  return http.request({
    url: 'bucket/picurl_class',
    method: 'POST',
    data
  })
}