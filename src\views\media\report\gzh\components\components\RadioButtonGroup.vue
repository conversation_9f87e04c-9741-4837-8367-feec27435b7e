<script setup lang="ts">
import {computed} from "vue";

interface Props {
  list: {
    label: string,
    value: string
  }[]
  value: string,
  type?: 'primary' | 'default'
}
const props = withDefaults(defineProps<Props>(), {
  list: () => [],
  value: '',
  type: 'default'
})
const emits = defineEmits(['update:value', 'changeData'])
const _value = computed({
  get() {
    return props.value
  },
  set(val) {
    emits('update:value', val)
  }
})
const changeData = (val: string) => {
  emits('changeData', val)
}
</script>

<template>
  <el-radio-group v-model="_value" size="large" @change="changeData">
    <el-radio-button v-for="(item, index) in list" :key="index" :label="item.label" :value="item.value" :class="`btn-${type}-item`" />
  </el-radio-group>
</template>

<style scoped lang="scss">
.btn-primary-item{
  margin-bottom: 10px;
  :deep(.el-radio-button__inner){
    background: #F2F4FA;
    min-width: 90px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    border-radius: 16px;
    font-size: 14px;
    font-family: PingFang;
    font-weight: 500;
    padding: 0 13px;
    box-sizing: border-box;
    transition: all ease-in-out 0.3s;
    cursor: pointer;
    border: none;
  }
  &.is-active, &:hover{
    :deep(.el-radio-button__inner){
      color: #fff;
      background: #1058FF linear-gradient(90deg, #1058FF, #5F8FFE);
    }
  }
  & +.btn-primary-item{
    margin-left: 10px;
  }
}
.btn-default-item{
  :deep(.el-radio-button__inner){
    background-color: #fff!important;
    min-width: 90px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    border-radius: 16px;
    font-size: 14px;
    font-family: PingFang;
    font-weight: 500;
    padding: 0 13px;
    box-sizing: border-box;
    transition: all ease-in-out 0.3s;
    cursor: pointer;
    border: 1px solid #D8D8D8!important;
    color: #666666!important;
  }
  &.is-active, &:hover{
    :deep(.el-radio-button__inner) {
      border: 1px solid #3583FB!important;
      color: #3583FB!important;
      box-shadow: none;
      background-color: #fff!important;
    }
  }
  & +.btn-default-item{
    margin-left: 10px;
  }
}
</style>
