<template>
  <div class="h100 flex-y">
    <page-menu v-if="navList.length" :list="navList" @click-item="navItemClick" :activeIndex="activeIndex" />
    <div class="flex-1">
      <component ref="currentComponentRef" :is="currentComponent" :cate-type="cateType" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import {computed, defineAsyncComponent, markRaw, ref} from "vue";

const navList = markRaw([
  { title: '站点总览', key: 'overview', component: defineAsyncComponent(() => import('@/views/site/list/components/Overview.vue')) },
  { title: '网站栏目管理', key: 'column', component: defineAsyncComponent(() => import('@/views/site/list/components/Column.vue')) },
  { title: '访问统计', key: 'statistics', component: defineAsyncComponent(() => import('@/views/site/list/components/Statistics.vue')) },
  { title: '上传SSL证书', key: 'certificate', component: defineAsyncComponent(() => import('@/views/site/list/components/Certificate.vue')) },
])

const activeIndex = ref(0)

const navItemClick = (item: any, index: number) => {
  activeIndex.value = index
}

const currentComponent = computed(() => {
  return navList[activeIndex.value].component
})

const cateType = computed(() => { return navList[activeIndex.value].key })
</script>

<style lang="scss" scoped>

</style>
