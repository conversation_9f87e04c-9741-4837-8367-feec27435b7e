<script setup lang="ts">
import TopTips from "@/components/TopTips.vue";
import {computed, ref, watch} from "vue";
import {getPageListApi, getSiteLayoutDomainApi, saveSeoInfoApi, setSeoInfoApi} from "@/api/siteManage";
import {ElMessage} from "element-plus";
import LimitWord from "@/components/LimitWord.vue";

const formRef: any = ref(null);

const form = ref({
  id: '',
  title_name: '',
  keywords: '',
  description: ''
})

const rules = ref({
  id: [
    { required: true, message: '请选择页面', trigger: 'change' },
  ]
})

const info = ref({
  layout_id: '',
  domain: '',
})
/**
 * 获取layout_id和domain
 */
const getLayoutIdAndDomain = async () => {
  const res: any = await getSiteLayoutDomainApi();
  info.value.layout_id = res.layout_id;
  info.value.domain = res.domain;
}
getLayoutIdAndDomain();

const loading = ref(false)

interface PageList {
  id: number;
  title: string;
}
const pageList = ref<PageList[]>([])
/**
 * 获取页面列表
 */
const getPageList = async (showLoading = true) => {
  showLoading && (loading.value = true)
  try{
    const res: any = await getPageListApi({layout_id: info.value.layout_id});
    if(form.value.id){
      const item: any = pageList.value.find((item: any) => item.id === form.value.id);
      form.value.id = item.id;
    }else{
      form.value.id = res[0].id;
    }
    pageList.value = res;
  }catch (e){
    console.log(e);
  }finally {
    showLoading && (loading.value = false)
  }
}
getPageList();

watch(() => form.value.id, () => {
  if (form.value.id) {
    const item: any = pageList.value.find((item: any) => +item.id === +form.value.id);
    form.value.title_name = item.title_name;
    form.value.keywords = item.keywords;
    form.value.description = item.description;
  }
}, {
  immediate: true
})

const indexedQueryUrl = computed(() => {
  return `https://www.baidu.com/baidu?wd=site%3A${info.value.domain}&tn=monline_4_dg&ie=utf-8`
})

/*极限词检测*/
const limitWordRef: any = ref(null);

/**
 * 保存
 * TODO: 需要在保存之前先检测极限词
 */
const saveType = ref<'single' | 'all'>('single')
const saveSeoInfo = async () => {
 const data: { [key: string]: any } = {
    layout_id: info.value.layout_id,
    id: form.value.id,
    title_name: form.value.title_name,
    keywords: form.value.keywords,
    description: form.value.description,
    content_type: 'site-seo'
  }
  loading.value = true
  try {
    if (saveType.value === 'all') {
      await setSeoInfoApi(data)
    } else {
      await saveSeoInfoApi(data)
    }
    ElMessage.success('保存成功');
    await getPageList(false);
  } catch (e) {
    console.log(e, 'e');
  } finally {
    loading.value = false
  }
}
const save = (type: 'single' | 'all' = 'single') => {
  if (!formRef.value) return
  formRef.value.validate(async (valid: boolean, fields: string) => {
    if (valid) {
      saveType.value = type;
      limitWordRef.value && await limitWordRef.value.checkLimit({...form.value, content_type: 'site-seo'});
    } else {
      console.log('error submit!', fields)
    }
  })
}
const continueSave = (formData: any) => {
  console.log(formData, 'form');
  const formValue = JSON.parse(JSON.stringify(formData));
  delete formValue.id;
  form.value = {...form.value, ...formValue};
  console.log(form.value, 'formValue');
  saveSeoInfo();
}
const showLimitTips = () => {
  console.log(111);
}
</script>

<template>
  <div class="wrap h100">
    <div class="box">
      <top-tips
          text="为提升网站点击率并实现最佳转化，页面标题和关键词应精确总结页面的内容，且每个页面都需要设置。设置个别栏目请选择保存按钮。如果想一次设置全部页面，请选择一键设置"/>
      <el-form ref="formRef" :model="form" label-width="180" class="w936" v-loading="loading" :rules="rules">
        <el-form-item label="页面选择：" prop="id">
          <div class="w100">
            <el-select v-model="form.id">
              <template #label="{ label }">
                <span v-html="label" />
              </template>
              <el-option
                  v-for="item in pageList"
                  :key="item.id"
                  :label="item.title"
                  :value="item.id">
                <span v-html="item.title" />
              </el-option>
            </el-select>
          </div>
        </el-form-item>
        <el-form-item label="标题（Title）：">
          <div class="w100">
            <el-input v-model="form.title_name" placeholder="请输入标题" />
          </div>
          <p class="tips2">*标题建议使用网站名称_地域+服务介绍/产品介绍，一般不超过80个字符。</p>
        </el-form-item>
        <el-form-item label="关键词（Keywords）：">
          <div class="w100">
            <el-input v-model="form.keywords" placeholder="请输入关键词" />
          </div>
          <p class="tips2">*关键词建议从地域+公司名称以及地域+网站业务这两方入手，一般控制在3-5个为合适，多个关键词间用逗号隔开，一般不超过100个字符。</p>
        </el-form-item>
        <el-form-item label="描述（Description）：">
          <div class="w100">
            <el-input type="textarea" v-model="form.description" placeholder="请输入描述" :autosize="{ minRows: 4, maxRows: 8 }" />
          </div>
          <p class="tips2">*描述建议使用公司介绍或者服务范围，好的描述可以让搜索引擎判断你的网页内容是否符合要求，一般不超过200个字符。</p>
        </el-form-item>
        <el-form-item label="提交网址给百度：">
          <el-button class="btn-default" tag="a" href="https://ziyuan.baidu.com/linksubmit/url" target="_blank" rel="noopener noreferrer">点击提交</el-button>
          <el-button class="btn-default" tag="a" :href="indexedQueryUrl" target="_blank" rel="noopener noreferrer">收录查询</el-button>
        </el-form-item>
        <el-form-item class="btn-wrap" label-width="0">
          <el-button type="primary" class="save-btn" @click="save">保存</el-button>
          <el-button type="primary" class="save-btn" @click="save('all')">一键设置</el-button>
        </el-form-item>
      </el-form>
      <limit-word ref="limitWordRef" @continue="continueSave" @limit="showLimitTips" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.wrap {
  padding: 10px 0;
  overflow: auto;
  .box {
    padding: 20px 30px;
    background: #fff;
    .tips2{
      font-size: 12px;
      font-weight: 500;
      color: #92939D;
      margin-top: 8px;
    }
    .w936{
      width: 936px;
    }
    .btn-default{
      width: 142px;
      height: 42px;
    }
    .btn-wrap{
      margin-top: 40px;
      :deep(.el-form-item__content) {
        justify-content: center;
        .save-btn{
          width: 142px;
          height: 42px;
        }
      }
    }
  }
}
</style>
