<!-- 头部 -->
<template>
  <div :class="['top-container', 'flex', {'is-iframe': isIframe}]">
    <div class="left-wrap flex-1 flex flex-align-center">
      <p class="left-title">网站编辑-{{ currentSite.web_name }}</p>
    </div>
    <div class="right-wrap flex">
      <!-- <jzt-button class="" name="其他内容编辑" @click="otherContent"></jzt-button> -->
      <jzt-button class=" ml10" name="访问网站" @click="previewWeb"></jzt-button>
      <jzt-button class="active ml10" name="发布网站" @click="reloadWeb"></jzt-button>
      <!-- <jzt-button class="ml10" name="返回代理商后台"></jzt-button> -->
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, computed } from 'vue'
import { useStore } from 'vuex'
import { ElMessage, ElLoading } from 'element-plus'
import { usePreview } from '@/views/website/hooks'
import { reloadWebApi } from '@/api/navigation'
import {ArrowLeft} from "@element-plus/icons-vue";

const store = useStore()
const { openPreview } = usePreview()

const currentSite = computed(() => store.getters.siteInfo)

// 重新发布
const reloadWeb = () => {
  let loadingInstance = ElLoading.service({
    lock: true,
    text: 'Loading',
    background: 'rgba(0, 0, 0, 0.7)'
  })
  reloadWebApi({site_id: currentSite.value.id}).then((res: any) => {
    loadingInstance.close()
    ElMessage.success('发布任务已提交，请等待15分钟左右刷新查看')
  }).finally(() => {
    loadingInstance.close()
  })
}

// 网站预览
const previewWeb = () => {
  let { yl_url } = currentSite.value
  if (!yl_url) return ElMessage.error('预览地址不存在')
  window.open(yl_url, '_blank')

  // 正式地址
  // let domain = currentSite.value.domain
  // if (domain) {
  //   if (!domain.startsWith('http')) domain = 'http://' + domain
  //   window.open(domain, '_blank')
  // }
}

// 其他内容管理
const otherContent = () => {
  // window.open(window.location.origin + '/newclient/Content/index', '_blank')
  window.open(window.location.origin + '/jzt_client/#/content/article', '_blank')
}

const isIframe = computed(() => {
  return window.self !== window.top
})

onMounted(() => {
  console.log(currentSite.value, 'currentSite')
})
</script>

<style lang='scss' scoped>
.top-container {
  height: 60px;
  background-color: #fff;
  box-shadow: 0px 6px 12px 0px rgba(222, 226, 232, 0.3);
  border-bottom: 1px solid #e6e8ec;
  padding: 0 30px;

  .left-wrap {
    .left-title {
      font-size: 18px;
      color: #000;
      font-weight: bold;
    }
  }

  &.is-iframe {
    padding-left: 70px;
  }
}
</style>
