<script setup lang="ts">
interface Props {
  text: string
}

const props = defineProps<Props>()
</script>

<template>
  <div class="tips">
    {{text}}
  </div>
</template>

<style lang="scss" scoped>
.tips {
  font-size: 12px;
  font-family: PingFang SC;
  font-weight: 500;
  color: #7F8294;
  margin-bottom: 20px;
  position: relative;
  padding-left: 10px;
  &::before {
    content: '';
    width: 4px;
    height: 4px;
    background: #7F8294;
    border-radius: 50%;
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    margin: auto;
  }
}
</style>