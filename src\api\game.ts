import http from '@/utils/request'

// 游戏列表
export const gameListApi = (params?: any) => {
  return http.request({
    url: '/Marketing/lst',
    method: 'get',
    params
  })
}

// 获取游戏体验状态
export const getGameStatusApi = (params?: any) => {
  return http.request({
    url: '/Marketing/ty_game',
    method: 'get',
    params
  })
}

// 申请游戏体验
export const applyGameApi = (data?: any) => {
  return http.request({
    url: '/Marketing/free_game',
    method: 'post',
    data
  })
}

// 获取套餐列表
export const getPackageListApi = (params?: any) => {
  return http.request({
    url: '/Marketing/gametc_list',
    method: 'get',
    params
  })
}

// 创建订单
export const createOrderApi = (data?: any) => {
  return http.request({
    url: '/Marketing/game_pay',
    method: 'post',
    data
  })
}

// 获取微信支付二维码
export const getWxPayQrApi = (data?: any, type?: string) => {
  if (type) {
    return http.request({
      url: 'https://jzt2.china9.cn/api/pay/' + type,
      method: 'post',
      data
    })
  } else {
    return null
  }
}

// 获取订单支付状态
export const getOrderStatusApi = (data?: any) => {
  return http.request({
    url: '/Marketing/orderInfo',
    method: 'post',
    data
  })
}

// 修改小游戏信息
export const updateGameInfoApi = (data?: any) => {
  return http.request({
    url: '/Marketing/edit_new',
    method: 'post',
    data
  })
}

// 下架小游戏
export const downGameApi = (data?: any) => {
  return http.request({
    url: '/Marketing/down',
    method: 'post',
    data
  })
}

// 上架小游戏
export const upGameApi = (data?: any) => {
  return http.request({
    url: '/Marketing/push',
    method: 'post',
    data
  })
}

// 获取游戏类型列表
export const getGameTypeApi = (params?: any) => {
  return http.request({
    url: '/Marketing/gamelst',
    method: 'get',
    params
  })
}

// 获取公司和域名信息
export const getCompanyInfoApi = (params?: any) => {
  return http.request({
    url: '/Marketing/getGameInfo',
    method: 'get',
    params
  })
}

// 新增小游戏
export const addGameApi = (data?: any) => {
  return http.request({
    url: '/Marketing/edit',
    method: 'post',
    data
  })
}

// 新增表单项
export const addItemApi = (data?: any) => {
  return http.request({
    url: '/Marketing/instfrom',
    method: 'post',
    data
  })
}

// 表单设置列表
export const getFormListApi = (params?: any) => {
  return http.request({
    url: '/Marketing/fromlst',
    method: 'get',
    params
  })
}

// 保存表单设置
export const saveFormApi = (data?: any) => {
  return http.request({
    url: '/Marketing/openfrom',
    method: 'post',
    data
  })
}

// 删除表单项
export const deleteItemApi = (data?: any) => {
  return http.request({
    url: '/Marketing/field_del',
    method: 'post',
    data
  })
}

// 表单数据列表
export const getFormDataListApi = (params?: any) => {
  return http.request({
    // TODO 把接口换回来
    url: '/Marketing/message_lst',
    // url: 'http://localhost:8888/jztImgAndTextList/gameFormData',
    method: 'get',
    params
  })
}

// 删除表单数据
export const deleteFormDataApi = (data?: any) => {
  return http.request({
    url: '/Marketing/lydel',
    method: 'post',
    data
  })
}

// 已读
export const readFormDataApi = (data?: any) => {
  return http.request({
    url: '/Marketing/lyread',
    method: 'post',
    data
  })
}

// 删除小游戏
export const deleteGameApi = (data?: any) => {
  return http.request({
    url: '/Marketing/del',
    method: 'post',
    data
  })
}
